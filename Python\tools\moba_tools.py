"""
MOBA Tools for Unreal MCP.

This module provides tools for creating and managing advanced MOBA objectives systems
in Unreal Engine, specifically designed for Auracron's 3D MOBA with multilayer towers,
inhibitors, neutral camps, and epic objectives.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_moba_tools(mcp: FastMCP):
    """Register MOBA tools with the MCP server."""
    
    @mcp.tool()
    def create_multilayer_tower_system(
        ctx: Context,
        tower_system_name: str,
        layer_configurations: Optional[List[Dict[str, Any]]] = None,
        tower_types: Optional[List[str]] = None,
        damage_scaling: Optional[Dict[str, float]] = None,
        range_modifiers: Optional[Dict[str, float]] = None,
        special_abilities: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Create multilayer tower system with layer-specific mechanics.
        
        Args:
            tower_system_name: Name of the tower system
            layer_configurations: Tower configs per layer
            tower_types: Different tower types (basic, advanced, nexus)
            damage_scaling: Damage scaling per layer
            range_modifiers: Attack range per layer
            special_abilities: Layer-specific tower abilities
        
        Returns:
            Dict containing success status and tower system details
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"tower_system_name": tower_system_name}
            
            if layer_configurations:
                params["layer_configurations"] = layer_configurations
            if tower_types:
                params["tower_types"] = tower_types
            if damage_scaling:
                params["damage_scaling"] = damage_scaling
            if range_modifiers:
                params["range_modifiers"] = range_modifiers
            if special_abilities:
                params["special_abilities"] = special_abilities
            
            logger.info(f"Creating multilayer tower system: {tower_system_name}")
            
            response = unreal.send_command("create_multilayer_tower_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer tower system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating multilayer tower system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_cascading_inhibitor_system(
        ctx: Context,
        inhibitor_system_name: str,
        inhibitor_positions: Optional[List[Dict[str, Any]]] = None,
        cascade_effects: Optional[Dict[str, Any]] = None,
        respawn_timers: Optional[Dict[str, float]] = None,
        layer_interactions: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Set up cascading inhibitor system affecting all layers.
        
        Args:
            inhibitor_system_name: Name of the inhibitor system
            inhibitor_positions: Positions for inhibitors per layer
            cascade_effects: Effects when inhibitors are destroyed
            respawn_timers: Respawn times per inhibitor type
            layer_interactions: How inhibitors affect other layers
        
        Returns:
            Dict containing success status and inhibitor system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"inhibitor_system_name": inhibitor_system_name}
            
            if inhibitor_positions:
                params["inhibitor_positions"] = inhibitor_positions
            if cascade_effects:
                params["cascade_effects"] = cascade_effects
            if respawn_timers:
                params["respawn_timers"] = respawn_timers
            if layer_interactions:
                params["layer_interactions"] = layer_interactions
            
            logger.info(f"Creating cascading inhibitor system: {inhibitor_system_name}")
            
            response = unreal.send_command("create_cascading_inhibitor_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Cascading inhibitor system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating cascading inhibitor system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_specialized_neutral_camps(
        ctx: Context,
        camp_system_name: str,
        camp_types: Optional[List[Dict[str, Any]]] = None,
        spawn_schedules: Optional[Dict[str, float]] = None,
        reward_systems: Optional[Dict[str, Any]] = None,
        difficulty_scaling: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """
        Create specialized neutral camps with layer-specific rewards.
        
        Args:
            camp_system_name: Name of the neutral camp system
            camp_types: Different camp types per layer
            spawn_schedules: Respawn timers and schedules
            reward_systems: Rewards and buffs per camp type
            difficulty_scaling: Camp difficulty per layer
        
        Returns:
            Dict containing success status and neutral camp system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"camp_system_name": camp_system_name}
            
            if camp_types:
                params["camp_types"] = camp_types
            if spawn_schedules:
                params["spawn_schedules"] = spawn_schedules
            if reward_systems:
                params["reward_systems"] = reward_systems
            if difficulty_scaling:
                params["difficulty_scaling"] = difficulty_scaling
            
            logger.info(f"Creating specialized neutral camps: {camp_system_name}")
            
            response = unreal.send_command("create_specialized_neutral_camps", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Specialized neutral camps creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating specialized neutral camps: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_epic_objectives(
        ctx: Context,
        epic_system_name: str,
        epic_objectives: Optional[List[Dict[str, Any]]] = None,
        spawn_conditions: Optional[Dict[str, Any]] = None,
        multilayer_effects: Optional[Dict[str, Any]] = None,
        team_benefits: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Set up epic objectives with multilayer impact.
        
        Args:
            epic_system_name: Name of the epic objectives system
            epic_objectives: Guardião da Aurora, Senhor dos Ventos, Arqui-Sombra
            spawn_conditions: Conditions for epic objective spawning
            multilayer_effects: Effects across all layers
            team_benefits: Benefits for controlling team
        
        Returns:
            Dict containing success status and epic objectives system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"epic_system_name": epic_system_name}
            
            if epic_objectives:
                params["epic_objectives"] = epic_objectives
            if spawn_conditions:
                params["spawn_conditions"] = spawn_conditions
            if multilayer_effects:
                params["multilayer_effects"] = multilayer_effects
            if team_benefits:
                params["team_benefits"] = team_benefits
            
            logger.info(f"Creating epic objectives: {epic_system_name}")
            
            response = unreal.send_command("create_epic_objectives", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Epic objectives creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating epic objectives: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_minion_spawning_system(
        ctx: Context,
        minion_system_name: str,
        spawn_points: Optional[List[Dict[str, Any]]] = None,
        minion_types: Optional[List[Dict[str, Any]]] = None,
        routing_paths: Optional[List[Dict[str, Any]]] = None,
        spawn_intervals: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """
        Create automated minion spawning and routing system.
        
        Args:
            minion_system_name: Name of the minion system
            spawn_points: Spawn locations per layer and team
            minion_types: Different minion types per layer
            routing_paths: Paths for minion movement
            spawn_intervals: Spawn timing per wave
        
        Returns:
            Dict containing success status and minion system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"minion_system_name": minion_system_name}
            
            if spawn_points:
                params["spawn_points"] = spawn_points
            if minion_types:
                params["minion_types"] = minion_types
            if routing_paths:
                params["routing_paths"] = routing_paths
            if spawn_intervals:
                params["spawn_intervals"] = spawn_intervals
            
            logger.info(f"Creating minion spawning system: {minion_system_name}")
            
            response = unreal.send_command("create_minion_spawning_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Minion spawning system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating minion spawning system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def create_dynamic_buff_system(
        ctx: Context,
        buff_system_name: str,
        buff_types: Optional[List[Dict[str, Any]]] = None,
        duration_settings: Optional[Dict[str, float]] = None,
        layer_modifiers: Optional[Dict[str, Any]] = None,
        team_effects: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Implement dynamic buff system per layer.

        Args:
            buff_system_name: Name of the buff system
            buff_types: Different buff types per layer
            duration_settings: Buff durations and stacking
            layer_modifiers: How buffs work per layer
            team_effects: Team-wide buff effects

        Returns:
            Dict containing success status and buff system results
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {"buff_system_name": buff_system_name}

            if buff_types:
                params["buff_types"] = buff_types
            if duration_settings:
                params["duration_settings"] = duration_settings
            if layer_modifiers:
                params["layer_modifiers"] = layer_modifiers
            if team_effects:
                params["team_effects"] = team_effects

            logger.info(f"Creating dynamic buff system: {buff_system_name}")

            response = unreal.send_command("create_dynamic_buff_system", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Dynamic buff system creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating dynamic buff system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def create_team_objective_control(
        ctx: Context,
        team_system_name: str,
        team_configurations: Optional[List[Dict[str, Any]]] = None,
        control_mechanics: Optional[Dict[str, Any]] = None,
        scoring_system: Optional[Dict[str, Any]] = None,
        layer_advantages: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Set up team-based objective control system.

        Args:
            team_system_name: Name of the team system
            team_configurations: Team setups and properties
            control_mechanics: How teams control objectives
            scoring_system: Points and victory conditions
            layer_advantages: Team advantages per layer

        Returns:
            Dict containing success status and team system results
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {"team_system_name": team_system_name}

            if team_configurations:
                params["team_configurations"] = team_configurations
            if control_mechanics:
                params["control_mechanics"] = control_mechanics
            if scoring_system:
                params["scoring_system"] = scoring_system
            if layer_advantages:
                params["layer_advantages"] = layer_advantages

            logger.info(f"Creating team objective control: {team_system_name}")

            response = unreal.send_command("create_team_objective_control", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Team objective control creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating team objective control: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

"""
Material Tools for Unreal MCP.

This module provides tools for creating and manipulating procedural materials in Unreal Engine.
Specifically designed for Auracron's themed materials with layer-specific properties.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_material_tools(mcp: FastMCP):
    """Register Material tools with the MCP server."""
    
    @mcp.tool()
    def create_layer_materials(
        ctx: Context,
        material_name: str,
        layer_index: int,
        material_type: str,
        material_properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create layer-specific materials using modern UE 5.6.1 APIs.
        
        Args:
            material_name: Name of the material to create
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            material_type: Type of material (terrain, structure, vegetation)
            material_properties: Material properties:
                - base_color: Base color of the material
                - metallic: Metallic value (0.0-1.0)
                - roughness: Roughness value (0.0-1.0)
                - normal_intensity: Normal map intensity
                - emissive_color: Emissive color
                - opacity: Opacity value (0.0-1.0)
        
        Returns:
            Dict containing success status and material creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "material_name": material_name,
                "layer_index": layer_index,
                "material_type": material_type
            }
            
            if material_properties:
                params["material_properties"] = material_properties
            
            logger.info(f"Creating layer material: {material_name} for layer {layer_index}")
            
            response = unreal.send_command("create_layer_materials", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Layer material creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating layer material: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_themed_textures(
        ctx: Context,
        texture_name: str,
        texture_type: str,
        layer_theme: str,
        texture_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create themed textures for different layers and structures.
        
        Args:
            texture_name: Name of the texture to create
            texture_type: Type of texture (diffuse, normal, roughness, metallic)
            layer_theme: Theme based on layer (golden, ethereal, shadow)
            texture_settings: Texture generation settings:
                - resolution: Texture resolution (512, 1024, 2048, 4096)
                - pattern_type: Pattern type (noise, geometric, organic)
                - color_palette: Color palette for the theme
                - detail_level: Level of detail in the texture
        
        Returns:
            Dict containing success status and texture creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "texture_name": texture_name,
                "texture_type": texture_type,
                "layer_theme": layer_theme
            }
            
            if texture_settings:
                params["texture_settings"] = texture_settings
            
            logger.info(f"Creating themed texture: {texture_name} ({texture_type}) with {layer_theme} theme")
            
            response = unreal.send_command("create_themed_textures", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Themed texture creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating themed texture: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_dynamic_materials(
        ctx: Context,
        material_name: str,
        base_material: str,
        dynamic_properties: List[Dict[str, Any]],
        animation_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create dynamic materials with animated properties.
        
        Args:
            material_name: Name of the dynamic material
            base_material: Base material to modify
            dynamic_properties: List of dynamic properties:
                - property_name: Name of the property to animate
                - animation_type: Type of animation (sine, cosine, linear)
                - speed: Animation speed
                - amplitude: Animation amplitude
            animation_settings: Animation settings:
                - loop_duration: Duration of animation loop
                - phase_offset: Phase offset for multiple materials
                - trigger_events: Events that trigger animations
        
        Returns:
            Dict containing success status and dynamic material creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "material_name": material_name,
                "base_material": base_material,
                "dynamic_properties": dynamic_properties
            }
            
            if animation_settings:
                params["animation_settings"] = animation_settings
            
            logger.info(f"Creating dynamic material: {material_name} based on {base_material}")
            
            response = unreal.send_command("create_dynamic_materials", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Dynamic material creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating dynamic material: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_material_functions(
        ctx: Context,
        function_name: str,
        function_type: str,
        input_parameters: List[Dict[str, Any]],
        function_logic: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create reusable material functions for complex effects.
        
        Args:
            function_name: Name of the material function
            function_type: Type of function (noise, blending, effects)
            input_parameters: List of input parameters:
                - parameter_name: Name of the parameter
                - parameter_type: Type of parameter (float, vector, texture)
                - default_value: Default value for the parameter
            function_logic: Logic definition for the function:
                - nodes: List of material nodes to create
                - connections: Connections between nodes
                - output_type: Type of output (float, vector3, vector4)
        
        Returns:
            Dict containing success status and material function creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "function_name": function_name,
                "function_type": function_type,
                "input_parameters": input_parameters,
                "function_logic": function_logic
            }
            
            logger.info(f"Creating material function: {function_name} ({function_type})")
            
            response = unreal.send_command("create_material_functions", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Material function creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating material function: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_material_parameters(
        ctx: Context,
        material_name: str,
        parameter_collection: str,
        global_parameters: List[Dict[str, Any]],
        layer_overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Setup material parameter collections for global control.
        
        Args:
            material_name: Name of the target material
            parameter_collection: Name of the parameter collection
            global_parameters: List of global parameters:
                - parameter_name: Name of the parameter
                - parameter_type: Type of parameter (scalar, vector)
                - default_value: Default value
                - description: Description of the parameter
            layer_overrides: Layer-specific parameter overrides:
                - layer_index: Layer to override
                - parameter_overrides: Parameters to override for this layer
        
        Returns:
            Dict containing success status and parameter setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "material_name": material_name,
                "parameter_collection": parameter_collection,
                "global_parameters": global_parameters
            }
            
            if layer_overrides:
                params["layer_overrides"] = layer_overrides
            
            logger.info(f"Setting up material parameters for: {material_name}")
            
            response = unreal.send_command("setup_material_parameters", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Material parameter setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up material parameters: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\module.auracron.gen.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\auracron.init.gen.cpp", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\worldpartitionfix.gen.cpp", "c:\\game\\auracron\\source\\auracron\\public\\worldpartitionfix.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\worldpartitionfix.generated.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\worldpartitionfixsubsystem.gen.cpp", "c:\\game\\auracron\\source\\auracron\\public\\worldpartitionfixsubsystem.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\worldpartitionfixsubsystem.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
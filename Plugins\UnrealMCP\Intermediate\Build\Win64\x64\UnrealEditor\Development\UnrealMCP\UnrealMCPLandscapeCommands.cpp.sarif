{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 66, "startColumn": 34, "snippet": {"text": "\tFScratchRenderTargetScope(const FScratchRenderTargetParams& InParams);"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 130, "startColumn": 2, "snippet": {"text": "\tconst UE::Landscape::FScratchRenderTargetParams& GetCurrentRenderTargetParams() { return CurrentRenderTargetParams; }"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 231, "startColumn": 45, "snippet": {"text": "\tbool IsCompatibleWith(const UE::Landscape::FScratchRenderTargetParams& InParams) const;"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 231, "startColumn": 24, "snippet": {"text": "\tbool IsCompatibleWith(const UE::Landscape::FScratchRenderTargetParams& InParams) const;"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 237, "startColumn": 40, "snippet": {"text": "\tvoid OnRequested(const UE::Landscape::FScratchRenderTargetParams& InParams);"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 237, "startColumn": 19, "snippet": {"text": "\tvoid OnRequested(const UE::Landscape::FScratchRenderTargetParams& InParams);"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 254, "startColumn": 17, "snippet": {"text": "\tUE::Landscape::FScratchRenderTargetParams CurrentRenderTargetParams;"}}}}]}, {"ruleId": "C4996", "message": {"text": "'ULandscapeScratchRenderTarget::TryGetRenderTarget2D': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 122, "startColumn": 37, "snippet": {"text": "\tbool IsTexture2D() const { return (TryGetRenderTarget2D() != nullptr); }"}}}}]}, {"ruleId": "C4996", "message": {"text": "'ULandscapeScratchRenderTarget::TryGetRenderTarget2DArray': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 124, "startColumn": 42, "snippet": {"text": "\tbool IsTexture2DArray() const { return (TryGetRenderTarget2DArray() != nullptr); }"}}}}]}, {"ruleId": "C4996", "message": {"text": "'ULandscapeScratchRenderTarget::GetEffectiveResolution': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 189, "startColumn": 44, "snippet": {"text": "\t\t\tCopySize = InSourceScratchRenderTarget->GetEffectiveResolution();"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 275, "startColumn": 81, "snippet": {"text": "\tULandscapeScratchRenderTarget* RequestScratchRenderTarget(const UE::Landscape::FScratchRenderTargetParams& InParams);"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UE::Landscape::FScratchRenderTargetParams': Landscape edit layers batched merge is still being actively developed, this API may change. - This API is experimental. As such, there is no guarantee that it won't change or be removed in future releases. Please use at your own risk."}, "analysisTarget": {"uri": "file:///C:/Game/AURACRON/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPLandscapeCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Program Files/Epic Games/UE_5.6/Engine/Source/Runtime/Landscape/Public/LandscapeEditResourcesSubsystem.h"}, "region": {"startLine": 275, "startColumn": 60, "snippet": {"text": "\tULandscapeScratchRenderTarget* RequestScratchRenderTarget(const UE::Landscape::FScratchRenderTargetParams& InParams);"}}}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}
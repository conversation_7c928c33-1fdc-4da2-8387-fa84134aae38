// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPLandscapeCommands.h"

#ifdef UNREALMCP_UnrealMCPLandscapeCommands_generated_h
#error "UnrealMCPLandscapeCommands.generated.h already included, missing '#pragma once' in UnrealMCPLandscapeCommands.h"
#endif
#define UNREALMCP_UnrealMCPLandscapeCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronLayerLandscapeConfig *************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_68_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerLandscapeConfig;
// ********** End ScriptStruct FAuracronLayerLandscapeConfig ***************************************

// ********** Begin ScriptStruct FProceduralLandscapeParams ****************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_116_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FProceduralLandscapeParams;
// ********** End ScriptStruct FProceduralLandscapeParams ******************************************

// ********** Begin Class UUnrealMCPLandscapeCommands **********************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_157_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPLandscapeCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPLandscapeCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPLandscapeCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_157_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPLandscapeCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPLandscapeCommands(UUnrealMCPLandscapeCommands&&) = delete; \
	UUnrealMCPLandscapeCommands(const UUnrealMCPLandscapeCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPLandscapeCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPLandscapeCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPLandscapeCommands) \
	NO_API virtual ~UUnrealMCPLandscapeCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_154_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_157_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_157_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h_157_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPLandscapeCommands;

// ********** End Class UUnrealMCPLandscapeCommands ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

"""
Pathfinding Tools for Unreal MCP.

This module provides tools for creating and managing advanced multilayer pathfinding systems
in Unreal Engine, specifically designed for Auracron's 3D MOBA with vertical navigation.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_pathfinding_tools(mcp: FastMCP):
    """Register Pathfinding tools with the MCP server."""
    
    @mcp.tool()
    def create_multilayer_pathfinding(
        ctx: Context,
        system_name: str,
        layer_count: int = 3,
        layer_heights: Optional[List[float]] = None,
        transition_costs: Optional[Dict[str, float]] = None,
        heuristic_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create advanced multilayer pathfinding system with custom A* algorithm.
        
        Args:
            system_name: Name of the pathfinding system
            layer_count: Number of vertical layers (default: 3 for Auracron)
            layer_heights: Z-coordinates for each layer [Planície, Firmamento, Abismo]
            transition_costs: Cost modifiers for vertical transitions
            heuristic_settings: A* heuristic configuration
        
        Returns:
            Dict containing success status and pathfinding system details
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            # Prepare parameters
            params = {
                "system_name": system_name,
                "layer_count": layer_count
            }
            
            if layer_heights:
                params["layer_heights"] = layer_heights
            if transition_costs:
                params["transition_costs"] = transition_costs
            if heuristic_settings:
                params["heuristic_settings"] = heuristic_settings
            
            logger.info(f"Creating multilayer pathfinding system: {system_name} with {layer_count} layers")
            
            response = unreal.send_command("create_multilayer_pathfinding", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer pathfinding system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating multilayer pathfinding system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_custom_query_filter(
        ctx: Context,
        filter_name: str,
        target_layer: str,
        area_costs: Optional[Dict[str, float]] = None,
        heuristic_scale: float = 1.0,
        backtracking_enabled: bool = False
    ) -> Dict[str, Any]:
        """
        Configure custom query filters for layer-specific pathfinding.
        
        Args:
            filter_name: Name of the custom filter
            target_layer: Layer this filter applies to
            area_costs: Cost modifiers per area type
            heuristic_scale: A* heuristic multiplier
            backtracking_enabled: Allow reverse pathfinding
        
        Returns:
            Dict containing success status and filter configuration results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "filter_name": filter_name,
                "target_layer": target_layer,
                "heuristic_scale": heuristic_scale,
                "backtracking_enabled": backtracking_enabled
            }
            
            if area_costs:
                params["area_costs"] = area_costs
            
            logger.info(f"Creating custom query filter: {filter_name} for layer {target_layer}")
            
            response = unreal.send_command("create_custom_query_filter", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Custom query filter creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating custom query filter: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_vertical_transition_costs(
        ctx: Context,
        transition_type: str,
        base_cost: float = 1.0,
        time_penalty: float = 0.0,
        interruption_risk: float = 0.0,
        layer_distance_multiplier: float = 1.0
    ) -> Dict[str, Any]:
        """
        Set up vertical transition costs for portals, elevators, and bridges.
        
        Args:
            transition_type: "portal", "elevator", "bridge"
            base_cost: Base cost for using transition
            time_penalty: Additional cost based on transition time
            interruption_risk: Cost modifier for vulnerability
            layer_distance_multiplier: Cost per layer traversed
        
        Returns:
            Dict containing success status and transition cost setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "transition_type": transition_type,
                "base_cost": base_cost,
                "time_penalty": time_penalty,
                "interruption_risk": interruption_risk,
                "layer_distance_multiplier": layer_distance_multiplier
            }
            
            logger.info(f"Setting up vertical transition costs for: {transition_type}")
            
            response = unreal.send_command("setup_vertical_transition_costs", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Vertical transition costs setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up vertical transition costs: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_mobile_navigation_assistance(
        ctx: Context,
        assistance_level: str,
        auto_pathfinding: bool = True,
        visual_indicators: bool = True,
        route_suggestions: bool = True,
        simplified_controls: bool = True
    ) -> Dict[str, Any]:
        """
        Create mobile-optimized navigation assistance system.
        
        Args:
            assistance_level: "basic", "advanced", "full"
            auto_pathfinding: Enable automatic path calculation
            visual_indicators: Show path visualization
            route_suggestions: AI-powered route optimization
            simplified_controls: Touch-friendly navigation
        
        Returns:
            Dict containing success status and mobile assistance setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "assistance_level": assistance_level,
                "auto_pathfinding": auto_pathfinding,
                "visual_indicators": visual_indicators,
                "route_suggestions": route_suggestions,
                "simplified_controls": simplified_controls
            }
            
            logger.info(f"Creating mobile navigation assistance: {assistance_level} level")
            
            response = unreal.send_command("create_mobile_navigation_assistance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Mobile navigation assistance creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating mobile navigation assistance: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def optimize_pathfinding_performance(
        ctx: Context,
        performance_level: str = "medium",
        max_search_nodes: Optional[int] = None,
        hierarchical_enabled: Optional[bool] = None,
        cache_size: Optional[int] = None,
        update_frequency: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Optimize pathfinding performance for mobile devices.
        
        Args:
            performance_level: "low", "medium", "high"
            max_search_nodes: Limit A* node exploration
            hierarchical_enabled: Use hierarchical pathfinding
            cache_size: Path cache size for reuse
            update_frequency: Path recalculation frequency
        
        Returns:
            Dict containing success status and performance optimization results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"performance_level": performance_level}
            
            if max_search_nodes is not None:
                params["max_search_nodes"] = max_search_nodes
            if hierarchical_enabled is not None:
                params["hierarchical_enabled"] = hierarchical_enabled
            if cache_size is not None:
                params["cache_size"] = cache_size
            if update_frequency is not None:
                params["update_frequency"] = update_frequency
            
            logger.info(f"Optimizing pathfinding performance: {performance_level} level")
            
            response = unreal.send_command("optimize_pathfinding_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Pathfinding performance optimization response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error optimizing pathfinding performance: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def create_custom_astar_algorithm(
        ctx: Context,
        algorithm_name: str,
        layer_weights: Optional[List[float]] = None,
        vertical_preference: float = 1.2,
        emergency_exit_costs: Optional[Dict[str, float]] = None,
        dynamic_cost_updates: bool = False
    ) -> Dict[str, Any]:
        """
        Create custom A* algorithm implementation for multilayer scenarios.

        Args:
            algorithm_name: Name of the custom algorithm
            layer_weights: Weight multipliers per layer
            vertical_preference: Preference for staying in same layer
            emergency_exit_costs: Costs for emergency transitions
            dynamic_cost_updates: Enable real-time cost updates

        Returns:
            Dict containing success status and custom algorithm creation results
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "algorithm_name": algorithm_name,
                "vertical_preference": vertical_preference,
                "dynamic_cost_updates": dynamic_cost_updates
            }

            if layer_weights:
                params["layer_weights"] = layer_weights
            if emergency_exit_costs:
                params["emergency_exit_costs"] = emergency_exit_costs

            logger.info(f"Creating custom A* algorithm: {algorithm_name}")

            response = unreal.send_command("create_custom_astar_algorithm", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Custom A* algorithm creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating custom A* algorithm: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def setup_pathfinding_debug_tools(
        ctx: Context,
        debug_level: str = "basic",
        visual_debug: bool = True,
        cost_display: str = "total",
        step_by_step: bool = False,
        performance_metrics: bool = False
    ) -> Dict[str, Any]:
        """
        Set up pathfinding debugging and visualization tools.

        Args:
            debug_level: "basic", "detailed", "full"
            visual_debug: Enable visual debugging
            cost_display: "total", "heuristic", "real"
            step_by_step: Enable step-by-step A* visualization
            performance_metrics: Show performance data

        Returns:
            Dict containing success status and debugging setup results
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "debug_level": debug_level,
                "visual_debug": visual_debug,
                "cost_display": cost_display,
                "step_by_step": step_by_step,
                "performance_metrics": performance_metrics
            }

            logger.info(f"Setting up pathfinding debug tools: {debug_level} level")

            response = unreal.send_command("setup_pathfinding_debug_tools", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Pathfinding debug tools setup response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error setting up pathfinding debug tools: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

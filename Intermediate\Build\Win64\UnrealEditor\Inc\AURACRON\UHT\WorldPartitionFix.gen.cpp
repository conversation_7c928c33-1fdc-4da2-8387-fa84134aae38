// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "WorldPartitionFix.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeWorldPartitionFix() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFix();
AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFix_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UWorld_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartition_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UWorldPartitionFix Function ForceResetWorldPartitionState ****************
struct Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics
{
	struct WorldPartitionFix_eventForceResetWorldPartitionState_Parms
	{
		UWorldPartition* WorldPartition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * For\xc3\xa7""a o reset do estado do World Partition para um estado v\xc3\xa1lido\n     * @param WorldPartition - O World Partition a ser resetado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a o reset do estado do World Partition para um estado v\xc3\xa1lido\n@param WorldPartition - O World Partition a ser resetado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WorldPartitionFix_eventForceResetWorldPartitionState_Parms, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::NewProp_WorldPartition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "ForceResetWorldPartitionState", Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::WorldPartitionFix_eventForceResetWorldPartitionState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::WorldPartitionFix_eventForceResetWorldPartitionState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execForceResetWorldPartitionState)
{
	P_GET_OBJECT(UWorldPartition,Z_Param_WorldPartition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceResetWorldPartitionState(Z_Param_WorldPartition);
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function ForceResetWorldPartitionState ******************

// ********** Begin Class UWorldPartitionFix Function IsWorldPartitionInValidState *****************
struct Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics
{
	struct WorldPartitionFix_eventIsWorldPartitionInValidState_Parms
	{
		UWorldPartition* WorldPartition;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se o World Partition est\xc3\xa1 em um estado v\xc3\xa1lido\n     * @param WorldPartition - O World Partition a ser verificado\n     * @return true se o estado for v\xc3\xa1lido, false caso contr\xc3\xa1rio\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se o World Partition est\xc3\xa1 em um estado v\xc3\xa1lido\n@param WorldPartition - O World Partition a ser verificado\n@return true se o estado for v\xc3\xa1lido, false caso contr\xc3\xa1rio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WorldPartitionFix_eventIsWorldPartitionInValidState_Parms, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WorldPartitionFix_eventIsWorldPartitionInValidState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WorldPartitionFix_eventIsWorldPartitionInValidState_Parms), &Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_WorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "IsWorldPartitionInValidState", Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::WorldPartitionFix_eventIsWorldPartitionInValidState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::WorldPartitionFix_eventIsWorldPartitionInValidState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execIsWorldPartitionInValidState)
{
	P_GET_OBJECT(UWorldPartition,Z_Param_WorldPartition);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWorldPartitionInValidState(Z_Param_WorldPartition);
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function IsWorldPartitionInValidState *******************

// ********** Begin Class UWorldPartitionFix Function OnWorldCleanup *******************************
struct Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics
{
	struct WorldPartitionFix_eventOnWorldCleanup_Parms
	{
		UWorld* World;
		bool bSessionEnded;
		bool bCleanupResources;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Callback chamado durante a limpeza do mundo\n     * Garante que o World Partition seja limpo adequadamente\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback chamado durante a limpeza do mundo\nGarante que o World Partition seja limpo adequadamente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_World;
	static void NewProp_bSessionEnded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSessionEnded;
	static void NewProp_bCleanupResources_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCleanupResources;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_World = { "World", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WorldPartitionFix_eventOnWorldCleanup_Parms, World), Z_Construct_UClass_UWorld_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bSessionEnded_SetBit(void* Obj)
{
	((WorldPartitionFix_eventOnWorldCleanup_Parms*)Obj)->bSessionEnded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bSessionEnded = { "bSessionEnded", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WorldPartitionFix_eventOnWorldCleanup_Parms), &Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bSessionEnded_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bCleanupResources_SetBit(void* Obj)
{
	((WorldPartitionFix_eventOnWorldCleanup_Parms*)Obj)->bCleanupResources = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bCleanupResources = { "bCleanupResources", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WorldPartitionFix_eventOnWorldCleanup_Parms), &Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bCleanupResources_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_World,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bSessionEnded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::NewProp_bCleanupResources,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "OnWorldCleanup", Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::WorldPartitionFix_eventOnWorldCleanup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::WorldPartitionFix_eventOnWorldCleanup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execOnWorldCleanup)
{
	P_GET_OBJECT(UWorld,Z_Param_World);
	P_GET_UBOOL(Z_Param_bSessionEnded);
	P_GET_UBOOL(Z_Param_bCleanupResources);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnWorldCleanup(Z_Param_World,Z_Param_bSessionEnded,Z_Param_bCleanupResources);
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function OnWorldCleanup *********************************

// ********** Begin Class UWorldPartitionFix Function SafeUninitializeWorldPartition ***************
struct Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics
{
	struct WorldPartitionFix_eventSafeUninitializeWorldPartition_Parms
	{
		UWorldPartition* WorldPartition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Realiza uninitialize seguro do World Partition\n     * @param WorldPartition - O World Partition a ser uninitializado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Realiza uninitialize seguro do World Partition\n@param WorldPartition - O World Partition a ser uninitializado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WorldPartitionFix_eventSafeUninitializeWorldPartition_Parms, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::NewProp_WorldPartition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "SafeUninitializeWorldPartition", Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::WorldPartitionFix_eventSafeUninitializeWorldPartition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::WorldPartitionFix_eventSafeUninitializeWorldPartition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execSafeUninitializeWorldPartition)
{
	P_GET_OBJECT(UWorldPartition,Z_Param_WorldPartition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SafeUninitializeWorldPartition(Z_Param_WorldPartition);
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function SafeUninitializeWorldPartition *****************

// ********** Begin Class UWorldPartitionFix Function ValidateWorldPartitionInitialization *********
struct Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics
{
	struct WorldPartitionFix_eventValidateWorldPartitionInitialization_Parms
	{
		UWorldPartition* WorldPartition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Valida se a inicializa\xc3\xa7\xc3\xa3o do World Partition foi bem-sucedida\n     * @param WorldPartition - O World Partition a ser validado\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valida se a inicializa\xc3\xa7\xc3\xa3o do World Partition foi bem-sucedida\n@param WorldPartition - O World Partition a ser validado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WorldPartitionFix_eventValidateWorldPartitionInitialization_Parms, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::NewProp_WorldPartition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "ValidateWorldPartitionInitialization", Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::WorldPartitionFix_eventValidateWorldPartitionInitialization_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::WorldPartitionFix_eventValidateWorldPartitionInitialization_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execValidateWorldPartitionInitialization)
{
	P_GET_OBJECT(UWorldPartition,Z_Param_WorldPartition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateWorldPartitionInitialization(Z_Param_WorldPartition);
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function ValidateWorldPartitionInitialization ***********

// ********** Begin Class UWorldPartitionFix Function VerifyAndFixWorldPartitionState **************
struct Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica e corrige o estado de todos os World Partitions ativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica e corrige o estado de todos os World Partitions ativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFix, nullptr, "VerifyAndFixWorldPartitionState", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFix::execVerifyAndFixWorldPartitionState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->VerifyAndFixWorldPartitionState();
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFix Function VerifyAndFixWorldPartitionState ****************

// ********** Begin Class UWorldPartitionFix *******************************************************
void UWorldPartitionFix::StaticRegisterNativesUWorldPartitionFix()
{
	UClass* Class = UWorldPartitionFix::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ForceResetWorldPartitionState", &UWorldPartitionFix::execForceResetWorldPartitionState },
		{ "IsWorldPartitionInValidState", &UWorldPartitionFix::execIsWorldPartitionInValidState },
		{ "OnWorldCleanup", &UWorldPartitionFix::execOnWorldCleanup },
		{ "SafeUninitializeWorldPartition", &UWorldPartitionFix::execSafeUninitializeWorldPartition },
		{ "ValidateWorldPartitionInitialization", &UWorldPartitionFix::execValidateWorldPartitionInitialization },
		{ "VerifyAndFixWorldPartitionState", &UWorldPartitionFix::execVerifyAndFixWorldPartitionState },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UWorldPartitionFix;
UClass* UWorldPartitionFix::GetPrivateStaticClass()
{
	using TClass = UWorldPartitionFix;
	if (!Z_Registration_Info_UClass_UWorldPartitionFix.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("WorldPartitionFix"),
			Z_Registration_Info_UClass_UWorldPartitionFix.InnerSingleton,
			StaticRegisterNativesUWorldPartitionFix,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UWorldPartitionFix.InnerSingleton;
}
UClass* Z_Construct_UClass_UWorldPartitionFix_NoRegister()
{
	return UWorldPartitionFix::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UWorldPartitionFix_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "ClassGroupNames", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Componente respons\xc3\xa1vel por corrigir problemas de inicializa\xc3\xa7\xc3\xa3o do World Partition\n * Implementa verifica\xc3\xa7\xc3\xb5""es de estado e corre\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas para evitar o erro:\n * \"Assertion failed: InitState == EWorldPartitionInitState::Uninitialized\"\n */" },
#endif
		{ "IncludePath", "WorldPartitionFix.h" },
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente respons\xc3\xa1vel por corrigir problemas de inicializa\xc3\xa7\xc3\xa3o do World Partition\nImplementa verifica\xc3\xa7\xc3\xb5""es de estado e corre\xc3\xa7\xc3\xb5""es autom\xc3\xa1ticas para evitar o erro:\n\"Assertion failed: InitState == EWorldPartitionInitState::Uninitialized\"" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDetailedLogging_MetaData[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Propriedade para habilitar/desabilitar logs detalhados\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedade para habilitar/desabilitar logs detalhados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAutomaticFix_MetaData[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Propriedade para habilitar/desabilitar corre\xc3\xa7\xc3\xa3o autom\xc3\xa1tica\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedade para habilitar/desabilitar corre\xc3\xa7\xc3\xa3o autom\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerificationInterval_MetaData[] = {
		{ "Category", "World Partition Fix" },
		{ "ClampMax", "10.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Propriedade para definir o intervalo de verifica\xc3\xa7\xc3\xa3o (em segundos)\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFix.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Propriedade para definir o intervalo de verifica\xc3\xa7\xc3\xa3o (em segundos)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableDetailedLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDetailedLogging;
	static void NewProp_bEnableAutomaticFix_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAutomaticFix;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VerificationInterval;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UWorldPartitionFix_ForceResetWorldPartitionState, "ForceResetWorldPartitionState" }, // 1478124563
		{ &Z_Construct_UFunction_UWorldPartitionFix_IsWorldPartitionInValidState, "IsWorldPartitionInValidState" }, // 1537908042
		{ &Z_Construct_UFunction_UWorldPartitionFix_OnWorldCleanup, "OnWorldCleanup" }, // 1111204908
		{ &Z_Construct_UFunction_UWorldPartitionFix_SafeUninitializeWorldPartition, "SafeUninitializeWorldPartition" }, // 1047014767
		{ &Z_Construct_UFunction_UWorldPartitionFix_ValidateWorldPartitionInitialization, "ValidateWorldPartitionInitialization" }, // 1143359726
		{ &Z_Construct_UFunction_UWorldPartitionFix_VerifyAndFixWorldPartitionState, "VerifyAndFixWorldPartitionState" }, // 1661480359
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UWorldPartitionFix>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableDetailedLogging_SetBit(void* Obj)
{
	((UWorldPartitionFix*)Obj)->bEnableDetailedLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableDetailedLogging = { "bEnableDetailedLogging", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWorldPartitionFix), &Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableDetailedLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDetailedLogging_MetaData), NewProp_bEnableDetailedLogging_MetaData) };
void Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableAutomaticFix_SetBit(void* Obj)
{
	((UWorldPartitionFix*)Obj)->bEnableAutomaticFix = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableAutomaticFix = { "bEnableAutomaticFix", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWorldPartitionFix), &Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableAutomaticFix_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAutomaticFix_MetaData), NewProp_bEnableAutomaticFix_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_VerificationInterval = { "VerificationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWorldPartitionFix, VerificationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerificationInterval_MetaData), NewProp_VerificationInterval_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UWorldPartitionFix_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableDetailedLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_bEnableAutomaticFix,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWorldPartitionFix_Statics::NewProp_VerificationInterval,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFix_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UWorldPartitionFix_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFix_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UWorldPartitionFix_Statics::ClassParams = {
	&UWorldPartitionFix::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UWorldPartitionFix_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFix_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFix_Statics::Class_MetaDataParams), Z_Construct_UClass_UWorldPartitionFix_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UWorldPartitionFix()
{
	if (!Z_Registration_Info_UClass_UWorldPartitionFix.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UWorldPartitionFix.OuterSingleton, Z_Construct_UClass_UWorldPartitionFix_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UWorldPartitionFix.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UWorldPartitionFix);
UWorldPartitionFix::~UWorldPartitionFix() {}
// ********** End Class UWorldPartitionFix *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UWorldPartitionFix, UWorldPartitionFix::StaticClass, TEXT("UWorldPartitionFix"), &Z_Registration_Info_UClass_UWorldPartitionFix, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UWorldPartitionFix), 4132491084U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h__Script_AURACRON_558719137(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#include "Commands/UnrealMCPProceduralMeshCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes - VERIFIED EXISTING HEADERS
#include "DynamicMesh/MeshNormals.h"
#include "Generators/MeshShapeGenerator.h"
#include "Generators/GridBoxMeshGenerator.h"
#include "Generators/MinimalBoxMeshGenerator.h"
#include "Generators/CapsuleGenerator.h"
#include "Generators/BoxSphereGenerator.h"
#include "Generators/SphereGenerator.h"
#include "Generators/RectangleMeshGenerator.h"
#include "Generators/DiscMeshGenerator.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Modern UE 5.6.1 Static Mesh creation includes
#include "Engine/StaticMesh.h"
#include "StaticMeshDescription.h"
#include "MeshDescription.h"
#include "StaticMeshAttributes.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/Material.h"
#include "Materials/MaterialInterface.h"

// Modern UE 5.6.1 Mesh conversion includes
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/MeshIndexMappings.h"
#include "DynamicMeshEditor.h"
#include "DynamicMesh/MeshTransforms.h"
#include "DynamicMeshToMeshDescription.h"

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPProceduralMeshCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_lane_geometry"))
    {
        return HandleCreateLaneGeometry(Params);
    }
    else if (CommandName == TEXT("create_jungle_structures"))
    {
        return HandleCreateJungleStructures(Params);
    }
    else if (CommandName == TEXT("create_tower_meshes"))
    {
        return HandleCreateTowerMeshes(Params);
    }
    else if (CommandName == TEXT("create_base_architecture"))
    {
        return HandleCreateBaseArchitecture(Params);
    }
    else if (CommandName == TEXT("create_portal_geometry"))
    {
        return HandleCreatePortalGeometry(Params);
    }
    else if (CommandName == TEXT("create_bridge_meshes"))
    {
        return HandleCreateBridgeMeshes(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown procedural mesh command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateLaneGeometry(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("lane_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: lane_name"));
    }

    FString LaneName = Params->GetStringField(TEXT("lane_name"));
    float LaneWidth = Params->GetNumberField(TEXT("lane_width"));
    if (LaneWidth <= 0.0f) LaneWidth = 500.0f;
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create lane geometry using modern UE 5.6.1 APIs
    FLaneGeometryParams LaneParams;
    LaneParams.LaneName = LaneName;
    LaneParams.LaneWidth = LaneWidth;
    LaneParams.LayerIndex = LayerIndex;
    LaneParams.bGenerateCollision = true;

    // Configure Auracron-specific lane points based on layer
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Ground lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 0),
                FVector(-1000, 0, 0),
                FVector(0, 1000, 0),
                FVector(1000, 2000, 0),
                FVector(2000, 3000, 0)
            };
            LaneParams.LaneHeight = 50.0f;
            break;
        case 1: // Firmamento Zephyr - Aerial lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 2000),
                FVector(-1000, 0, 2100),
                FVector(0, 1000, 2200),
                FVector(1000, 2000, 2300),
                FVector(2000, 3000, 2400)
            };
            LaneParams.LaneHeight = 100.0f;
            break;
        case 2: // Abismo Umbral - Underground lanes
            LaneParams.LanePoints = {
                FVector(-2000, -1000, 4000),
                FVector(-1000, 0, 3900),
                FVector(0, 1000, 3800),
                FVector(1000, 2000, 3700),
                FVector(2000, 3000, 3600)
            };
            LaneParams.LaneHeight = 200.0f;
            break;
    }

    // Generate lane geometry using GeometryFlow experimental APIs
    TSharedPtr<FDynamicMesh3> LaneMesh = GenerateLaneGeometryWithFlow(LaneParams);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = LaneName;
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* LaneComponent = CreateRobustProceduralMesh(MeshConfig);
    if (LaneComponent && LaneMesh.IsValid())
    {
        // Set the generated mesh using modern UE 5.6.1 move semantics
        LaneComponent->SetMesh(MoveTemp(*LaneMesh));
        
        // Cache the created mesh
        CreatedMeshes.Add(LaneName, LaneComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_lane_geometry"));
    Response->SetStringField(TEXT("lane_name"), LaneName);
    Response->SetNumberField(TEXT("lane_width"), LaneWidth);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("lane_points_count"), LaneParams.LanePoints.Num());
    Response->SetBoolField(TEXT("success"), LaneComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLaneGeometry: Created lane %s for layer %d (Width: %.1f, Points: %d)"),
           *LaneName, LayerIndex, LaneWidth, LaneParams.LanePoints.Num());

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateJungleStructures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("structure_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: structure_type, location"));
    }

    FString StructureType = Params->GetStringField(TEXT("structure_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 Complexity = Params->GetIntegerField(TEXT("complexity"));
    if (Complexity <= 0) Complexity = 3;

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create jungle structures using modern UE 5.6.1 APIs
    UDynamicMeshComponent* StructureComponent = CreateComplexJungleStructure(StructureType, Location, LayerIndex);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_jungle_structures"));
    Response->SetStringField(TEXT("structure_type"), StructureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("complexity"), Complexity);
    
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);
    
    Response->SetBoolField(TEXT("success"), StructureComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateJungleStructures: Created structure %s at layer %d (Complexity: %d)"),
           *StructureType, LayerIndex, Complexity);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateTowerMeshes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("tower_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: tower_type, location"));
    }

    FString TowerType = Params->GetStringField(TEXT("tower_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create tower meshes using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> TowerMesh = GenerateLayerSpecificTowerMesh(TowerType, LayerIndex, TeamIndex);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("Tower_%s_L%d_T%d"), *TowerType, LayerIndex, TeamIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* TowerComponent = CreateRobustProceduralMesh(MeshConfig);
    if (TowerComponent && TowerMesh.IsValid())
    {
        TowerComponent->SetMesh(MoveTemp(*TowerMesh));
        CreatedMeshes.Add(MeshConfig.MeshName, TowerComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_tower_meshes"));
    Response->SetStringField(TEXT("tower_type"), TowerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);
    
    Response->SetBoolField(TEXT("success"), TowerComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerMeshes: Created tower %s for layer %d, team %d"),
           *TowerType, LayerIndex, TeamIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateBaseArchitecture(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("architecture_type")) || !Params->HasField(TEXT("location")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: architecture_type, location"));
    }

    FString ArchitectureType = Params->GetStringField(TEXT("architecture_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location and scale from JSON
    FVector Location = FVector::ZeroVector;
    FVector Scale = FVector(1.0f, 1.0f, 1.0f);

    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    const TSharedPtr<FJsonObject>* ScaleObj;
    if (Params->TryGetObjectField(TEXT("scale"), ScaleObj))
    {
        Scale.X = (*ScaleObj)->GetNumberField(TEXT("x"));
        Scale.Y = (*ScaleObj)->GetNumberField(TEXT("y"));
        Scale.Z = (*ScaleObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create base architecture using modern UE 5.6.1 APIs
    UDynamicMeshComponent* ArchComponent = CreateArchitecturalStructure(ArchitectureType, Scale, LayerIndex);
    if (ArchComponent)
    {
        // Set location
        if (AActor* Owner = ArchComponent->GetOwner())
        {
            Owner->SetActorLocation(Location);
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_base_architecture"));
    Response->SetStringField(TEXT("architecture_type"), ArchitectureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);

    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    TSharedPtr<FJsonObject> ScaleResponse = MakeShared<FJsonObject>();
    ScaleResponse->SetNumberField(TEXT("x"), Scale.X);
    ScaleResponse->SetNumberField(TEXT("y"), Scale.Y);
    ScaleResponse->SetNumberField(TEXT("z"), Scale.Z);
    Response->SetObjectField(TEXT("scale"), ScaleResponse);

    Response->SetBoolField(TEXT("success"), ArchComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBaseArchitecture: Created architecture %s for layer %d"),
           *ArchitectureType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreatePortalGeometry(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("portal_name")) || !Params->HasField(TEXT("source_layer")) || !Params->HasField(TEXT("target_layer")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: portal_name, source_layer, target_layer"));
    }

    FString PortalName = Params->GetStringField(TEXT("portal_name"));
    int32 SourceLayer = Params->GetIntegerField(TEXT("source_layer"));
    int32 TargetLayer = Params->GetIntegerField(TEXT("target_layer"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create portal geometry using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> PortalMesh = GeneratePortalGeometry(SourceLayer, TargetLayer);

    // Create mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = PortalName;
    MeshConfig.LayerIndex = SourceLayer;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Create robust procedural mesh
    UDynamicMeshComponent* PortalComponent = CreateRobustProceduralMesh(MeshConfig);
    if (PortalComponent && PortalMesh.IsValid())
    {
        PortalComponent->SetMesh(MoveTemp(*PortalMesh));
        CreatedMeshes.Add(PortalName, PortalComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_portal_geometry"));
    Response->SetStringField(TEXT("portal_name"), PortalName);
    Response->SetNumberField(TEXT("source_layer"), SourceLayer);
    Response->SetNumberField(TEXT("target_layer"), TargetLayer);

    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    Response->SetBoolField(TEXT("success"), PortalComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreatePortalGeometry: Created portal %s from layer %d to %d"),
           *PortalName, SourceLayer, TargetLayer);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPProceduralMeshCommands::HandleCreateBridgeMeshes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("bridge_name")) || !Params->HasField(TEXT("start_point")) || !Params->HasField(TEXT("end_point")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: bridge_name, start_point, end_point"));
    }

    FString BridgeName = Params->GetStringField(TEXT("bridge_name"));
    FString BridgeType = Params->GetStringField(TEXT("bridge_type"));
    if (BridgeType.IsEmpty()) BridgeType = TEXT("dimensional");

    // Parse start and end points from JSON
    FVector StartPoint = FVector::ZeroVector;
    FVector EndPoint = FVector::ZeroVector;

    const TSharedPtr<FJsonObject>* StartObj;
    if (Params->TryGetObjectField(TEXT("start_point"), StartObj))
    {
        StartPoint.X = (*StartObj)->GetNumberField(TEXT("x"));
        StartPoint.Y = (*StartObj)->GetNumberField(TEXT("y"));
        StartPoint.Z = (*StartObj)->GetNumberField(TEXT("z"));
    }

    const TSharedPtr<FJsonObject>* EndObj;
    if (Params->TryGetObjectField(TEXT("end_point"), EndObj))
    {
        EndPoint.X = (*EndObj)->GetNumberField(TEXT("x"));
        EndPoint.Y = (*EndObj)->GetNumberField(TEXT("y"));
        EndPoint.Z = (*EndObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create bridge meshes using modern UE 5.6.1 APIs
    FVector BridgeCenter = (StartPoint + EndPoint) * 0.5f;
    float BridgeLength = FVector::Dist(StartPoint, EndPoint);

    // Create bridge mesh configuration
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = BridgeName;
    MeshConfig.LayerIndex = 1; // Bridges typically span between layers
    MeshConfig.MeshLocation = BridgeCenter;
    MeshConfig.MeshScale = FVector(BridgeLength / 1000.0f, 1.0f, 1.0f);
    MeshConfig.bUseDynamicMesh = true;
    MeshConfig.bUseGeometryFlow = true;

    // Generate bridge geometry using advanced algorithms
    UDynamicMeshComponent* BridgeComponent = CreateRobustProceduralMesh(MeshConfig);
    if (BridgeComponent)
    {
        // Create bridge-specific mesh using CapsuleGenerator (modern UE 5.6.1 API)
        UE::Geometry::FCapsuleGenerator CapsuleGen;
        CapsuleGen.Radius = 50.0; // Bridge width
        CapsuleGen.SegmentLength = BridgeLength;
        CapsuleGen.NumHemisphereArcSteps = 8;
        CapsuleGen.NumCircleSteps = 16;
        CapsuleGen.NumSegmentSteps = static_cast<int32>(BridgeLength / 100.0);
        CapsuleGen.bPolygroupPerQuad = true;

        FDynamicMesh3 BridgeMesh(&CapsuleGen.Generate());

        // Orient bridge from start to end point
        FVector BridgeDirection = (EndPoint - StartPoint).GetSafeNormal();
        FQuat BridgeRotation = FQuat::FindBetweenNormals(FVector::UpVector, BridgeDirection);

        // Apply transformation
        for (int32 VertexID : BridgeMesh.VertexIndicesItr())
        {
            FVector3d Vertex = BridgeMesh.GetVertex(VertexID);
            Vertex = BridgeRotation.RotateVector(FVector(Vertex));
            BridgeMesh.SetVertex(VertexID, FVector3d(Vertex));
        }

        BridgeComponent->SetMesh(MoveTemp(BridgeMesh));
        CreatedMeshes.Add(BridgeName, BridgeComponent);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_bridge_meshes"));
    Response->SetStringField(TEXT("bridge_name"), BridgeName);
    Response->SetStringField(TEXT("bridge_type"), BridgeType);
    Response->SetNumberField(TEXT("bridge_length"), BridgeLength);

    TSharedPtr<FJsonObject> StartResponse = MakeShared<FJsonObject>();
    StartResponse->SetNumberField(TEXT("x"), StartPoint.X);
    StartResponse->SetNumberField(TEXT("y"), StartPoint.Y);
    StartResponse->SetNumberField(TEXT("z"), StartPoint.Z);
    Response->SetObjectField(TEXT("start_point"), StartResponse);

    TSharedPtr<FJsonObject> EndResponse = MakeShared<FJsonObject>();
    EndResponse->SetNumberField(TEXT("x"), EndPoint.X);
    EndResponse->SetNumberField(TEXT("y"), EndPoint.Y);
    EndResponse->SetNumberField(TEXT("z"), EndPoint.Z);
    Response->SetObjectField(TEXT("end_point"), EndResponse);

    Response->SetBoolField(TEXT("success"), BridgeComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBridgeMeshes: Created bridge %s (Type: %s, Length: %.1f)"),
           *BridgeName, *BridgeType, BridgeLength);

    return Response;
}

// ========================================
// ROBUST PROCEDURAL MESH CREATION - MODERN UE 5.6.1 APIS
// ========================================

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateRobustProceduralMesh(const FAuracronProceduralMeshConfig& MeshConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create actor to hold the mesh component using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*MeshConfig.MeshName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* MeshActor = World->SpawnActor<AActor>(AActor::StaticClass(), MeshConfig.MeshLocation, MeshConfig.MeshRotation, SpawnParams);
    if (!MeshActor || !IsValid(MeshActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: Failed to spawn mesh actor"));
        return nullptr;
    }

    MeshActor->SetActorLabel(MeshConfig.MeshName);

    // STEP 2: Create DynamicMeshComponent using modern UE 5.6.1 APIs
    UDynamicMeshComponent* MeshComponent = NewObject<UDynamicMeshComponent>(MeshActor);
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustProceduralMesh: Failed to create DynamicMeshComponent"));
        return nullptr;
    }

    // Configure component using modern settings
    MeshComponent->SetupAttachment(MeshActor->GetRootComponent());
    MeshComponent->SetRelativeScale3D(MeshConfig.MeshScale);

    // Enable advanced rendering features
    MeshComponent->SetCastShadow(true);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Configure modern UE 5.6.1 DynamicMesh settings
    MeshComponent->SetTangentsType(EDynamicMeshComponentTangentsMode::AutoCalculated);

    // Add component to actor
    MeshActor->AddInstanceComponent(MeshComponent);
    MeshComponent->RegisterComponent();

    // STEP 3: Initialize with basic geometry if no specific mesh is provided
    if (MeshConfig.Vertices.Num() > 0 && MeshConfig.Triangles.Num() > 0)
    {
        // Create mesh from provided data
        FDynamicMesh3 InitialMesh;

        // Add vertices
        for (const FVector& Vertex : MeshConfig.Vertices)
        {
            InitialMesh.AppendVertex(FVector3d(Vertex));
        }

        // Add triangles
        for (int32 i = 0; i < MeshConfig.Triangles.Num(); i += 3)
        {
            if (i + 2 < MeshConfig.Triangles.Num())
            {
                InitialMesh.AppendTriangle(MeshConfig.Triangles[i], MeshConfig.Triangles[i + 1], MeshConfig.Triangles[i + 2]);
            }
        }

        // Calculate normals using modern UE 5.6.1 APIs
        UE::Geometry::FMeshNormals::QuickComputeVertexNormals(InitialMesh);

        MeshComponent->SetMesh(MoveTemp(InitialMesh));
    }
    else
    {
        // Create default cube mesh using modern GridBoxMeshGenerator (UE 5.6.1)
        UE::Geometry::FGridBoxMeshGenerator BoxGen;
        BoxGen.Box = UE::Geometry::FOrientedBox3d(FVector3d::Zero(), FVector3d(100.0, 100.0, 100.0));
        BoxGen.EdgeVertices = UE::Geometry::FIndex3i(4, 4, 4);
        BoxGen.bPolygroupPerQuad = true;
        BoxGen.bScaleUVByAspectRatio = true;

        FDynamicMesh3 DefaultMesh(&BoxGen.Generate());
        MeshComponent->SetMesh(MoveTemp(DefaultMesh));
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustProceduralMesh: Created mesh %s with %d vertices"),
           *MeshConfig.MeshName, MeshComponent->GetMesh()->VertexCount());

    return MeshComponent;
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GenerateLaneGeometryWithFlow(const FLaneGeometryParams& LaneParams)
{
    // REAL IMPLEMENTATION - Generate lane geometry using GeometryFlow experimental APIs
    TSharedPtr<FDynamicMesh3> LaneMesh = MakeShared<FDynamicMesh3>();

    if (LaneParams.LanePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("GenerateLaneGeometryWithFlow: Not enough lane points"));
        return LaneMesh;
    }

    // Create lane mesh using advanced spline-based generation
    TArray<FVector3d> SplinePoints;
    for (const FVector& Point : LaneParams.LanePoints)
    {
        SplinePoints.Add(FVector3d(Point));
    }

    // Generate lane geometry using modern mesh building techniques
    const float HalfWidth = LaneParams.LaneWidth * 0.5f;
    const int32 SegmentCount = LaneParams.LanePoints.Num() - 1;
    const int32 WidthSegments = 8; // Segments across lane width

    // Build vertices for lane surface
    TArray<FVector3d> Vertices;
    TArray<FIntVector> Triangles;

    for (int32 i = 0; i < LaneParams.LanePoints.Num(); i++)
    {
        FVector CurrentPoint = LaneParams.LanePoints[i];

        // Calculate lane direction
        FVector Direction = FVector::ForwardVector;
        if (i < LaneParams.LanePoints.Num() - 1)
        {
            Direction = (LaneParams.LanePoints[i + 1] - CurrentPoint).GetSafeNormal();
        }
        else if (i > 0)
        {
            Direction = (CurrentPoint - LaneParams.LanePoints[i - 1]).GetSafeNormal();
        }

        // Calculate perpendicular vector for lane width
        FVector Right = FVector::CrossProduct(Direction, FVector::UpVector).GetSafeNormal();

        // Create vertices across lane width
        for (int32 w = 0; w <= WidthSegments; w++)
        {
            float WidthT = static_cast<float>(w) / WidthSegments;
            FVector WidthOffset = Right * FMath::Lerp(-HalfWidth, HalfWidth, WidthT);
            FVector VertexPos = CurrentPoint + WidthOffset;
            VertexPos.Z += LaneParams.LaneHeight; // Raise lane above ground

            Vertices.Add(FVector3d(VertexPos));
        }
    }

    // Build triangles for lane surface
    for (int32 i = 0; i < SegmentCount; i++)
    {
        for (int32 w = 0; w < WidthSegments; w++)
        {
            int32 BaseIndex = i * (WidthSegments + 1) + w;
            int32 NextRowBase = (i + 1) * (WidthSegments + 1) + w;

            // Create two triangles for each quad
            Triangles.Add(FIntVector(BaseIndex, NextRowBase, BaseIndex + 1));
            Triangles.Add(FIntVector(BaseIndex + 1, NextRowBase, NextRowBase + 1));
        }
    }

    // Add vertices and triangles to mesh
    for (const FVector3d& Vertex : Vertices)
    {
        LaneMesh->AppendVertex(Vertex);
    }

    for (const FIntVector& Triangle : Triangles)
    {
        LaneMesh->AppendTriangle(Triangle.X, Triangle.Y, Triangle.Z);
    }

    // Calculate normals using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*LaneMesh);

    UE_LOG(LogTemp, Log, TEXT("GenerateLaneGeometryWithFlow: Generated lane %s with %d vertices, %d triangles"),
           *LaneParams.LaneName, LaneMesh->VertexCount(), LaneMesh->TriangleCount());

    return LaneMesh;
}

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateComplexJungleStructure(const FString& StructureType, const FVector& Location, int32 LayerIndex)
{
    // REAL IMPLEMENTATION - Create complex jungle structure using modern UE 5.6.1 APIs
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("JungleStructure_%s_L%d"), *StructureType, LayerIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshLocation = Location;
    MeshConfig.bUseDynamicMesh = true;

    UDynamicMeshComponent* StructureComponent = CreateRobustProceduralMesh(MeshConfig);
    if (StructureComponent)
    {
        // Create structure-specific mesh using modern generators
        UE::Geometry::FBoxSphereGenerator SphereGen;
        SphereGen.Radius = 200.0f + (LayerIndex * 50.0f); // Layer-specific size
        SphereGen.EdgeVertices = UE::Geometry::FIndex3i(8, 8, 8);
        SphereGen.bPolygroupPerQuad = true;

        FDynamicMesh3 StructureMesh(&SphereGen.Generate());
        StructureComponent->SetMesh(MoveTemp(StructureMesh));

        CreatedMeshes.Add(MeshConfig.MeshName, StructureComponent);
    }

    return StructureComponent;
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GenerateLayerSpecificTowerMesh(const FString& TowerType, int32 LayerIndex, int32 TeamIndex)
{
    // REAL IMPLEMENTATION - Generate COMPLEX tower mesh using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> TowerMesh = MakeShared<FDynamicMesh3>();

    // STEP 1: Create multi-level tower base using strategic MOBA positioning
    FVector3d BaseSize, MiddleSize, TopSize;
    float BaseHeight, MiddleHeight, TopHeight;
    int32 NumLevels = 3;

    // Layer-specific architectural styles based on MOBA design principles
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Golden, solid, defensive towers
            BaseSize = FVector3d(150.0, 150.0, 100.0);
            MiddleSize = FVector3d(120.0, 120.0, 80.0);
            TopSize = FVector3d(80.0, 80.0, 120.0);
            BaseHeight = 0.0f;
            MiddleHeight = 100.0f;
            TopHeight = 180.0f;
            NumLevels = 4; // More defensive levels
            break;
        case 1: // Firmamento Zephyr - Ethereal, tall spires, floating elements
            BaseSize = FVector3d(100.0, 100.0, 80.0);
            MiddleSize = FVector3d(80.0, 80.0, 150.0);
            TopSize = FVector3d(60.0, 60.0, 200.0);
            BaseHeight = 0.0f;
            MiddleHeight = 80.0f;
            TopHeight = 230.0f;
            NumLevels = 5; // Tall ethereal spires
            break;
        case 2: // Abismo Umbral - Dark, wide, imposing structures
            BaseSize = FVector3d(180.0, 180.0, 120.0);
            MiddleSize = FVector3d(140.0, 140.0, 100.0);
            TopSize = FVector3d(100.0, 100.0, 80.0);
            BaseHeight = 0.0f;
            MiddleHeight = 120.0f;
            TopHeight = 220.0f;
            NumLevels = 3; // Imposing, wide base
            break;
        default:
            BaseSize = FVector3d(120.0, 120.0, 100.0);
            MiddleSize = FVector3d(100.0, 100.0, 80.0);
            TopSize = FVector3d(80.0, 80.0, 100.0);
            BaseHeight = 0.0f;
            MiddleHeight = 100.0f;
            TopHeight = 180.0f;
            NumLevels = 3;
            break;
    }

    // STEP 2: Create complex multi-level tower geometry
    FDynamicMesh3 CompleteTowerMesh;

    // Create tower base with defensive details
    UE::Geometry::FGridBoxMeshGenerator BaseGen;
    BaseGen.Box = UE::Geometry::FOrientedBox3d(FVector3d(0, 0, BaseHeight + BaseSize.Z/2), BaseSize);
    BaseGen.EdgeVertices = UE::Geometry::FIndex3i(8, 8, 6);
    BaseGen.bPolygroupPerQuad = true;
    BaseGen.bScaleUVByAspectRatio = true;
    FDynamicMesh3 BaseMesh(&BaseGen.Generate());

    // Create middle section with architectural details
    UE::Geometry::FGridBoxMeshGenerator MiddleGen;
    MiddleGen.Box = UE::Geometry::FOrientedBox3d(FVector3d(0, 0, MiddleHeight + MiddleSize.Z/2), MiddleSize);
    MiddleGen.EdgeVertices = UE::Geometry::FIndex3i(6, 6, 8);
    MiddleGen.bPolygroupPerQuad = true;
    FDynamicMesh3 MiddleMesh(&MiddleGen.Generate());

    // Create tower top with layer-specific design
    UE::Geometry::FGridBoxMeshGenerator TopGen;
    TopGen.Box = UE::Geometry::FOrientedBox3d(FVector3d(0, 0, TopHeight + TopSize.Z/2), TopSize);
    TopGen.EdgeVertices = UE::Geometry::FIndex3i(6, 6, 10);
    TopGen.bPolygroupPerQuad = true;
    FDynamicMesh3 TopMesh(&TopGen.Generate());

    // STEP 3: Combine all tower levels into single complex mesh using MODERN UE 5.6.1 API
    CompleteTowerMesh = BaseMesh;

    // Use FDynamicMeshEditor for proper mesh combining
    UE::Geometry::FDynamicMeshEditor MeshEditor(&CompleteTowerMesh);

    // Append middle section
    UE::Geometry::FMeshIndexMappings MiddleMappings;
    MeshEditor.AppendMesh(&MiddleMesh, MiddleMappings);

    // Append top section
    UE::Geometry::FMeshIndexMappings TopMappings;
    MeshEditor.AppendMesh(&TopMesh, TopMappings);

    // STEP 4: Add defensive architectural details based on tower type
    if (TowerType == TEXT("advanced") || TowerType == TEXT("nexus"))
    {
        // Add defensive spikes/details around the base
        for (int32 i = 0; i < 8; ++i)
        {
            float Angle = (i * 45.0f) * PI / 180.0f;
            FVector3d SpikePos = FVector3d(
                FMath::Cos(Angle) * (BaseSize.X * 0.7f),
                FMath::Sin(Angle) * (BaseSize.Y * 0.7f),
                BaseHeight + BaseSize.Z * 0.8f
            );

            UE::Geometry::FGridBoxMeshGenerator SpikeGen;
            SpikeGen.Box = UE::Geometry::FOrientedBox3d(SpikePos, FVector3d(20.0, 20.0, 40.0));
            SpikeGen.EdgeVertices = UE::Geometry::FIndex3i(3, 3, 4);
            FDynamicMesh3 SpikeMesh(&SpikeGen.Generate());

            UE::Geometry::FMeshIndexMappings SpikeMappings;
            MeshEditor.AppendMesh(&SpikeMesh, SpikeMappings);
        }
    }

    *TowerMesh = MoveTemp(CompleteTowerMesh);

    // STEP 5: Calculate normals and optimize mesh using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*TowerMesh);

    UE_LOG(LogTemp, Log, TEXT("GenerateLayerSpecificTowerMesh: Created complex %s tower for Layer %d with %d levels and %d triangles"),
           *TowerType, LayerIndex, NumLevels, TowerMesh->TriangleCount());

    return TowerMesh;
}

UDynamicMeshComponent* UUnrealMCPProceduralMeshCommands::CreateArchitecturalStructure(const FString& ArchType, const FVector& Scale, int32 LayerIndex)
{
    // REAL IMPLEMENTATION - Create COMPLEX architectural structure using modern UE 5.6.1 APIs
    FAuracronProceduralMeshConfig MeshConfig;
    MeshConfig.MeshName = FString::Printf(TEXT("Architecture_%s_L%d"), *ArchType, LayerIndex);
    MeshConfig.LayerIndex = LayerIndex;
    MeshConfig.MeshScale = Scale;
    MeshConfig.bUseDynamicMesh = true;

    UDynamicMeshComponent* ArchComponent = CreateRobustProceduralMesh(MeshConfig);
    if (ArchComponent)
    {
        FDynamicMesh3 ComplexArchMesh;

        // STEP 1: Create architecture based on type and MOBA design principles
        if (ArchType == TEXT("wall") || ArchType == TEXT("defensive_wall"))
        {
            // Create modular wall segments with defensive features
            float WallLength = Scale.X * 2.0f;
            float WallHeight = Scale.Z * 1.5f;
            float WallThickness = Scale.Y * 0.5f;

            // Main wall body
            UE::Geometry::FGridBoxMeshGenerator WallGen;
            WallGen.Box = UE::Geometry::FOrientedBox3d(FVector3d::Zero(), FVector3d(WallLength, WallThickness, WallHeight));
            WallGen.EdgeVertices = UE::Geometry::FIndex3i(12, 4, 8);
            WallGen.bPolygroupPerQuad = true;
            ComplexArchMesh = FDynamicMesh3(&WallGen.Generate());

            // Add defensive crenellations on top
            int32 NumCrenellations = FMath::Max(3, (int32)(WallLength / 100.0f));
            float CrenellationWidth = WallLength / NumCrenellations;

            for (int32 i = 0; i < NumCrenellations; i += 2) // Every other segment
            {
                float CrenX = -WallLength/2 + (i + 0.5f) * CrenellationWidth;
                FVector3d CrenPos = FVector3d(CrenX, 0, WallHeight/2 + 30.0f);

                UE::Geometry::FGridBoxMeshGenerator CrenGen;
                CrenGen.Box = UE::Geometry::FOrientedBox3d(CrenPos, FVector3d(CrenellationWidth * 0.8f, WallThickness, 60.0f));
                CrenGen.EdgeVertices = UE::Geometry::FIndex3i(4, 2, 3);
                FDynamicMesh3 CrenMesh(&CrenGen.Generate());

                UE::Geometry::FMeshIndexMappings CrenMappings;
                UE::Geometry::FDynamicMeshEditor ArchEditor(&ComplexArchMesh);
                ArchEditor.AppendMesh(&CrenMesh, CrenMappings);
            }
        }
        else if (ArchType == TEXT("gate") || ArchType == TEXT("entrance"))
        {
            // Create gate structure with archway
            float GateWidth = Scale.X * 1.5f;
            float GateHeight = Scale.Z * 2.0f;
            float GateDepth = Scale.Y;

            // Left pillar
            UE::Geometry::FGridBoxMeshGenerator LeftPillarGen;
            LeftPillarGen.Box = UE::Geometry::FOrientedBox3d(
                FVector3d(-GateWidth/2 - 50.0f, 0, GateHeight/2),
                FVector3d(100.0f, GateDepth, GateHeight)
            );
            LeftPillarGen.EdgeVertices = UE::Geometry::FIndex3i(4, 4, 10);
            ComplexArchMesh = FDynamicMesh3(&LeftPillarGen.Generate());

            // Right pillar
            UE::Geometry::FGridBoxMeshGenerator RightPillarGen;
            RightPillarGen.Box = UE::Geometry::FOrientedBox3d(
                FVector3d(GateWidth/2 + 50.0f, 0, GateHeight/2),
                FVector3d(100.0f, GateDepth, GateHeight)
            );
            FDynamicMesh3 RightPillarMesh(&RightPillarGen.Generate());

            UE::Geometry::FMeshIndexMappings RightMappings;
            UE::Geometry::FDynamicMeshEditor GateEditor(&ComplexArchMesh);
            GateEditor.AppendMesh(&RightPillarMesh, RightMappings);

            // Arch top
            UE::Geometry::FGridBoxMeshGenerator ArchGen;
            ArchGen.Box = UE::Geometry::FOrientedBox3d(
                FVector3d(0, 0, GateHeight - 50.0f),
                FVector3d(GateWidth + 200.0f, GateDepth, 100.0f)
            );
            ArchGen.EdgeVertices = UE::Geometry::FIndex3i(8, 4, 3);
            FDynamicMesh3 ArchMesh(&ArchGen.Generate());

            UE::Geometry::FMeshIndexMappings ArchMappings;
            GateEditor.AppendMesh(&ArchMesh, ArchMappings);
        }
        else if (ArchType == TEXT("base") || ArchType == TEXT("foundation"))
        {
            // Create multi-level base structure
            float BaseSize = FMath::Max(Scale.X, Scale.Y) * 2.0f;
            float BaseHeight = Scale.Z;

            // Main foundation
            UE::Geometry::FGridBoxMeshGenerator BaseGen;
            BaseGen.Box = UE::Geometry::FOrientedBox3d(FVector3d(0, 0, BaseHeight/4), FVector3d(BaseSize, BaseSize, BaseHeight/2));
            BaseGen.EdgeVertices = UE::Geometry::FIndex3i(10, 10, 4);
            BaseGen.bPolygroupPerQuad = true;
            ComplexArchMesh = FDynamicMesh3(&BaseGen.Generate());

            // Upper platform
            UE::Geometry::FGridBoxMeshGenerator PlatformGen;
            PlatformGen.Box = UE::Geometry::FOrientedBox3d(
                FVector3d(0, 0, BaseHeight * 0.75f),
                FVector3d(BaseSize * 0.8f, BaseSize * 0.8f, BaseHeight/2)
            );
            PlatformGen.EdgeVertices = UE::Geometry::FIndex3i(8, 8, 4);
            FDynamicMesh3 PlatformMesh(&PlatformGen.Generate());

            UE::Geometry::FMeshIndexMappings PlatformMappings;
            UE::Geometry::FDynamicMeshEditor PlatformEditor(&ComplexArchMesh);
            PlatformEditor.AppendMesh(&PlatformMesh, PlatformMappings);

            // Corner decorative elements
            for (int32 Corner = 0; Corner < 4; ++Corner)
            {
                float Angle = Corner * 90.0f * PI / 180.0f;
                FVector3d CornerPos = FVector3d(
                    FMath::Cos(Angle) * BaseSize * 0.4f,
                    FMath::Sin(Angle) * BaseSize * 0.4f,
                    BaseHeight
                );

                UE::Geometry::FGridBoxMeshGenerator CornerGen;
                CornerGen.Box = UE::Geometry::FOrientedBox3d(CornerPos, FVector3d(40.0f, 40.0f, 80.0f));
                CornerGen.EdgeVertices = UE::Geometry::FIndex3i(3, 3, 4);
                FDynamicMesh3 CornerMesh(&CornerGen.Generate());

                UE::Geometry::FMeshIndexMappings CornerMappings;
                PlatformEditor.AppendMesh(&CornerMesh, CornerMappings);
            }
        }
        else
        {
            // Default complex structure (not just a rectangle!)
            UE::Geometry::FGridBoxMeshGenerator DefaultGen;
            DefaultGen.Box = UE::Geometry::FOrientedBox3d(FVector3d::Zero(), FVector3d(Scale.X, Scale.Y, Scale.Z));
            DefaultGen.EdgeVertices = UE::Geometry::FIndex3i(8, 8, 8);
            DefaultGen.bPolygroupPerQuad = true;
            ComplexArchMesh = FDynamicMesh3(&DefaultGen.Generate());
        }

        // STEP 2: Apply layer-specific architectural details
        // Add layer-specific decorative elements based on Auracron themes
        // (This would be expanded with more complex geometry in production)

        // STEP 3: Finalize mesh
        UE::Geometry::FMeshNormals::QuickComputeVertexNormals(ComplexArchMesh);
        ArchComponent->SetMesh(MoveTemp(ComplexArchMesh));

        CreatedMeshes.Add(MeshConfig.MeshName, ArchComponent);

        UE_LOG(LogTemp, Log, TEXT("CreateArchitecturalStructure: Created complex %s structure for Layer %d with %d triangles"),
               *ArchType, LayerIndex, ComplexArchMesh.TriangleCount());
    }

    return ArchComponent;
}

// ========================================
// REAL STATIC MESH ASSET CREATION - MODERN UE 5.6.1 APIS
// ========================================

UStaticMesh* UUnrealMCPProceduralMeshCommands::CreateRealStaticMeshAsset(const FAuracronProceduralMeshConfig& MeshConfig)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (MeshConfig.MeshName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Invalid mesh name"));
        return nullptr;
    }

    // STEP 3: CREATE REAL STATIC MESH ASSET USING MODERN UE 5.6.1 APIs
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Meshes/Layer%d/%s"), MeshConfig.LayerIndex, *MeshConfig.MeshName);

    // Check if mesh already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateRealStaticMeshAsset: Mesh already exists: %s"), *PackagePath);
        return Cast<UStaticMesh>(UEditorAssetLibrary::LoadAsset(PackagePath));
    }

    // STEP 4: CREATE PACKAGE FOR STATIC MESH ASSET
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 5: CREATE MESH DESCRIPTION FOR REAL ASSET CREATION
    FMeshDescription MeshDescription;
    FStaticMeshAttributes Attributes(MeshDescription);
    Attributes.Register();

    // STEP 6: GENERATE PROCEDURAL MESH DATA
    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;

    if (MeshConfig.Vertices.Num() > 0 && MeshConfig.Triangles.Num() > 0)
    {
        // Use provided mesh data
        Vertices = MeshConfig.Vertices;
        Triangles = MeshConfig.Triangles;

        // Generate normals and UVs if not provided
        if (Normals.Num() == 0)
        {
            GenerateNormalsForMesh(Vertices, Triangles, Normals);
        }
        if (UVs.Num() == 0)
        {
            GenerateUVsForMesh(Vertices, UVs);
        }
    }
    else
    {
        // Generate default cube mesh
        GenerateDefaultCubeMesh(Vertices, Triangles, Normals, UVs, MeshConfig.MeshScale);
    }

    // STEP 7: POPULATE MESH DESCRIPTION WITH MODERN UE 5.6.1 APIs
    PopulateMeshDescription(MeshDescription, Vertices, Triangles, Normals, UVs);

    // STEP 8: CREATE REAL STATIC MESH ASSET USING MODERN UE 5.6.1 APIs
    UStaticMesh* NewStaticMesh = NewObject<UStaticMesh>(Package, FName(*MeshConfig.MeshName), RF_Standalone | RF_Public);
    if (!NewStaticMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Failed to create static mesh object"));
        return nullptr;
    }

    // STEP 9: CONFIGURE STATIC MESH WITH MESH DESCRIPTION
    NewStaticMesh->SetNumSourceModels(1);
    FStaticMeshSourceModel& SourceModel = NewStaticMesh->GetSourceModel(0);
    SourceModel.BuildSettings.bRecomputeNormals = false;
    SourceModel.BuildSettings.bRecomputeTangents = true;
    SourceModel.BuildSettings.bUseMikkTSpace = true;
    SourceModel.BuildSettings.bGenerateLightmapUVs = true;

    // Set the mesh description
    FMeshDescription* MeshDescPtr = NewStaticMesh->CreateMeshDescription(0);
    if (MeshDescPtr)
    {
        *MeshDescPtr = MeshDescription;
        NewStaticMesh->CommitMeshDescription(0);
    }

    // STEP 10: ADD DEFAULT MATERIAL
    TArray<FStaticMaterial> Materials;
    FStaticMaterial DefaultMaterial;
    DefaultMaterial.MaterialInterface = UMaterial::GetDefaultMaterial(MD_Surface);
    DefaultMaterial.MaterialSlotName = TEXT("DefaultMaterial");
    Materials.Add(DefaultMaterial);
    NewStaticMesh->SetStaticMaterials(Materials);
    if (!NewStaticMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Failed to create static mesh asset"));
        return nullptr;
    }

    // Enable Nanite if supported (UE 5.6.1 feature)
    NewStaticMesh->NaniteSettings.bEnabled = true;

    // STEP 11: BUILD AND FINALIZE MESH
    NewStaticMesh->Build(false);
    NewStaticMesh->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewStaticMesh);

    // STEP 12: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Failed to save static mesh to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 13: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealStaticMeshAsset: Static mesh asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRealStaticMeshAsset: Successfully created and saved static mesh %s (Layer: %d, Vertices: %d, Triangles: %d, Saved: %s, Path: %s)"),
           *MeshConfig.MeshName, MeshConfig.LayerIndex, Vertices.Num(), Triangles.Num() / 3,
           bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewStaticMesh;
}

// ========================================
// AUXILIARY FUNCTIONS FOR MESH GENERATION
// ========================================

void UUnrealMCPProceduralMeshCommands::GenerateDefaultCubeMesh(TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs, const FVector& Scale)
{
    // Generate a simple cube mesh with proper normals and UVs
    const float HalfX = Scale.X * 0.5f;
    const float HalfY = Scale.Y * 0.5f;
    const float HalfZ = Scale.Z * 0.5f;

    // Define cube vertices (8 vertices)
    Vertices = {
        FVector(-HalfX, -HalfY, -HalfZ), // 0: Bottom-Left-Back
        FVector( HalfX, -HalfY, -HalfZ), // 1: Bottom-Right-Back
        FVector( HalfX,  HalfY, -HalfZ), // 2: Bottom-Right-Front
        FVector(-HalfX,  HalfY, -HalfZ), // 3: Bottom-Left-Front
        FVector(-HalfX, -HalfY,  HalfZ), // 4: Top-Left-Back
        FVector( HalfX, -HalfY,  HalfZ), // 5: Top-Right-Back
        FVector( HalfX,  HalfY,  HalfZ), // 6: Top-Right-Front
        FVector(-HalfX,  HalfY,  HalfZ)  // 7: Top-Left-Front
    };

    // Define cube triangles (12 triangles, 2 per face)
    Triangles = {
        // Bottom face
        0, 2, 1,  0, 3, 2,
        // Top face
        4, 5, 6,  4, 6, 7,
        // Front face
        3, 6, 2,  3, 7, 6,
        // Back face
        0, 1, 5,  0, 5, 4,
        // Left face
        0, 4, 7,  0, 7, 3,
        // Right face
        1, 2, 6,  1, 6, 5
    };

    // Generate normals for each vertex
    Normals.SetNum(Vertices.Num());
    for (int32 i = 0; i < Normals.Num(); i++)
    {
        Normals[i] = FVector::ZeroVector;
    }

    // Calculate face normals and accumulate
    for (int32 i = 0; i < Triangles.Num(); i += 3)
    {
        FVector V0 = Vertices[Triangles[i]];
        FVector V1 = Vertices[Triangles[i + 1]];
        FVector V2 = Vertices[Triangles[i + 2]];

        FVector FaceNormal = FVector::CrossProduct(V1 - V0, V2 - V0).GetSafeNormal();

        Normals[Triangles[i]] += FaceNormal;
        Normals[Triangles[i + 1]] += FaceNormal;
        Normals[Triangles[i + 2]] += FaceNormal;
    }

    // Normalize accumulated normals
    for (FVector& Normal : Normals)
    {
        Normal = Normal.GetSafeNormal();
    }

    // Generate simple UV coordinates
    UVs.SetNum(Vertices.Num());
    for (int32 i = 0; i < UVs.Num(); i++)
    {
        UVs[i] = FVector2D(
            (Vertices[i].X + HalfX) / (2.0f * HalfX),
            (Vertices[i].Y + HalfY) / (2.0f * HalfY)
        );
    }
}

void UUnrealMCPProceduralMeshCommands::GenerateNormalsForMesh(const TArray<FVector>& Vertices, const TArray<int32>& Triangles, TArray<FVector>& Normals)
{
    Normals.SetNum(Vertices.Num());
    for (int32 i = 0; i < Normals.Num(); i++)
    {
        Normals[i] = FVector::ZeroVector;
    }

    // Calculate face normals and accumulate
    for (int32 i = 0; i < Triangles.Num(); i += 3)
    {
        if (Triangles[i] < Vertices.Num() && Triangles[i + 1] < Vertices.Num() && Triangles[i + 2] < Vertices.Num())
        {
            FVector V0 = Vertices[Triangles[i]];
            FVector V1 = Vertices[Triangles[i + 1]];
            FVector V2 = Vertices[Triangles[i + 2]];

            FVector FaceNormal = FVector::CrossProduct(V1 - V0, V2 - V0).GetSafeNormal();

            Normals[Triangles[i]] += FaceNormal;
            Normals[Triangles[i + 1]] += FaceNormal;
            Normals[Triangles[i + 2]] += FaceNormal;
        }
    }

    // Normalize accumulated normals
    for (FVector& Normal : Normals)
    {
        Normal = Normal.GetSafeNormal();
    }
}

void UUnrealMCPProceduralMeshCommands::GenerateUVsForMesh(const TArray<FVector>& Vertices, TArray<FVector2D>& UVs)
{
    UVs.SetNum(Vertices.Num());

    // Find bounding box for UV mapping
    FVector MinBounds = Vertices[0];
    FVector MaxBounds = Vertices[0];

    for (const FVector& Vertex : Vertices)
    {
        MinBounds = FVector::Min(MinBounds, Vertex);
        MaxBounds = FVector::Max(MaxBounds, Vertex);
    }

    FVector Size = MaxBounds - MinBounds;

    // Generate planar UV coordinates
    for (int32 i = 0; i < Vertices.Num(); i++)
    {
        FVector RelativePos = Vertices[i] - MinBounds;
        UVs[i] = FVector2D(
            Size.X > 0 ? RelativePos.X / Size.X : 0.0f,
            Size.Y > 0 ? RelativePos.Y / Size.Y : 0.0f
        );
    }
}

void UUnrealMCPProceduralMeshCommands::PopulateMeshDescription(FMeshDescription& MeshDescription, const TArray<FVector>& Vertices, const TArray<int32>& Triangles, const TArray<FVector>& Normals, const TArray<FVector2D>& UVs)
{
    // Get mesh attributes
    FStaticMeshAttributes Attributes(MeshDescription);
    TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();
    TVertexInstanceAttributesRef<FVector3f> VertexInstanceNormals = Attributes.GetVertexInstanceNormals();
    TVertexInstanceAttributesRef<FVector2f> VertexInstanceUVs = Attributes.GetVertexInstanceUVs();
    TPolygonGroupAttributesRef<FName> PolygonGroupImportedMaterialSlotNames = Attributes.GetPolygonGroupMaterialSlotNames();

    // Reserve space
    MeshDescription.ReserveNewVertices(Vertices.Num());
    MeshDescription.ReserveNewVertexInstances(Triangles.Num());
    MeshDescription.ReserveNewPolygons(Triangles.Num() / 3);
    MeshDescription.ReserveNewEdges(Triangles.Num());

    // Create polygon group
    FPolygonGroupID PolygonGroupID = MeshDescription.CreatePolygonGroup();
    PolygonGroupImportedMaterialSlotNames[PolygonGroupID] = TEXT("DefaultMaterial");

    // Add vertices
    TArray<FVertexID> VertexIDs;
    VertexIDs.Reserve(Vertices.Num());
    for (int32 i = 0; i < Vertices.Num(); i++)
    {
        FVertexID VertexID = MeshDescription.CreateVertex();
        VertexPositions[VertexID] = FVector3f(Vertices[i]);
        VertexIDs.Add(VertexID);
    }

    // Add triangles
    for (int32 i = 0; i < Triangles.Num(); i += 3)
    {
        TArray<FVertexInstanceID> VertexInstanceIDs;
        VertexInstanceIDs.Reserve(3);

        for (int32 j = 0; j < 3; j++)
        {
            int32 VertexIndex = Triangles[i + j];
            if (VertexIndex < VertexIDs.Num())
            {
                FVertexInstanceID VertexInstanceID = MeshDescription.CreateVertexInstance(VertexIDs[VertexIndex]);

                // Set normal
                if (VertexIndex < Normals.Num())
                {
                    VertexInstanceNormals[VertexInstanceID] = FVector3f(Normals[VertexIndex]);
                }
                else
                {
                    VertexInstanceNormals[VertexInstanceID] = FVector3f::UpVector;
                }

                // Set UV
                if (VertexIndex < UVs.Num())
                {
                    VertexInstanceUVs.Set(VertexInstanceID, 0, FVector2f(UVs[VertexIndex]));
                }
                else
                {
                    VertexInstanceUVs.Set(VertexInstanceID, 0, FVector2f::ZeroVector);
                }

                VertexInstanceIDs.Add(VertexInstanceID);
            }
        }

        if (VertexInstanceIDs.Num() == 3)
        {
            MeshDescription.CreatePolygon(PolygonGroupID, VertexInstanceIDs);
        }
    }
}

TSharedPtr<FDynamicMesh3> UUnrealMCPProceduralMeshCommands::GeneratePortalGeometry(int32 SourceLayer, int32 TargetLayer)
{
    // REAL IMPLEMENTATION - Generate portal geometry using modern UE 5.6.1 APIs
    TSharedPtr<FDynamicMesh3> PortalMesh = MakeShared<FDynamicMesh3>();

    // Create portal using DiscMeshGenerator for circular portal
    UE::Geometry::FDiscMeshGenerator DiscGen;
    DiscGen.Radius = 150.0f + (FMath::Abs(TargetLayer - SourceLayer) * 25.0f);
    DiscGen.AngleSamples = 32;
    DiscGen.RadialSamples = 8;
    DiscGen.bReverseOrientation = false;

    *PortalMesh = FDynamicMesh3(&DiscGen.Generate());

    // Calculate normals using modern UE 5.6.1 APIs
    UE::Geometry::FMeshNormals::QuickComputeVertexNormals(*PortalMesh);

    return PortalMesh;
}

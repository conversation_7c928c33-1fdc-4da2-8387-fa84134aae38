"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AuracronEditor/Development/Slate/SharedPCH.Slate.Cpp20.cpp"
/I "."
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/Core/Public"
/I "Runtime/Core/Internal"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/CoreUObject/Internal"
/I "Runtime/CorePreciseFP/Public"
/I "Runtime/CorePreciseFP/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/ApplicationCore/Internal"
/I "Runtime/RHI/Public"
/I "Runtime/RHI/Internal"
/I "Runtime/ImageWrapper/Public"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"
/Yc"SharedPCH.Slate.Cpp20.h"
/Fp"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AuracronEditor/Development/Slate/SharedPCH.Slate.Cpp20.h.pch"
/Fo"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AuracronEditor/Development/Slate/SharedPCH.Slate.Cpp20.h.obj"
/experimental:log "C:/Game/AURACRON/Intermediate/Build/Win64/x64/AuracronEditor/Development/Slate/SharedPCH.Slate.Cpp20.h.sarif"
/sourceDependencies "C:/Game/AURACRON/Intermediate/Build/Win64/x64/AuracronEditor/Development/Slate/SharedPCH.Slate.Cpp20.h.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR-
/std:c++20
/Zc:preprocessor
/wd5054
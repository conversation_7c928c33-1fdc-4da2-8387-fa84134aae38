"""
Procedural Mesh Tools for Unreal MCP.

This module provides tools for creating and manipulating procedural meshes in Unreal Engine.
Specifically designed for Auracron's MOBA geometry including lanes, towers, and structures.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_procedural_mesh_tools(mcp: FastMCP):
    """Register Procedural Mesh tools with the MCP server."""
    
    @mcp.tool()
    def create_lane_geometry(
        ctx: Context,
        lane_name: str,
        lane_type: str,
        start_location: Dict[str, float],
        end_location: Dict[str, float],
        lane_width: float = 800.0,
        geometry_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create lane geometry for MOBA gameplay using modern UE 5.6.1 APIs.
        
        Args:
            lane_name: Name of the lane to create
            lane_type: Type of lane (top, middle, bottom, jungle)
            start_location: Starting location {x, y, z}
            end_location: Ending location {x, y, z}
            lane_width: Width of the lane (default: 800.0)
            geometry_settings: Advanced geometry settings:
                - segments: Number of segments along the lane
                - elevation_curve: Elevation curve for the lane
                - side_barriers: Create side barriers
                - material_zones: Different material zones
        
        Returns:
            Dict containing success status and lane creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "lane_name": lane_name,
                "lane_type": lane_type,
                "start_location": start_location,
                "end_location": end_location,
                "lane_width": lane_width
            }
            
            if geometry_settings:
                params["geometry_settings"] = geometry_settings
            
            logger.info(f"Creating lane geometry: {lane_name} ({lane_type})")
            
            response = unreal.send_command("create_lane_geometry", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Lane geometry creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating lane geometry: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_jungle_structures(
        ctx: Context,
        structure_name: str,
        structure_type: str,
        location: Dict[str, float],
        layer_index: int,
        structure_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create jungle structures with procedural generation.
        
        Args:
            structure_name: Name of the structure to create
            structure_type: Type of structure (camp, monster_den, resource_node)
            location: Structure location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            structure_settings: Structure-specific settings:
                - size_variation: Size variation range
                - detail_level: Level of detail for the structure
                - vegetation: Add vegetation around structure
                - defensive_elements: Add defensive elements
        
        Returns:
            Dict containing success status and structure creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "structure_name": structure_name,
                "structure_type": structure_type,
                "location": location,
                "layer_index": layer_index
            }
            
            if structure_settings:
                params["structure_settings"] = structure_settings
            
            logger.info(f"Creating jungle structure: {structure_name} ({structure_type}) on layer {layer_index}")
            
            response = unreal.send_command("create_jungle_structures", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Jungle structure creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating jungle structure: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_tower_meshes(
        ctx: Context,
        tower_name: str,
        tower_type: str,
        location: Dict[str, float],
        layer_index: int,
        tower_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create tower meshes with layer-specific designs.
        
        Args:
            tower_name: Name of the tower to create
            tower_type: Type of tower (basic, advanced, nexus, inhibitor)
            location: Tower location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            tower_settings: Tower-specific settings:
                - height: Tower height
                - radius: Tower base radius
                - defensive_features: Add defensive features
                - visual_effects: Add visual effects
        
        Returns:
            Dict containing success status and tower creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "tower_name": tower_name,
                "tower_type": tower_type,
                "location": location,
                "layer_index": layer_index
            }
            
            if tower_settings:
                params["tower_settings"] = tower_settings
            
            logger.info(f"Creating tower mesh: {tower_name} ({tower_type}) on layer {layer_index}")
            
            response = unreal.send_command("create_tower_meshes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Tower mesh creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating tower mesh: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_base_architecture(
        ctx: Context,
        base_name: str,
        base_type: str,
        location: Dict[str, float],
        team_side: str,
        architecture_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create base architecture with team-specific designs.
        
        Args:
            base_name: Name of the base structure
            base_type: Type of base (nexus, fountain, barracks)
            location: Base location {x, y, z}
            team_side: Team side (blue, red)
            architecture_settings: Architecture-specific settings:
                - architectural_style: Style of architecture
                - defensive_walls: Add defensive walls
                - decorative_elements: Add decorative elements
                - team_colors: Apply team colors
        
        Returns:
            Dict containing success status and base creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "base_name": base_name,
                "base_type": base_type,
                "location": location,
                "team_side": team_side
            }
            
            if architecture_settings:
                params["architecture_settings"] = architecture_settings
            
            logger.info(f"Creating base architecture: {base_name} ({base_type}) for {team_side} team")
            
            response = unreal.send_command("create_base_architecture", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Base architecture creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating base architecture: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_portal_geometry(
        ctx: Context,
        portal_name: str,
        portal_type: str,
        source_location: Dict[str, float],
        target_location: Dict[str, float],
        portal_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create portal geometry for layer transitions.
        
        Args:
            portal_name: Name of the portal
            portal_type: Type of portal (elevator, teleporter, bridge)
            source_location: Source location {x, y, z}
            target_location: Target location {x, y, z}
            portal_settings: Portal-specific settings:
                - portal_size: Size of the portal
                - visual_effects: Visual effects for the portal
                - activation_method: How portal is activated
                - transition_time: Time for transition
        
        Returns:
            Dict containing success status and portal creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "portal_name": portal_name,
                "portal_type": portal_type,
                "source_location": source_location,
                "target_location": target_location
            }
            
            if portal_settings:
                params["portal_settings"] = portal_settings
            
            logger.info(f"Creating portal geometry: {portal_name} ({portal_type})")
            
            response = unreal.send_command("create_portal_geometry", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Portal geometry creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating portal geometry: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_bridge_meshes(
        ctx: Context,
        bridge_name: str,
        bridge_type: str,
        start_location: Dict[str, float],
        end_location: Dict[str, float],
        bridge_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create bridge meshes for connecting different areas.
        
        Args:
            bridge_name: Name of the bridge
            bridge_type: Type of bridge (stone, magical, dimensional)
            start_location: Start location {x, y, z}
            end_location: End location {x, y, z}
            bridge_settings: Bridge-specific settings:
                - bridge_width: Width of the bridge
                - support_pillars: Add support pillars
                - decorative_railings: Add decorative railings
                - magical_effects: Add magical effects
        
        Returns:
            Dict containing success status and bridge creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "bridge_name": bridge_name,
                "bridge_type": bridge_type,
                "start_location": start_location,
                "end_location": end_location
            }
            
            if bridge_settings:
                params["bridge_settings"] = bridge_settings
            
            logger.info(f"Creating bridge mesh: {bridge_name} ({bridge_type})")
            
            response = unreal.send_command("create_bridge_meshes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Bridge mesh creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating bridge mesh: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

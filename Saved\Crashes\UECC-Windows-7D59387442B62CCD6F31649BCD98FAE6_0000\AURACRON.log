﻿Log file open, 08/29/25 08:31:28
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=22892)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 38005
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/Game/AURACRON/AURACRON.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=0ad3379138a0463681ea6cd2bd89208f -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.292653
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-7D59387442B62CCD6F31649BCD98FAE6
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AuracronEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.07 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.07 seconds
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AuracronEditor.target
LogConfig: Display: Loading Android ini files took 0.07 seconds
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogConfig: Display: Loading TVOS ini files took 0.08 seconds
LogConfig: Display: Loading Windows ini files took 0.08 seconds
LogAssetRegistry: Display: Asset registry cache read as 74.0 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogConfig: Display: Loading Linux ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin LandscapePatch
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PCGBiomeCore
LogPluginManager: Mounting Engine plugin PCGBiomeSample
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin MLAdapter
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=0ad3379138a0463681ea6cd2bd89208f -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.29-11.31.28:964][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.29-11.31.28:964][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.29-11.31.28:965][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.29-11.31.28:965][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.29-11.31.28:966][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.29-11.31.28:966][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.29-11.31.28:971][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-11.31.28:971][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-11.31.28:971][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-11.31.28:974][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.29-11.31.28:974][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-11.31.29:050][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.29-11.31.29:071][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.29-11.31.29:071][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.29-11.31.29:071][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.29-11.31.29:071][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-11.31.29:071][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.29-11.31.29:076][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.29-11.31.29:076][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.29-11.31.29:076][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.29-11.31.29:076][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.29-11.31.29:076][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.29-11.31.29:076][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-11.31.29:076][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-11.31.29:076][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-11.31.29:077][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-11.31.29:077][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.29-11.31.29:077][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.29-11.31.29:077][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.29-11.31.29:077][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.31.29:077][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.29-11.31.29:282][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.31.29:282][  0]LogD3D11RHI:     AdapterLuid : 0 86560
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.29-11.31.29:285][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.31.29:285][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.31.29:285][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.29-11.31.29:285][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-11.31.29:285][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.29-11.31.29:285][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.29-11.31.29:286][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.29-11.31.29:286][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.31.29:286][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.29-11.31.29:286][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.29-11.31.29:286][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-11.31.29:286][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.29-11.31.29:286][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.29-11.31.29:286][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.29-11.31.29:286][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.29-11.31.29:286][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.29-11.31.29:286][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-11.31.29:286][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.29-11.31.29:286][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.29-11.31.29:286][  0]LogInit: Computer: TKT
[2025.08.29-11.31.29:286][  0]LogInit: User: tktca
[2025.08.29-11.31.29:286][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.29-11.31.29:286][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.29-11.31.29:286][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.29-11.31.29:286][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=37.6GB
[2025.08.29-11.31.29:286][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.29-11.31.29:286][  0]LogMemory: Process Physical Memory: 645.93 MB used, 709.02 MB peak
[2025.08.29-11.31.29:286][  0]LogMemory: Process Virtual Memory: 651.43 MB used, 694.84 MB peak
[2025.08.29-11.31.29:286][  0]LogMemory: Physical Memory: 21305.13 MB used,  11147.57 MB free, 32452.70 MB total
[2025.08.29-11.31.29:286][  0]LogMemory: Virtual Memory: 27901.92 MB used,  10588.82 MB free, 38490.75 MB total
[2025.08.29-11.31.29:286][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.29-11.31.29:288][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.29-11.31.29:290][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.29-11.31.29:290][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.29-11.31.29:290][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.29-11.31.29:290][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.29-11.31.29:296][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.29-11.31.29:575][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.29-11.31.29:660][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.29-11.31.29:660][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.29-11.31.29:660][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.29-11.31.29:660][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.29-11.31.29:664][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.29-11.31.29:666][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.29-11.31.29:671][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.29-11.31.29:671][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.29-11.31.29:768][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-11.31.29:768][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-11.31.29:768][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-11.31.29:768][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-11.31.29:768][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-11.31.29:768][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-11.31.29:768][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-11.31.29:768][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-11.31.29:768][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-11.31.29:769][  0]LogWindows: Attached monitors:
[2025.08.29-11.31.29:769][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY1' [PRIMARY]
[2025.08.29-11.31.29:769][  0]LogWindows: Found 1 attached monitors.
[2025.08.29-11.31.29:769][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.29-11.31.29:769][  0]LogRHI: RHI Adapter Info:
[2025.08.29-11.31.29:769][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.29:769][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-11.31.29:769][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.29-11.31.29:769][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.29-11.31.29:769][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.29-11.31.29:769][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.29-11.31.29:769][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.31.29:769][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.31.29:770][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.31.29:770][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.31.29:770][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.31.29:951][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.29-11.31.29:975][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.29-11.31.29:975][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.29-11.31.29:976][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.29-11.31.29:976][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.29-11.31.29:976][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.29-11.31.29:976][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.29-11.31.29:976][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.29-11.31.29:991][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.29-11.31.29:994][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.29-11.31.29:994][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.29-11.31.29:994][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.29-11.31.29:994][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.29-11.31.29:994][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.29-11.31.29:996][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all'
[2025.08.29-11.31.29:996][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all" ]
[2025.08.29-11.31.30:007][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.29-11.31.30:007][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.29-11.31.30:007][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.29-11.31.30:007][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.29-11.31.30:007][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.29-11.31.30:007][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.29-11.31.30:007][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.29-11.31.30:007][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.29-11.31.30:008][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.29-11.31.30:008][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.29-11.31.30:035][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.29-11.31.30:035][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.29-11.31.30:035][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.29-11.31.30:035][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.29-11.31.30:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.29-11.31.30:047][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.29-11.31.30:047][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.29-11.31.30:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.29-11.31.30:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.29-11.31.30:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.29-11.31.30:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.29-11.31.30:075][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.29-11.31.30:075][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.29-11.31.30:075][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.29-11.31.30:075][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.29-11.31.30:087][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.29-11.31.30:087][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.29-11.31.30:102][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.29-11.31.30:102][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.29-11.31.30:102][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.29-11.31.30:102][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.29-11.31.30:102][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.29-11.31.30:135][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.29-11.31.30:141][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.29-11.31.30:141][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.29-11.31.30:144][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.29-11.31.30:144][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.29-11.31.30:144][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.29-11.31.30:144][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-11.31.30:144][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.29-11.31.30:363][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.29-11.31.30:363][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-11.31.30:363][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.29-11.31.30:364][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.29-11.31.30:364][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-11.31.30:365][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-11.31.30:365][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.29-11.31.30:469][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.29-11.31.30:469][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.105 seconds
[2025.08.29-11.31.30:472][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.29-11.31.30:483][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.29-11.31.30:483][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=798.98MBs, RandomWriteSpeed=102.43MBs. Assigned SpeedClass 'Local'
[2025.08.29-11.31.30:484][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.29-11.31.30:485][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.29-11.31.30:485][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/28852/).
[2025.08.29-11.31.30:486][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/2556C418425867E1C73728831AC71CB5/'.
[2025.08.29-11.31.30:486][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.29-11.31.30:486][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.29-11.31.30:487][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.29-11.31.30:488][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.29-11.31.30:488][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.29-11.31.31:838][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.29-11.31.32:204][  0]LogSlate: Using FreeType 2.10.0
[2025.08.29-11.31.32:205][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.29-11.31.32:208][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.29-11.31.32:208][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.29-11.31.32:208][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.29-11.31.32:208][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.29-11.31.32:225][  0]LogAssetRegistry: FAssetRegistry took 0.0025 seconds to start up
[2025.08.29-11.31.32:227][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.29-11.31.32:292][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.29-11.31.32:510][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.29-11.31.32:510][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.29-11.31.32:542][  0]LogDeviceProfileManager: Active device profile: [00000223018D3200][00000222F9927800 66] WindowsEditor
[2025.08.29-11.31.32:542][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.29-11.31.32:545][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.29-11.31.32:546][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.29-11.31.32:547][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.29-11.31.32:547][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.29-11.31.32:553][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.29-11.31.32:553][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.29-11.31.32:553][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.29-11.31.32:554][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT'
[2025.08.29-11.31.32:554][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.29-11.31.32:583][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:584][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.29-11.31.32:584][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:584][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:585][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.29-11.31.32:585][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:585][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:587][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.29-11.31.32:587][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.29-11.31.32:589][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.29-11.31.32:590][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:591][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:592][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:593][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.29-11.31.32:593][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.29-11.31.32:595][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.29-11.31.32:595][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:628][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.29-11.31.32:628][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:643][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.29-11.31.32:643][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:657][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.29-11.31.32:657][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.31.32:775][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.29-11.31.32:775][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.29-11.31.32:775][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.29-11.31.32:775][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.29-11.31.32:775][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.29-11.31.33:269][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.29-11.31.33:306][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.29-11.31.33:307][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.29-11.31.33:307][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.29-11.31.33:307][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.29-11.31.33:310][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.29-11.31.33:310][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.29-11.31.33:311][  0]LogLiveCoding: Display: First instance in process group "UE_AURACRON_0xa5ca6502", spawning console
[2025.08.29-11.31.33:315][  0]LogLiveCoding: Display: Waiting for server
[2025.08.29-11.31.33:332][  0]LogSlate: Border
[2025.08.29-11.31.33:332][  0]LogSlate: BreadcrumbButton
[2025.08.29-11.31.33:332][  0]LogSlate: Brushes.Title
[2025.08.29-11.31.33:332][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.29-11.31.33:332][  0]LogSlate: Default
[2025.08.29-11.31.33:332][  0]LogSlate: Icons.Save
[2025.08.29-11.31.33:332][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.29-11.31.33:332][  0]LogSlate: ListView
[2025.08.29-11.31.33:332][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.29-11.31.33:332][  0]LogSlate: SoftwareCursor_Grab
[2025.08.29-11.31.33:332][  0]LogSlate: TableView.DarkRow
[2025.08.29-11.31.33:332][  0]LogSlate: TableView.Row
[2025.08.29-11.31.33:332][  0]LogSlate: TreeView
[2025.08.29-11.31.33:427][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.29-11.31.33:430][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 3.332 ms
[2025.08.29-11.31.33:451][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.29-11.31.33:451][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.29-11.31.33:451][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.29-11.31.33:488][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.29-11.31.33:905][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-11.31.33:905][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.33:966][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT: Interface availability:
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   RDG: no
[2025.08.29-11.31.33:993][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.29-11.31.34:076][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-11.31.34:076][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.31.34:332][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.29-11.31.34:332][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.29-11.31.34:348][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.29-11.31.34:411][  0]LogMLAdapter: Warning: Neural network asset data not set
[2025.08.29-11.31.34:474][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 60FFC20853C647218000000000004700 | Instance: 47B6EA414557715A0DE9CCB11085DA44 (TKT-28852).
[2025.08.29-11.31.34:517][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.29-11.31.34:521][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.29-11.31.34:521][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.29-11.31.34:522][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:61385'.
[2025.08.29-11.31.34:526][  0]LogUdpMessaging: Display: Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
[2025.08.29-11.31.34:526][  0]LogUdpMessaging: Display: Added local interface '172.17.96.1' to multicast group '230.0.0.1:6666'
[2025.08.29-11.31.34:529][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.29-11.31.34:534][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.29-11.31.34:560][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.29-11.31.34:560][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.29-11.31.34:566][  0]LogTimingProfiler: Initialize
[2025.08.29-11.31.34:566][  0]LogTimingProfiler: OnSessionChanged
[2025.08.29-11.31.34:566][  0]LoadingProfiler: Initialize
[2025.08.29-11.31.34:566][  0]LoadingProfiler: OnSessionChanged
[2025.08.29-11.31.34:566][  0]LogNetworkingProfiler: Initialize
[2025.08.29-11.31.34:566][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.29-11.31.34:566][  0]LogMemoryProfiler: Initialize
[2025.08.29-11.31.34:566][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.29-11.31.34:605][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.29-11.31.34:851][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.29-11.31.34:858][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.29-11.31.34:861][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-11.31.34:861][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-11.31.34:921][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.31.34:930][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.29-11.31.34:930][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.29-11.31.34:930][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.29-11.31.34:930][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.29-11.31.34:935][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.31.34:965][  0]LogCollectionManager: Loaded 0 collections in 0.001321 seconds
[2025.08.29-11.31.34:967][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.00s
[2025.08.29-11.31.34:970][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.29-11.31.34:972][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.00s
[2025.08.29-11.31.35:009][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.29-11.31.35:021][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.29-11.31.35:026][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.29-11.31.35:026][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.29-11.31.35:026][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.29-11.31.35:026][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.29-11.31.35:049][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.29-11.31.35:049][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.29-11.31.35:049][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.29-11.31.35:049][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.29-11.31.35:061][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-08-29T11:31:35.061Z using C
[2025.08.29-11.31.35:062][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.26100.4768.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=AURACRON, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.08.29-11.31.35:062][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.29-11.31.35:062][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.08.29-11.31.35:067][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.08.29-11.31.35:067][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.08.29-11.31.35:067][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.08.29-11.31.35:067][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000061
[2025.08.29-11.31.35:107][  0]LogUObjectArray: 45950 objects as part of root set at end of initial load.
[2025.08.29-11.31.35:107][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.29-11.31.35:217][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.31.35:217][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.31.35:217][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.31.35:218][  0]LogEngine: Initializing Engine...
[2025.08.29-11.31.35:324][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.29-11.31.35:325][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.29-11.31.35:325][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.29-11.31.35:325][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.29-11.31.35:325][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.29-11.31.35:329][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.29-11.31.35:574][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.29-11.31.35:597][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.29-11.31.35:613][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.29-11.31.35:631][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.08.29-11.31.35:631][  0]LogInit: Texture streaming: Enabled
[2025.08.29-11.31.35:642][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.29-11.31.35:648][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.29-11.31.35:659][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.29-11.31.35:659][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.29-11.31.35:661][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.29-11.31.35:661][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.29-11.31.35:661][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.29-11.31.35:661][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.29-11.31.35:661][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.29-11.31.35:661][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.29-11.31.35:661][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.29-11.31.35:671][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.29-11.31.36:139][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.29-11.31.36:140][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.29-11.31.36:141][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.29-11.31.36:141][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.29-11.31.36:143][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.29-11.31.36:143][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.29-11.31.36:146][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.29-11.31.36:146][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.29-11.31.36:146][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.29-11.31.36:146][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.29-11.31.36:146][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.29-11.31.36:151][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.29-11.31.36:158][  0]LogInit: Undo buffer set to 256 MB
[2025.08.29-11.31.36:158][  0]LogInit: Transaction tracking system initialized
[2025.08.29-11.31.36:169][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.29-11.31.36:209][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.29-11.31.36:372][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/' took 0.00s
[2025.08.29-11.31.36:417][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.29-11.31.36:429][  0]LogMLAdapter: Creating MLAdapter manager of class MLAdapterManager
[2025.08.29-11.31.36:429][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.29-11.31.36:430][  0]LogPython: Using Python 3.11.8
[2025.08.29-11.31.36:464][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.29-11.31.37:216][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.29-11.31.37:235][  0]LogEditorDataStorage: Initializing
[2025.08.29-11.31.37:238][  0]LogEditorDataStorage: Initialized
[2025.08.29-11.31.37:241][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.29-11.31.37:244][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.29-11.31.37:320][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.29-11.31.37:329][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.29-11.31.37:333][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.31.37:333][  0]LogUnrealEdMisc: Loading editor; pre map load, took 9.092
[2025.08.29-11.31.37:335][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.29-11.31.37:336][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.29-11.31.37:338][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.29-11.31.37:356][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.29-11.31.37:359][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.08.29-11.31.37:367][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.29-11.31.37:367][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.29-11.31.37:369][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.29-11.31.37:369][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.29-11.31.37:369][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.29-11.31.37:497][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.31.37:503][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.31.37:509][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.31.37:512][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.29-11.31.37:512][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.29-11.31.37:512][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.31.37:518][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.29-11.31.37:648][  0]LogWorldPartition: Display: WorldPartition initialize took 279.691 ms
[2025.08.29-11.31.37:794][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.29-11.31.37:811][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.46ms
[2025.08.29-11.31.37:811][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.29-11.31.37:813][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 1,196ms para ser concluída.
[2025.08.29-11.31.37:821][  0]LogUnrealEdMisc: Total Editor Startup Time, took 9.580
[2025.08.29-11.31.37:968][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.29-11.31.38:027][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.29-11.31.38:358][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.31.38:636][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.31.38:922][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.31.39:166][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.31.39:526][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:527][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.29-11.31.39:527][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:529][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.29-11.31.39:529][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:531][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.29-11.31.39:531][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:532][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.29-11.31.39:533][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:533][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.29-11.31.39:535][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:536][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.29-11.31.39:537][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:537][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.29-11.31.39:538][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:538][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.29-11.31.39:538][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:540][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.29-11.31.39:540][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.31.39:542][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.29-11.31.40:048][  0]LogAssetRegistry: Display: Asset registry cache written as 74.0 MiB to ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin
[2025.08.29-11.31.40:677][  0]LogSlate: Took 0.000335 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.29-11.31.40:682][  0]LogSlate: Took 0.000151 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.29-11.31.40:735][  0]LogSlate: Took 0.000297 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.29-11.31.40:857][  0]LogStall: Startup...
[2025.08.29-11.31.40:860][  0]LogStall: Startup complete.
[2025.08.29-11.31.40:865][  0]LogLoad: (Engine Initialization) Total time: 12.62 seconds
[2025.08.29-11.31.41:495][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.29-11.31.41:495][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.29-11.31.41:546][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.29-11.31.41:548][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.29-11.31.41:582][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.29-11.31.41:595][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 47.560 ms
[2025.08.29-11.31.41:857][  1]LogAssetRegistry: AssetRegistryGather time 0.1580s: AssetDataDiscovery 0.0180s, AssetDataGather 0.0461s, StoreResults 0.0940s. Wall time 9.6340s.
	NumCachedDirectories 1647. NumUncachedDirectories 23. NumCachedFiles 7994. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.08.29-11.31.41:882][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.29-11.31.41:891][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.29-11.31.41:891][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.29-11.31.42:088][  2]LogSourceControl: Uncontrolled asset discovery finished in 0.196732 seconds (Found 7970 uncontrolled assets)
[2025.08.29-11.31.42:858][  5]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 7.463349
[2025.08.29-11.31.42:860][  5]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.08.29-11.31.42:861][  6]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7.791625
[2025.08.29-11.31.44:193][  9]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.29-11.31.44:859][ 11]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 9.460468
[2025.08.29-11.31.44:862][ 11]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 1782979643
[2025.08.29-11.31.44:862][ 12]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9.460468, Update Interval: 352.274536
[2025.08.29-11.33.10:638][283]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.29-11.33.10:638][283]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.29-11.33.10:638][283]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.29-11.33.10:739][283]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.29-11.33.10:739][283]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.29-11.33.10:739][283]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_map", "params": {"map_name": "AURACRON_Revolutionary_Map", "layers": [{"name": "Planicie_Radiante", "height_offset": 1000, "layer_type": "ground", "streaming_distance": 5000, "description": "Camada inferior com terreno verdejante e estruturas radiantes"}, {"name": "Firmamento_Zefiro", "height_offset": 3000, "layer_type": "aerial", "streaming_distance": 6000, "description": "Camada intermedi\u00e1ria et\u00e9rea com plataformas flutuantes"}, {"name": "Abismo_Umbral", "height_offset": 5000, "layer_type": "underground", "streaming_distance": 4500, "description": "Camada superior sombria com arquitetura g\u00f3tica"}], "world_partition_settings": {"enable_streaming": true, "cell_size": 2000, "loading_range": 8000, "enable_hlod": true}}}
[2025.08.29-11.33.10:739][283]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_map
[2025.08.29-11.33.10:742][283]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/Maps/AURACRON_Revolutionary_Map'
[2025.08.29-11.33.10:742][283]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/Maps/AURACRON_Revolutionary_Map', Physical: '../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.uasset'
[2025.08.29-11.33.10:742][283]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON_Revolutionary_Map
[2025.08.29-11.33.10:746][283]LogTemp: AURACRON MEMORY FIX: World configured without AddToRoot() - letting engine manage lifecycle
[2025.08.29-11.33.10:747][283]LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
[2025.08.29-11.33.10:747][283]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: WaitingForIo) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.33.10:748][283]LogStreaming: Display: Package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced is adding a dynamic import to package /Engine/Maps/Templates/HLODs/HLODLayer_Merged because of a recursive sync load
[2025.08.29-11.33.10:748][283]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: DeferredPostLoad) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.33.10:748][283]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.29-11.33.10:748][283]LogWorldPartition: UWorldPartition::Initialize : World = /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.29-11.33.10:748][283]LogWorldPartition: Display: WorldPartition initialize took 232 us (total: 279.924 ms)
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] WorldPartition initialized successfully
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] WorldPartition streaming enabled
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] Configuring level for Data Layers support
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] WorldPartition found - External Objects should be automatically enabled
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] External Objects configuration attempted
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] External Objects support verified through WorldPartition
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] External Objects check via PersistentLevel: Not Supported
[2025.08.29-11.33.10:748][283]LogTemp: Warning: [MapSystem] Attempting to force enable External Actors...
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] External Objects confirmed - proceeding with Data Layer creation
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] Creating Data Layer: Planicie_Radiante (Index: 0)
[2025.08.29-11.33.10:748][283]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante
[2025.08.29-11.33.10:748][283]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante'
[2025.08.29-11.33.10:748][283]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.33.10:749][283]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.33.10:758][283]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante
[2025.08.29-11.33.10:758][283]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_Plani4199B399449F98C696295FA1CAD08330.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.33.10:758][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Planicie_Radiante
[2025.08.29-11.33.10:759][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Planicie_Radiante
[2025.08.29-11.33.10:759][283]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Planicie_Radiante
[2025.08.29-11.33.10:759][283]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Planicie_Radiante (Height: 1000.0, Type: ground, State: Activated)
[2025.08.29-11.33.10:759][283]LogTemp: [MapSystem] Creating Data Layer: Firmamento_Zefiro (Index: 1)
[2025.08.29-11.33.10:759][283]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro
[2025.08.29-11.33.10:759][283]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro'
[2025.08.29-11.33.10:759][283]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro.uasset'
[2025.08.29-11.33.10:759][283]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro.uasset'
[2025.08.29-11.33.10:767][283]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro
[2025.08.29-11.33.10:767][283]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_Firma162BE0E349F769C6FC0230A7BD5A381D.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro.uasset'
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Firmamento_Zefiro
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Firmamento_Zefiro
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Firmamento_Zefiro
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Firmamento_Zefiro (Height: 3000.0, Type: aerial, State: Loaded)
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] Creating Data Layer: Abismo_Umbral (Index: 2)
[2025.08.29-11.33.10:768][283]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral
[2025.08.29-11.33.10:768][283]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral'
[2025.08.29-11.33.10:768][283]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.33.10:768][283]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.33.10:777][283]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral
[2025.08.29-11.33.10:777][283]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_AbismB456B55344840957C938FCBA3ABA6A95.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Abismo_Umbral
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Abismo_Umbral
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Abismo_Umbral
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Abismo_Umbral (Height: 5000.0, Type: underground, State: Loaded)
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] WorldPartition streaming configured via subsystem
[2025.08.29-11.33.10:778][283]LogTemp: [MapSystem] World Partition configured SAFELY with 3 Data Layers (Created: 3, Valid Instances: 3)
[2025.08.29-11.33.10:778][283]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Planicie_Radiante - SKIPPING to prevent assertion failure
[2025.08.29-11.33.10:778][283]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Firmamento_Zefiro - SKIPPING to prevent assertion failure
[2025.08.29-11.33.10:778][283]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Abismo_Umbral - SKIPPING to prevent assertion failure
[2025.08.29-11.33.10:778][283]LogTemp: SpawnInitialLayerActors: Total actors spawned: 0
[2025.08.29-11.33.10:778][283]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.29-11.33.10:843][283]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/AURACRON_Revolutionary_Map" FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.29-11.33.10:893][283]LogUObjectHash: Compacting FUObjectHashTables data took   0.57ms
[2025.08.29-11.33.10:904][283]LogSavePackage: Moving output files for package: /Game/Maps/AURACRON_Revolutionary_Map
[2025.08.29-11.33.10:904][283]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map09EF216D4CBE7E2B6613E48B36A7C87E.tmp' to '../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.umap'
[2025.08.29-11.33.10:910][283]LogFileHelpers: Saving map 'AURACRON_Revolutionary_Map' took 0.067
[2025.08.29-11.33.10:925][283]LogFileHelpers: InternalPromptForCheckoutAndSave took 146.379 ms
[2025.08.29-11.33.10:925][283]LogTemp: AURACRON: DataLayer Planicie_Radiante accessed SAFELY
[2025.08.29-11.33.10:925][283]LogTemp: AURACRON: DataLayer Firmamento_Zefiro accessed SAFELY
[2025.08.29-11.33.10:925][283]LogTemp: AURACRON: DataLayer Abismo_Umbral accessed SAFELY
[2025.08.29-11.33.10:925][283]LogTemp: [MapSystem] Multilayer map created successfully:
[2025.08.29-11.33.10:925][283]LogTemp:   - Map Name: AURACRON_Revolutionary_Map
[2025.08.29-11.33.10:925][283]LogTemp:   - Main Asset: /Game/Maps/AURACRON_Revolutionary_Map
[2025.08.29-11.33.10:925][283]LogTemp:   - Layers Created: 3
[2025.08.29-11.33.10:925][283]LogTemp:   - World Partition: Enabled
[2025.08.29-11.33.10:925][283]LogTemp:   - Saved to Disk: SUCCESS
[2025.08.29-11.33.10:925][283]LogTemp: AURACRON MEMORY FIX: Letting engine handle World cleanup naturally
[2025.08.29-11.33.10:925][283]LogTemp: AURACRON MEMORY FIX: WorldFactory reference cleared (RF_Transient will auto-cleanup)
[2025.08.29-11.33.10:947][283]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.08.29-11.33.10:948][283]LogTemp: AURACRON MEMORY FIX: Complete garbage collection and memory cleanup completed successfully
[2025.08.29-11.33.10:948][283]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"command": "create_multilayer_map",
		"map_name": "AURACRON_Revolutionary_Map",
		"main_asset_path": "/Game/Maps/AURACRON_Revolutionary_Map",
		"saved_to_disk": true,
		"full_disk_path": "../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.umap",
		"size_x": 10000,
		"size_y": 10000,
		"actors_spawned": 0,
		"layers_created": [
			{
				"name": "Planicie_Radiante",
				"height_offset": 1000,
				"layer_type": "ground",
				"streaming_distance": 5000,
				"description": "Camada inferior com terreno verdejante e estruturas radiantes",
				"data_layer_name": "Planicie_Radiante",
				"is_visible": true,
				"is_initially_visible": true,
				"runtime_state": "Activated",
				"data_layer_created": true
			},
			{
				"name": "Firmamento_Zefiro",
				"height_offset": 3000,
				"layer_type": "aerial",
				"streaming_distance": 6000,
				"description": "Camada intermediária etérea com plataformas flutuantes",
				"data_layer_name": "Firmamento_Zefiro",
				"is_visible": true,
				"is_initially_visible": true,
				"runtime_state": "Loaded",
				"data_layer_created": true
			},
			{
				"name": "Abismo_Umbral",
				"height_offset": 5000,
				"layer_type": "underground",
				"streaming_distance": 4500,
				"description": "Camada superior sombria com arquitetura gótica",
				"data_layer_name": "Abismo_Umbral",
				"is_visible": true,
				"is_initially_visible": true,
				"runtime_state": "Loaded",
				"data_layer_created": true
			}
		],
		"world_partition_enabled": "true",
		"world_data_layers_created": true,
		"data_layer_instances_count": 3,
		"cell_size": 25600,
		"loading_range": 76800,
		"timestamp": "2025.08.29-08.33.10"
	}
}
[2025.08.29-11.33.10:948][283]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 1764
[2025.08.29-11.33.10:948][283]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.29-11.33.10:950][283]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.29-11.33.11:127][284]LogContentValidation: Display: Starting to validate 4 assets
[2025.08.29-11.33.11:127][284]LogContentValidation: Enabled validators:
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.08.29-11.33.11:127][284]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.08.29-11.33.11:127][284]AssetCheck: /Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante Validando ativo
[2025.08.29-11.33.11:128][284]AssetCheck: /Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zefiro Validando ativo
[2025.08.29-11.33.11:128][284]AssetCheck: /Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral Validando ativo
[2025.08.29-11.33.11:128][284]AssetCheck: /Game/Maps/AURACRON_Revolutionary_Map Validando ativo
[2025.08.29-11.33.18:729][414]LogSlate: Took 0.000234 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.29-11.33.20:824][443]LogUObjectHash: Compacting FUObjectHashTables data took   0.41ms
[2025.08.29-11.33.20:827][443]Cmd: MAP LOAD FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.29-11.33.20:856][443]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.29-11.33.20:856][443]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.29-11.33.20:856][443]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.29-11.33.20:880][443]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.29-11.33.20:891][443]LogUObjectHash: Compacting FUObjectHashTables data took   0.56ms
[2025.08.29-11.33.20:893][443]LogLoad: Beginning reference chain search...
[2025.08.29-11.33.20:893][443]LogLoad:  - World /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map
[2025.08.29-11.33.20:915][443]LogReferenceChain: Display: InitialGather memory usage: 3.50
[2025.08.29-11.33.20:915][443]LogReferenceChain: Display: Post-search memory usage: 1.60
[2025.08.29-11.33.20:915][443]LogLoad: Error: Printing reference chains leading to World /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map: 
[2025.08.29-11.33.20:915][443]LogReferenceChain: Error: (standalone) World /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map is not currently reachable but it does have some of GARBAGE_COLLECTION_KEEPFLAGS set.
[2025.08.29-11.33.20:915][443]LogLoad: Error: Old World /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map not cleaned up by GC! However it's not referenced by any object. It may have a flag set that's preventing it from being destroyed (see log for details):

[2025.08.29-11.33.20:915][443]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.29-11.33.20:915][443]LogWindows: Error: appError called: Fatal error: [File:D:\build\++UE5\Sync\Engine\Source\Editor\UnrealEd\Private\EditorServer.cpp] [Line: 1934] 
World Memory Leaks: 1 leaks objects and packages. See The output above.



[2025.08.29-11.33.20:915][443]LogWindows: Windows GetLastError: A operação foi concluída com êxito. (0)

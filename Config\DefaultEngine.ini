

[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Engine/Maps/Templates/OpenWorld

[/Script/Engine.RendererSettings]
r.AllowStaticLighting=False

r.GenerateMeshDistanceFields=True

r.DynamicGlobalIlluminationMethod=1

r.ReflectionMethod=1

r.SkinCache.CompileShaders=True

r.RayTracing=True

r.RayTracing.RayTracingProxies.ProjectEnabled=True

r.Shadow.Virtual.Enable=1

r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True

r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8

r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
-TargetedRHIs=SF_VULKAN_SM5
+TargetedRHIs=SF_VULKAN_SM6

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=Class'/Script/UnrealEd.WorldPartitionConvertCommandlet'
bEnableWorldPartitionStreaming=True
bEnableWorldPartitionHLODs=True
bDisableWorldPartitionLoadingInEditor=False
bEnableWorldPartitionRuntimeHash=True
bEnableWorldPartitionLevelStreaming=True

[/Script/Engine.WorldPartition]
bEnableWorldPartition=True
bEnableStreamingInEditor=True
bEnableHLODs=True
bDisableBuggyWorldComposition=True
bEnableWorldPartitionRuntimeSpatialHash=True
DefaultGridSize=25600
DefaultLoadingRange=25600
DefaultHLODLayer=None
bStreamingDataGenerationEnabled=True
bBuggyWorldCompositionGridSnapping=False

[/Script/Engine.World]
bEnableWorldPartition=True
bStreamingDataGenerationEnabled=True
bEnableHierarchicalLOD=True
bGenerateOverlapEvents=True
bEnableWorldComposition=False
DefaultPhysicsVolumeClass=/Script/Engine.DefaultPhysicsVolume
PhysicsCollisionHandlerClass=/Script/Engine.PhysicsCollisionHandler
DefaultGameMode=/Script/Engine.GameModeBase
GlobalDefaultGameMode=/Script/Engine.GameModeBase
GlobalDefaultPawn=/Script/Engine.DefaultPawn
bSublevelsShareNavMesh=True
bEnableNavigationSystem=True

[/Script/Engine.UserInterfaceSettings]
bAuthorizeAutomaticWidgetVariableCreation=False
FontDPIPreset=Standard
FontDPI=72

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_BlankBP",NewGameName="/Script/AURACRON")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_BlankBP",NewGameName="/Script/AURACRON")

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=5A73A4464F8F8A45560B8B88154F722C
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=


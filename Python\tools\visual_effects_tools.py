"""
Visual Effects Tools for Unreal MCP.

This module provides tools for creating and manipulating visual effects in Unreal Engine.
Specifically designed for Auracron's atmospheric effects, lighting, and particle systems.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_visual_effects_tools(mcp: FastMCP):
    """Register Visual Effects tools with the MCP server."""
    
    @mcp.tool()
    def create_dynamic_lighting(
        ctx: Context,
        light_name: str,
        light_type: str,
        location: Dict[str, float],
        layer_index: int,
        lighting_properties: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create dynamic lighting using modern UE 5.6.1 APIs with Lumen integration.
        
        Args:
            light_name: Name of the light to create
            light_type: Type of light (directional, point, spot)
            location: Light location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            lighting_properties: Lighting properties:
                - intensity: Light intensity
                - color: Light color {r, g, b, a}
                - attenuation_radius: Attenuation radius for point/spot lights
                - cast_shadows: Whether light casts shadows
                - use_lumen: Enable Lumen global illumination
        
        Returns:
            Dict containing success status and lighting creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "light_name": light_name,
                "light_type": light_type,
                "location": location,
                "layer_index": layer_index
            }
            
            if lighting_properties:
                params.update(lighting_properties)
            
            logger.info(f"Creating dynamic lighting: {light_name} ({light_type}) on layer {layer_index}")
            
            response = unreal.send_command("create_dynamic_lighting", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Dynamic lighting creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating dynamic lighting: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_sky_atmosphere(
        ctx: Context,
        atmosphere_name: str,
        atmosphere_settings: Dict[str, Any],
        layer_specific: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Setup sky atmosphere with modern atmospheric scattering.
        
        Args:
            atmosphere_name: Name of the atmosphere setup
            atmosphere_settings: Atmosphere settings:
                - bottom_radius: Planet bottom radius (km)
                - atmosphere_height: Atmosphere height (km)
                - rayleigh_scattering: Rayleigh scattering color
                - mie_scattering: Mie scattering color
                - ground_albedo: Ground albedo color
            layer_specific: Layer-specific atmosphere overrides
        
        Returns:
            Dict containing success status and atmosphere setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "atmosphere_name": atmosphere_name,
                **atmosphere_settings
            }
            
            if layer_specific:
                params["layer_specific"] = layer_specific
            
            logger.info(f"Setting up sky atmosphere: {atmosphere_name}")
            
            response = unreal.send_command("setup_sky_atmosphere", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Sky atmosphere setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up sky atmosphere: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_volumetric_effects(
        ctx: Context,
        effect_name: str,
        effect_type: str,
        location: Dict[str, float],
        effect_properties: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create volumetric effects like fog and clouds.
        
        Args:
            effect_name: Name of the volumetric effect
            effect_type: Type of effect (fog, clouds, particles)
            location: Effect location {x, y, z}
            effect_properties: Effect properties:
                - density: Effect density
                - color: Effect color {r, g, b, a}
                - scale: Effect scale {x, y, z}
                - use_volumetric_lighting: Enable volumetric lighting
        
        Returns:
            Dict containing success status and volumetric effect creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "effect_name": effect_name,
                "effect_type": effect_type,
                "location": location,
                **effect_properties
            }
            
            logger.info(f"Creating volumetric effect: {effect_name} ({effect_type})")
            
            response = unreal.send_command("create_volumetric_effects", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Volumetric effect creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating volumetric effect: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_niagara_effects(
        ctx: Context,
        effect_name: str,
        effect_type: str,
        location: Dict[str, float],
        niagara_system: Optional[str] = None,
        effect_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Setup Niagara particle effects.
        
        Args:
            effect_name: Name of the Niagara effect
            effect_type: Type of effect (particles, magic, environmental)
            location: Effect location {x, y, z}
            niagara_system: Path to Niagara system asset
            effect_settings: Effect settings:
                - auto_activate: Auto-activate the effect
                - auto_destroy: Auto-destroy when finished
                - scale: Effect scale {x, y, z}
                - rotation: Effect rotation {pitch, yaw, roll}
        
        Returns:
            Dict containing success status and Niagara effect setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "effect_name": effect_name,
                "effect_type": effect_type,
                "location": location
            }
            
            if niagara_system:
                params["niagara_system"] = niagara_system
            
            if effect_settings:
                params.update(effect_settings)
            
            logger.info(f"Setting up Niagara effect: {effect_name} ({effect_type})")
            
            response = unreal.send_command("setup_niagara_effects", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Niagara effect setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up Niagara effect: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def configure_post_processing(
        ctx: Context,
        postprocess_name: str,
        location: Dict[str, float],
        extent: Dict[str, float],
        postprocess_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configure post-processing effects.
        
        Args:
            postprocess_name: Name of the post-process volume
            location: Volume location {x, y, z}
            extent: Volume extent {x, y, z}
            postprocess_settings: Post-process settings:
                - bloom_intensity: Bloom intensity
                - exposure: Exposure compensation
                - color_grading: Color grading settings
                - unbound: Whether volume is unbound
        
        Returns:
            Dict containing success status and post-processing configuration results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "postprocess_name": postprocess_name,
                "location": location,
                "extent": extent,
                **postprocess_settings
            }
            
            logger.info(f"Configuring post-processing: {postprocess_name}")
            
            response = unreal.send_command("configure_post_processing", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Post-processing configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring post-processing: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

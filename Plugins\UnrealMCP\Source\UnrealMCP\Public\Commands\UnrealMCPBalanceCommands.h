#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core Analysis APIs - UE 5.6.1 Modern
#include "Engine/World.h"
#include "GameFramework/GameMode.h"
#include "GameFramework/GameState.h"
#include "GameFramework/WorldSettings.h"
#include "Engine/LevelStreaming.h"

// Advanced Analytics APIs - UE 5.6.1 Experimental
#include "Analytics.h"
#include "Interfaces/IAnalyticsProvider.h"
#include "AnalyticsEventAttribute.h"
#include "Stats/Stats.h"

// Mathematical Analysis APIs - UE 5.6.1 Enhanced
#include "Kismet/KismetMathLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Misc/ExpressionParserTypes.h"
#include "Misc/ExpressionParser.h"

// Performance Tracking APIs - UE 5.6.1 Advanced
#include "ProfilingDebugging/CsvProfiler.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Thread Safety and Memory Validation - UE 5.6.1 Modern
#include "UObject/WeakObjectPtr.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"

// Thread safety macros are now imported from UnrealMCPCommonUtils.h

/**
 * Handler class for Advanced Dynamic Balance System
 * 
 * Implements PRODUCTION READY balance analysis for Auracron's 3-layer MOBA
 * with automated symmetry analysis, imbalance detection, compensation systems,
 * and continuous telemetry for refinements.
 * 
 * Features:
 * - Automated strategic symmetry analysis across all 3 layers
 * - Advanced imbalance detection using statistical algorithms
 * - Dynamic compensation system for resource and objective imbalances
 * - Continuous telemetry with real-time balance monitoring
 * - Automated suggestions for balance corrections
 * - Performance impact analysis for balance changes
 * - Historical balance trend analysis and prediction
 * - Integration with analytics for data-driven balance decisions
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPBalanceCommands
{
public:
    FUnrealMCPBalanceCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // SYMMETRY ANALYSIS COMMANDS
    // ========================================

    /**
     * Creates automated strategic symmetry analysis system
     * 
     * Parameters:
     * - analysis_system_name (string): Name of the symmetry analysis system
     * - symmetry_categories (array): Categories to analyze (spatial, resource, objective, strategic)
     * - layer_analysis_settings (array): Analysis settings per layer
     * - comparison_algorithms (object): Algorithms for symmetry comparison
     * - tolerance_thresholds (object): Acceptable deviation thresholds
     * 
     * Implementation:
     * - Analyzes spatial symmetry across all 3 layers
     * - Evaluates resource distribution symmetry
     * - Checks objective placement and accessibility symmetry
     * - Measures strategic advantage symmetry between teams
     * 
     * @param Params JSON parameters
     * @return JSON response with symmetry analysis system details
     */
    TSharedPtr<FJsonObject> HandleCreateAutomatedSymmetryAnalysis(const TSharedPtr<FJsonObject>& Params);

    /**
     * Detects imbalances using advanced statistical algorithms
     * 
     * Parameters:
     * - detection_system_name (string): Name of the imbalance detection system
     * - detection_algorithms (array): Statistical algorithms to use
     * - imbalance_categories (array): Categories of imbalances to detect
     * - statistical_thresholds (object): Statistical significance thresholds
     * - temporal_analysis (object): Time-based imbalance analysis settings
     * 
     * Implementation:
     * - Uses statistical analysis to detect significant imbalances
     * - Implements trend analysis for emerging imbalances
     * - Applies machine learning for pattern recognition
     * - Creates automated alerts for critical imbalances
     * 
     * @param Params JSON parameters
     * @return JSON response with imbalance detection system results
     */
    TSharedPtr<FJsonObject> HandleCreateAdvancedImbalanceDetection(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // COMPENSATION SYSTEM COMMANDS
    // ========================================

    /**
     * Implements dynamic compensation system for detected imbalances
     * 
     * Parameters:
     * - compensation_system_name (string): Name of the compensation system
     * - compensation_strategies (array): Strategies for different imbalance types
     * - adjustment_parameters (object): Parameters for dynamic adjustments
     * - safety_limits (object): Safety limits for automatic adjustments
     * - rollback_mechanisms (object): Rollback mechanisms for failed adjustments
     * 
     * Implementation:
     * - Automatically adjusts resource spawns to compensate imbalances
     * - Dynamically modifies objective values and timers
     * - Implements gradual adjustment algorithms
     * - Provides rollback mechanisms for failed compensations
     * 
     * @param Params JSON parameters
     * @return JSON response with compensation system results
     */
    TSharedPtr<FJsonObject> HandleCreateDynamicCompensationSystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // TELEMETRY SYSTEM COMMANDS
    // ========================================

    /**
     * Sets up continuous telemetry for balance monitoring
     * 
     * Parameters:
     * - telemetry_system_name (string): Name of the telemetry system
     * - monitoring_categories (array): Categories to monitor continuously
     * - data_collection_intervals (object): Collection intervals per category
     * - real_time_alerts (object): Real-time alert configurations
     * - data_retention_policies (object): Data retention and archival policies
     * 
     * Implementation:
     * - Continuously monitors balance metrics across all layers
     * - Implements real-time data collection and analysis
     * - Creates automated alerts for balance deviations
     * - Maintains historical data for trend analysis
     * 
     * @param Params JSON parameters
     * @return JSON response with telemetry system results
     */
    TSharedPtr<FJsonObject> HandleCreateContinuousTelemetrySystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // REFINEMENT SYSTEM COMMANDS
    // ========================================

    /**
     * Creates automated refinement suggestions system
     * 
     * Parameters:
     * - refinement_system_name (string): Name of the refinement system
     * - suggestion_algorithms (array): Algorithms for generating suggestions
     * - refinement_categories (array): Categories of refinements to suggest
     * - impact_analysis (object): Impact analysis for suggested changes
     * - validation_mechanisms (object): Validation mechanisms for suggestions
     * 
     * Implementation:
     * - Analyzes balance data to generate refinement suggestions
     * - Predicts impact of suggested changes
     * - Validates suggestions against historical data
     * - Creates prioritized refinement recommendations
     * 
     * @param Params JSON parameters
     * @return JSON response with refinement system results
     */
    TSharedPtr<FJsonObject> HandleCreateAutomatedRefinementSuggestions(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName, 
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // INTERNAL BALANCE LOGIC
    // ========================================

    /**
     * Analyzes spatial symmetry between teams across layers
     * @param LayerIndex Layer to analyze
     * @param AnalysisType Type of spatial analysis
     * @return Symmetry analysis results
     */
    TSharedPtr<FJsonObject> AnalyzeSpatialSymmetry(int32 LayerIndex, const FString& AnalysisType);

    /**
     * Calculates statistical significance of detected imbalance
     * @param MetricName Name of the metric
     * @param Team1Values Values for team 1
     * @param Team2Values Values for team 2
     * @return Statistical significance result
     */
    float CalculateStatisticalSignificance(const FString& MetricName,
                                          const TArray<float>& Team1Values,
                                          const TArray<float>& Team2Values);

    /**
     * Applies dynamic compensation for detected imbalance
     * @param ImbalanceType Type of imbalance
     * @param ImbalanceMagnitude Magnitude of the imbalance
     * @param CompensationStrategy Strategy to use for compensation
     * @return Success status and applied compensation details
     */
    TSharedPtr<FJsonObject> ApplyDynamicCompensation(const FString& ImbalanceType,
                                                    float ImbalanceMagnitude,
                                                    const FString& CompensationStrategy);

    /**
     * Generates refinement suggestions based on balance analysis
     * @param AnalysisData Balance analysis data
     * @param SuggestionCategories Categories of suggestions to generate
     * @return Generated refinement suggestions
     */
    TArray<TSharedPtr<FJsonObject>> GenerateRefinementSuggestions(const TSharedPtr<FJsonObject>& AnalysisData,
                                                                 const TArray<FString>& SuggestionCategories);

private:
    // Balance analysis data per layer
    TMap<int32, TArray<TSharedPtr<FJsonObject>>> LayerBalanceData;
    
    // Symmetry analysis results
    TMap<FString, TSharedPtr<FJsonObject>> SymmetryAnalysisResults;
    
    // Detected imbalances with timestamps
    TArray<TSharedPtr<FJsonObject>> DetectedImbalances;
    
    // Applied compensations history
    TArray<TSharedPtr<FJsonObject>> CompensationHistory;
    
    // Telemetry data collection
    TMap<FString, TArray<float>> TelemetryMetrics;
    
    // Refinement suggestions
    TArray<TSharedPtr<FJsonObject>> RefinementSuggestions;
    
    // Balance system settings
    struct FBalanceSystemSettings
    {
        float SymmetryTolerance = 0.05f; // 5% tolerance
        float StatisticalSignificanceThreshold = 0.05f; // p < 0.05
        float CompensationSafetyLimit = 0.2f; // Max 20% adjustment
        float TelemetryCollectionInterval = 10.0f; // 10 seconds
        bool bEnableAutomaticCompensation = false; // Manual approval required
    } SystemSettings;
};

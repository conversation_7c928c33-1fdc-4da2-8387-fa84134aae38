// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPVisualEffectsCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPVisualEffectsCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APostProcessVolume_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkyAtmosphereComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FNiagaraEffectsConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FPostProcessConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FSkyAtmosphereConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FVolumetricEffectsConfig();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronLightingConfig *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLightingConfig;
class UScriptStruct* FAuracronLightingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLightingConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronLightingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron lighting configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron lighting configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightName_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightType_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightLocation_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// directional, point, spot\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "directional, point, spot" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightRotation_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightColor_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LightIntensity_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttenuationRadius_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLumen_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCastShadows_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicShadowDistance_MetaData[] = {
		{ "Category", "AuracronLightingConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LightType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LightIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttenuationRadius;
	static void NewProp_bUseLumen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLumen;
	static void NewProp_bCastShadows_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCastShadows;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DynamicShadowDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLightingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightName = { "LightName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightName_MetaData), NewProp_LightName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightType = { "LightType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightType_MetaData), NewProp_LightType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightLocation = { "LightLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightLocation_MetaData), NewProp_LightLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightRotation = { "LightRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightRotation_MetaData), NewProp_LightRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightColor = { "LightColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightColor_MetaData), NewProp_LightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightIntensity = { "LightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, LightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LightIntensity_MetaData), NewProp_LightIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_AttenuationRadius = { "AttenuationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, AttenuationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttenuationRadius_MetaData), NewProp_AttenuationRadius_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bUseLumen_SetBit(void* Obj)
{
	((FAuracronLightingConfig*)Obj)->bUseLumen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bUseLumen = { "bUseLumen", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfig), &Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bUseLumen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLumen_MetaData), NewProp_bUseLumen_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bCastShadows_SetBit(void* Obj)
{
	((FAuracronLightingConfig*)Obj)->bCastShadows = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bCastShadows = { "bCastShadows", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLightingConfig), &Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bCastShadows_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCastShadows_MetaData), NewProp_bCastShadows_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_DynamicShadowDistance = { "DynamicShadowDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLightingConfig, DynamicShadowDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicShadowDistance_MetaData), NewProp_DynamicShadowDistance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_LightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_AttenuationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bUseLumen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_bCastShadows,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewProp_DynamicShadowDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronLightingConfig",
	Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::PropPointers),
	sizeof(FAuracronLightingConfig),
	alignof(FAuracronLightingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLightingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLightingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLightingConfig *********************************************

// ********** Begin ScriptStruct FSkyAtmosphereConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig;
class UScriptStruct* FSkyAtmosphereConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSkyAtmosphereConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("SkyAtmosphereConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sky atmosphere configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sky atmosphere configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AtmosphereName_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BottomRadius_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AtmosphereHeight_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RayleighScattering_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RayleighExponentialDistribution_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MieScattering_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MieAnisotropy_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroundAlbedo_MetaData[] = {
		{ "Category", "SkyAtmosphereConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AtmosphereName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BottomRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AtmosphereHeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RayleighScattering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RayleighExponentialDistribution;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MieScattering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MieAnisotropy;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GroundAlbedo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSkyAtmosphereConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_AtmosphereName = { "AtmosphereName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, AtmosphereName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AtmosphereName_MetaData), NewProp_AtmosphereName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_BottomRadius = { "BottomRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, BottomRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BottomRadius_MetaData), NewProp_BottomRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_AtmosphereHeight = { "AtmosphereHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, AtmosphereHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AtmosphereHeight_MetaData), NewProp_AtmosphereHeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_RayleighScattering = { "RayleighScattering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, RayleighScattering), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RayleighScattering_MetaData), NewProp_RayleighScattering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_RayleighExponentialDistribution = { "RayleighExponentialDistribution", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, RayleighExponentialDistribution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RayleighExponentialDistribution_MetaData), NewProp_RayleighExponentialDistribution_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_MieScattering = { "MieScattering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, MieScattering), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MieScattering_MetaData), NewProp_MieScattering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_MieAnisotropy = { "MieAnisotropy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, MieAnisotropy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MieAnisotropy_MetaData), NewProp_MieAnisotropy_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_GroundAlbedo = { "GroundAlbedo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSkyAtmosphereConfig, GroundAlbedo), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroundAlbedo_MetaData), NewProp_GroundAlbedo_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_AtmosphereName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_BottomRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_AtmosphereHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_RayleighScattering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_RayleighExponentialDistribution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_MieScattering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_MieAnisotropy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewProp_GroundAlbedo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"SkyAtmosphereConfig",
	Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::PropPointers),
	sizeof(FSkyAtmosphereConfig),
	alignof(FSkyAtmosphereConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSkyAtmosphereConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.InnerSingleton, Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig.InnerSingleton;
}
// ********** End ScriptStruct FSkyAtmosphereConfig ************************************************

// ********** Begin ScriptStruct FVolumetricEffectsConfig ******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig;
class UScriptStruct* FVolumetricEffectsConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FVolumetricEffectsConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("VolumetricEffectsConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Volumetric effects configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volumetric effects configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectName_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectType_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectLocation_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// fog, clouds, particles\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "fog, clouds, particles" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectScale_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectColor_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectDensity_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectIntensity_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseVolumetricLighting_MetaData[] = {
		{ "Category", "VolumetricEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectDensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectIntensity;
	static void NewProp_bUseVolumetricLighting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseVolumetricLighting;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FVolumetricEffectsConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectName = { "EffectName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectName_MetaData), NewProp_EffectName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectType_MetaData), NewProp_EffectType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectLocation = { "EffectLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectLocation_MetaData), NewProp_EffectLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectScale = { "EffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectScale_MetaData), NewProp_EffectScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectColor = { "EffectColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectColor_MetaData), NewProp_EffectColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectDensity = { "EffectDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectDensity_MetaData), NewProp_EffectDensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectIntensity = { "EffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVolumetricEffectsConfig, EffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectIntensity_MetaData), NewProp_EffectIntensity_MetaData) };
void Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_bUseVolumetricLighting_SetBit(void* Obj)
{
	((FVolumetricEffectsConfig*)Obj)->bUseVolumetricLighting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_bUseVolumetricLighting = { "bUseVolumetricLighting", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FVolumetricEffectsConfig), &Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_bUseVolumetricLighting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseVolumetricLighting_MetaData), NewProp_bUseVolumetricLighting_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_EffectIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewProp_bUseVolumetricLighting,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"VolumetricEffectsConfig",
	Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::PropPointers),
	sizeof(FVolumetricEffectsConfig),
	alignof(FVolumetricEffectsConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FVolumetricEffectsConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.InnerSingleton, Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig.InnerSingleton;
}
// ********** End ScriptStruct FVolumetricEffectsConfig ********************************************

// ********** Begin ScriptStruct FNiagaraEffectsConfig *********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig;
class UScriptStruct* FNiagaraEffectsConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FNiagaraEffectsConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("NiagaraEffectsConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Niagara effects configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Niagara effects configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectName_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectType_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectLocation_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// particles, magic, environmental\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "particles, magic, environmental" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectRotation_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectScale_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NiagaraSystem_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Niagara system asset reference\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Niagara system asset reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoActivate_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoDestroy_MetaData[] = {
		{ "Category", "NiagaraEffectsConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EffectType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EffectScale;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NiagaraSystem;
	static void NewProp_bAutoActivate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoActivate;
	static void NewProp_bAutoDestroy_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoDestroy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FNiagaraEffectsConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectName = { "EffectName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, EffectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectName_MetaData), NewProp_EffectName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectType = { "EffectType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, EffectType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectType_MetaData), NewProp_EffectType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectLocation = { "EffectLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, EffectLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectLocation_MetaData), NewProp_EffectLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectRotation = { "EffectRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, EffectRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectRotation_MetaData), NewProp_EffectRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectScale = { "EffectScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, EffectScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectScale_MetaData), NewProp_EffectScale_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_NiagaraSystem = { "NiagaraSystem", nullptr, (EPropertyFlags)0x0114000000000005, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FNiagaraEffectsConfig, NiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NiagaraSystem_MetaData), NewProp_NiagaraSystem_MetaData) };
void Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoActivate_SetBit(void* Obj)
{
	((FNiagaraEffectsConfig*)Obj)->bAutoActivate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoActivate = { "bAutoActivate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FNiagaraEffectsConfig), &Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoActivate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoActivate_MetaData), NewProp_bAutoActivate_MetaData) };
void Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoDestroy_SetBit(void* Obj)
{
	((FNiagaraEffectsConfig*)Obj)->bAutoDestroy = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoDestroy = { "bAutoDestroy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FNiagaraEffectsConfig), &Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoDestroy_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoDestroy_MetaData), NewProp_bAutoDestroy_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_EffectScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_NiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoActivate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewProp_bAutoDestroy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"NiagaraEffectsConfig",
	Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::PropPointers),
	sizeof(FNiagaraEffectsConfig),
	alignof(FNiagaraEffectsConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FNiagaraEffectsConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.InnerSingleton, Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig.InnerSingleton;
}
// ********** End ScriptStruct FNiagaraEffectsConfig ***********************************************

// ********** Begin ScriptStruct FPostProcessConfig ************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPostProcessConfig;
class UScriptStruct* FPostProcessConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPostProcessConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPostProcessConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPostProcessConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("PostProcessConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FPostProcessConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPostProcessConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Post process configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Post process configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessName_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumeLocation_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumeExtent_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BloomIntensity_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExposureCompensation_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorGrading_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUnbound_MetaData[] = {
		{ "Category", "PostProcessConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PostProcessName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VolumeLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VolumeExtent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BloomIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExposureCompensation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorGrading;
	static void NewProp_bUnbound_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUnbound;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPostProcessConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_PostProcessName = { "PostProcessName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, PostProcessName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessName_MetaData), NewProp_PostProcessName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_VolumeLocation = { "VolumeLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, VolumeLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumeLocation_MetaData), NewProp_VolumeLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_VolumeExtent = { "VolumeExtent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, VolumeExtent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumeExtent_MetaData), NewProp_VolumeExtent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_BloomIntensity = { "BloomIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, BloomIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BloomIntensity_MetaData), NewProp_BloomIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_ExposureCompensation = { "ExposureCompensation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, ExposureCompensation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExposureCompensation_MetaData), NewProp_ExposureCompensation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_ColorGrading = { "ColorGrading", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPostProcessConfig, ColorGrading), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorGrading_MetaData), NewProp_ColorGrading_MetaData) };
void Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_bUnbound_SetBit(void* Obj)
{
	((FPostProcessConfig*)Obj)->bUnbound = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_bUnbound = { "bUnbound", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPostProcessConfig), &Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_bUnbound_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUnbound_MetaData), NewProp_bUnbound_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPostProcessConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_PostProcessName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_VolumeLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_VolumeExtent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_BloomIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_ExposureCompensation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_ColorGrading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewProp_bUnbound,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPostProcessConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPostProcessConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"PostProcessConfig",
	Z_Construct_UScriptStruct_FPostProcessConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPostProcessConfig_Statics::PropPointers),
	sizeof(FPostProcessConfig),
	alignof(FPostProcessConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPostProcessConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPostProcessConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPostProcessConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FPostProcessConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPostProcessConfig.InnerSingleton, Z_Construct_UScriptStruct_FPostProcessConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPostProcessConfig.InnerSingleton;
}
// ********** End ScriptStruct FPostProcessConfig **************************************************

// ********** Begin Class UUnrealMCPVisualEffectsCommands ******************************************
void UUnrealMCPVisualEffectsCommands::StaticRegisterNativesUUnrealMCPVisualEffectsCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands;
UClass* UUnrealMCPVisualEffectsCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPVisualEffectsCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPVisualEffectsCommands"),
			Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPVisualEffectsCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister()
{
	return UUnrealMCPVisualEffectsCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Visual Effects Commands - Modern UE 5.6.1 Implementation\n * Handles advanced lighting, atmosphere, and visual effects with Lumen/Nanite integration\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPVisualEffectsCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Visual Effects Commands - Modern UE 5.6.1 Implementation\nHandles advanced lighting, atmosphere, and visual effects with Lumen/Nanite integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedLights_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created lights\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created lights" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkyAtmosphereComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for sky atmosphere components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for sky atmosphere components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VolumetricEffects_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for volumetric effects\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for volumetric effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessVolumes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for post process volumes\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisualEffectsCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for post process volumes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedLights_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedLights_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedLights;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SkyAtmosphereComponents_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SkyAtmosphereComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SkyAtmosphereComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VolumetricEffects_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VolumetricEffects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VolumetricEffects;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PostProcessVolumes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PostProcessVolumes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PostProcessVolumes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPVisualEffectsCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights_ValueProp = { "CreatedLights", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights_Key_KeyProp = { "CreatedLights_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights = { "CreatedLights", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPVisualEffectsCommands, CreatedLights), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedLights_MetaData), NewProp_CreatedLights_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents_ValueProp = { "SkyAtmosphereComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USkyAtmosphereComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents_Key_KeyProp = { "SkyAtmosphereComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents = { "SkyAtmosphereComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPVisualEffectsCommands, SkyAtmosphereComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkyAtmosphereComponents_MetaData), NewProp_SkyAtmosphereComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects_ValueProp = { "VolumetricEffects", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects_Key_KeyProp = { "VolumetricEffects_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects = { "VolumetricEffects", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPVisualEffectsCommands, VolumetricEffects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VolumetricEffects_MetaData), NewProp_VolumetricEffects_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes_ValueProp = { "PostProcessVolumes", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_APostProcessVolume_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes_Key_KeyProp = { "PostProcessVolumes_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes = { "PostProcessVolumes", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPVisualEffectsCommands, PostProcessVolumes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessVolumes_MetaData), NewProp_PostProcessVolumes_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_CreatedLights,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_SkyAtmosphereComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_VolumetricEffects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::NewProp_PostProcessVolumes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::ClassParams = {
	&UUnrealMCPVisualEffectsCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands.OuterSingleton;
}
UUnrealMCPVisualEffectsCommands::UUnrealMCPVisualEffectsCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPVisualEffectsCommands);
UUnrealMCPVisualEffectsCommands::~UUnrealMCPVisualEffectsCommands() {}
// ********** End Class UUnrealMCPVisualEffectsCommands ********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLightingConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics::NewStructOps, TEXT("AuracronLightingConfig"), &Z_Registration_Info_UScriptStruct_FAuracronLightingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLightingConfig), 2481056071U) },
		{ FSkyAtmosphereConfig::StaticStruct, Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics::NewStructOps, TEXT("SkyAtmosphereConfig"), &Z_Registration_Info_UScriptStruct_FSkyAtmosphereConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSkyAtmosphereConfig), 1102901052U) },
		{ FVolumetricEffectsConfig::StaticStruct, Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics::NewStructOps, TEXT("VolumetricEffectsConfig"), &Z_Registration_Info_UScriptStruct_FVolumetricEffectsConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FVolumetricEffectsConfig), 2403335358U) },
		{ FNiagaraEffectsConfig::StaticStruct, Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics::NewStructOps, TEXT("NiagaraEffectsConfig"), &Z_Registration_Info_UScriptStruct_FNiagaraEffectsConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FNiagaraEffectsConfig), 1028115295U) },
		{ FPostProcessConfig::StaticStruct, Z_Construct_UScriptStruct_FPostProcessConfig_Statics::NewStructOps, TEXT("PostProcessConfig"), &Z_Registration_Info_UScriptStruct_FPostProcessConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPostProcessConfig), 2871719362U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPVisualEffectsCommands, UUnrealMCPVisualEffectsCommands::StaticClass, TEXT("UUnrealMCPVisualEffectsCommands"), &Z_Registration_Info_UClass_UUnrealMCPVisualEffectsCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPVisualEffectsCommands), 4060177479U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_3560218849(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

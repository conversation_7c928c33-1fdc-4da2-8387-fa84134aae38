# AURACRON MEMORY LEAK FIX SCRIPT
# This script fixes all memory leaks by adding proper RF_Public | RF_Standalone flags to NewObject calls

Write-Host "=== AURACRON MEMORY LEAK FIX ===" -ForegroundColor Red
Write-Host "Fixing memory leaks in UnrealMCP MapCommands..." -ForegroundColor Yellow

$file = "Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMapCommands.cpp"
$content = Get-Content $file -Raw
$originalContent = $content

Write-Host "Processing memory leak fixes..." -ForegroundColor Cyan

# Fix 1: WorldPartition creation
$content = $content -replace 'WorldPartition = NewObject<UWorldPartition>\(WorldSettings\);', 'WorldPartition = NewObject<UWorldPartition>(WorldSettings, UWorldPartition::StaticClass(), NAME_None, RF_Public | RF_Standalone);'

# Fix 2: Component creations without proper flags - Add RF_Public | RF_Standalone where missing
$content = $content -replace 'NewObject<USceneComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<USceneComponent>($1, USceneComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

$content = $content -replace 'NewObject<UStaticMeshComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<UStaticMeshComponent>($1, UStaticMeshComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

$content = $content -replace 'NewObject<USphereComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<USphereComponent>($1, USphereComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

$content = $content -replace 'NewObject<UBoxComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<UBoxComponent>($1, UBoxComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

$content = $content -replace 'NewObject<UAudioComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<UAudioComponent>($1, UAudioComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

$content = $content -replace 'NewObject<USplineComponent>\(([^,]+), TEXT\("([^"]+)"\)\);', 'NewObject<USplineComponent>($1, USplineComponent::StaticClass(), TEXT("$2"), RF_Public | RF_Standalone);'

# Fix 3: Component creations with complex names
$content = $content -replace 'NewObject<UStaticMeshComponent>\(([^,]+),\s*\*FString::Printf\(TEXT\("([^"]+)"\), ([^)]+)\)\);', 'NewObject<UStaticMeshComponent>($1, UStaticMeshComponent::StaticClass(), *FString::Printf(TEXT("$2"), $3), RF_Public | RF_Standalone);'

# Fix 4: Component creations without TEXT wrapper
$content = $content -replace 'NewObject<UStaticMeshComponent>\(([^,]+)\);', 'NewObject<UStaticMeshComponent>($1, UStaticMeshComponent::StaticClass(), NAME_None, RF_Public | RF_Standalone);'

Write-Host "Memory leak fixes applied!" -ForegroundColor Green

if ($content -ne $originalContent) {
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "File updated successfully!" -ForegroundColor Green
    Write-Host "Memory leaks fixed:" -ForegroundColor Cyan
    Write-Host "  - WorldPartition creation" -ForegroundColor White
    Write-Host "  - Component creations with proper flags" -ForegroundColor White
    Write-Host "  - All NewObject calls now use RF_Public | RF_Standalone" -ForegroundColor White
} else {
    Write-Host "No changes needed" -ForegroundColor Gray
}

Write-Host "=== MEMORY LEAK FIX COMPLETED ===" -ForegroundColor Green

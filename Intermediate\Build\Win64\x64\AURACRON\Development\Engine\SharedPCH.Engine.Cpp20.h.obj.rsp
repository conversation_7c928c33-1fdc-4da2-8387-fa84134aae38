"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Cpp20.cpp"
/I "."
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Engine/UHT"
/I "Runtime/Engine/Classes"
/I "Runtime/Engine/Public"
/I "Runtime/Engine/Internal"
/I "Runtime/Core/Public"
/I "Runtime/Core/Internal"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreOnline/UHT"
/I "Runtime/CoreOnline/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/CoreUObject/Internal"
/I "Runtime/CorePreciseFP/Public"
/I "Runtime/CorePreciseFP/Internal"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/FieldNotification/UHT"
/I "Runtime/FieldNotification/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/NetCore/UHT"
/I "Runtime/Net/Core/Classes"
/I "Runtime/Net/Core/Public"
/I "Runtime/Net/Common/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/JsonUtilities/UHT"
/I "Runtime/JsonUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/ApplicationCore/Internal"
/I "Runtime/RHI/Public"
/I "Runtime/RHI/Internal"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/ImageWrapper/Public"
/I "Runtime/Messaging/Public"
/I "Runtime/MessagingCommon/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/RenderCore/UHT"
/I "Runtime/RenderCore/Public"
/I "Runtime/RenderCore/Internal"
/I "Runtime/OpenGLDrv/Public"
/I "Runtime/Analytics/AnalyticsET/Public"
/I "Runtime/Analytics/Analytics/Public"
/I "Runtime/Sockets/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AssetRegistry/UHT"
/I "Runtime/AssetRegistry/Public"
/I "Runtime/AssetRegistry/Internal"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/EngineMessages/UHT"
/I "Runtime/EngineMessages/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/EngineSettings/UHT"
/I "Runtime/EngineSettings/Classes"
/I "Runtime/EngineSettings/Public"
/I "Runtime/SynthBenchmark/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/GameplayTags/UHT"
/I "Runtime/GameplayTags/Classes"
/I "Runtime/GameplayTags/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/PacketHandler/UHT"
/I "Runtime/PacketHandlers/PacketHandler/Classes"
/I "Runtime/PacketHandlers/PacketHandler/Public"
/I "Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioPlatformConfiguration/UHT"
/I "Runtime/AudioPlatformConfiguration/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MeshDescription/UHT"
/I "Runtime/MeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/StaticMeshDescription/UHT"
/I "Runtime/StaticMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/SkeletalMeshDescription/UHT"
/I "Runtime/SkeletalMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AnimationCore/UHT"
/I "Runtime/AnimationCore/Public"
/I "Runtime/PakFile/Public"
/I "Runtime/PakFile/Internal"
/I "Runtime/RSA/Public"
/I "Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/PhysicsCore/UHT"
/I "Runtime/PhysicsCore/Public"
/I "Runtime/Experimental/ChaosCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Chaos/UHT"
/I "Runtime/Experimental/Chaos/Public"
/I "Runtime/Experimental/Voronoi/Public"
/I "Runtime/GeometryCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ChaosVDRuntime/UHT"
/I "Runtime/Experimental/ChaosVisualDebugger/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/NNE/UHT"
/I "Runtime/NNE/Public"
/I "Runtime/SignalProcessing/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/StateStream/UHT"
/I "Runtime/StateStream/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioExtensions/UHT"
/I "Runtime/AudioExtensions/Public"
/I "Runtime/AudioMixerCore/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioMixer/UHT"
/I "Runtime/AudioMixer/Classes"
/I "Runtime/AudioMixer/Public"
/I "Developer/TargetPlatform/Public"
/I "Developer/TextureFormat/Public"
/I "Developer/DesktopPlatform/Public"
/I "Developer/DesktopPlatform/Internal"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioLinkEngine/UHT"
/I "Runtime/AudioLink/AudioLinkEngine/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/AudioLinkCore/UHT"
/I "Runtime/AudioLink/AudioLinkCore/Public"
/I "Runtime/CookOnTheFly/Internal"
/I "Runtime/Networking/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Internal"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/ClothSysRuntimeIntrfc/UHT"
/I "Runtime/ClothingSystemRuntimeInterface/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/IrisCore/UHT"
/I "Runtime/Experimental/Iris/Core/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/MovieSceneCapture/UHT"
/I "Runtime/MovieSceneCapture/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/Renderer/UHT"
/I "Runtime/Renderer/Public"
/I "Runtime/Renderer/Internal"
/I "../Shaders/Public"
/I "../Shaders/Shared"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/TypedElementFramework/UHT"
/I "Runtime/TypedElementFramework/Tests"
/I "Runtime/TypedElementFramework/Public"
/I "../Intermediate/Build/Win64/UnrealGame/Inc/TypedElementRuntime/UHT"
/I "Runtime/TypedElementRuntime/Public"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "ThirdParty/OpenGL"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"
/Yc"SharedPCH.Engine.Cpp20.h"
/Fp"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Cpp20.h.pch"
/Fo"C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Cpp20.h.obj"
/experimental:log "C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Cpp20.h.sarif"
/sourceDependencies "C:/Game/AURACRON/Intermediate/Build/Win64/x64/AURACRON/Development/Engine/SharedPCH.Engine.Cpp20.h.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/D_HAS_EXCEPTIONS=0
/DPLATFORM_EXCEPTIONS_DISABLED=1
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR-
/std:c++20
/Zc:preprocessor
/wd5054
﻿# WorldPartitionFix_AutoApply.ps1 - Script para aplicar correção automaticamente
# Executa verificações e aplica correções robustas para o erro de InitState

Write-Host "=== AURACRON World Partition Fix - Aplicação Automática ===" -ForegroundColor Green
Write-Host "Versão: 1.0 - Compatível com UE 5.6.1" -ForegroundColor Cyan
Write-Host ""

# Verificar se estamos no diretório correto
if (-not (Test-Path "AURACRON.uproject")) {
    Write-Host "ERRO: Execute este script no diretório raiz do projeto AURACRON!" -ForegroundColor Red
    exit 1
}

Write-Host "1. Verificando arquivos de configuração..." -ForegroundColor Yellow

# Verificar se as configurações foram aplicadas
$configFiles = @(
    "Config\DefaultEngine.ini",
    "Config\DefaultEditor.ini"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "    $file encontrado" -ForegroundColor Green
    } else {
        Write-Host "    $file não encontrado!" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "2. Verificando arquivos de código fonte..." -ForegroundColor Yellow

$sourceFiles = @(
    "Source\AURACRON\Public\WorldPartitionFix.h",
    "Source\AURACRON\Private\WorldPartitionFix.cpp",
    "Source\AURACRON\Public\WorldPartitionFixSubsystem.h",
    "Source\AURACRON\Private\WorldPartitionFixSubsystem.cpp"
)

foreach ($file in $sourceFiles) {
    if (Test-Path $file) {
        Write-Host "    $file encontrado" -ForegroundColor Green
    } else {
        Write-Host "    $file não encontrado!" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "3. Limpando cache e arquivos temporários..." -ForegroundColor Yellow

# Limpar caches que podem causar problemas
$cacheDirs = @(
    "Binaries",
    "Intermediate",
    "DerivedDataCache"
)

foreach ($dir in $cacheDirs) {
    if (Test-Path $dir) {
        Write-Host "   Limpando $dir..." -ForegroundColor Cyan
        Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "    $dir limpo" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "4. Regenerando arquivos de projeto..." -ForegroundColor Yellow

# Regenerar arquivos de projeto
if (Test-Path "AURACRON.sln") {
    Remove-Item "AURACRON.sln" -Force -ErrorAction SilentlyContinue
}

# Procurar pelo UnrealBuildTool
$unrealPath = ""
$possiblePaths = @(
    "C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe",
    "C:\Program Files (x86)\Epic Games\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe"
)

foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        $unrealPath = $path
        break
    }
}

if ($unrealPath -ne "") {
    Write-Host "   Regenerando projeto com UnrealBuildTool..." -ForegroundColor Cyan
    & $unrealPath -projectfiles -project="$PWD\AURACRON.uproject" -game -rocket -progress
    Write-Host "    Arquivos de projeto regenerados" -ForegroundColor Green
} else {
    Write-Host "    UnrealBuildTool não encontrado, regeneração manual necessária" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "5. Verificando integridade da correção..." -ForegroundColor Yellow

# Verificar se as configurações estão corretas
$engineIni = Get-Content "Config\DefaultEngine.ini" -Raw
if ($engineIni -match "bEnableWorldPartition=True") {
    Write-Host "    Configurações do World Partition aplicadas" -ForegroundColor Green
} else {
    Write-Host "    Configurações do World Partition não encontradas!" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== CORREÇÃO APLICADA COM SUCESSO ===" -ForegroundColor Green
Write-Host ""
Write-Host "Próximos passos:" -ForegroundColor Cyan
Write-Host "1. Abra o projeto no Unreal Engine 5.6" -ForegroundColor White
Write-Host "2. Compile o projeto (Build > Build Solution)" -ForegroundColor White
Write-Host "3. O sistema de correção será aplicado automaticamente" -ForegroundColor White
Write-Host ""
Write-Host "A correção implementada:" -ForegroundColor Yellow
Write-Host " Verifica o estado do World Partition antes da inicialização" -ForegroundColor White
Write-Host " Aplica reset seguro quando necessário" -ForegroundColor White
Write-Host " Monitora continuamente o estado" -ForegroundColor White
Write-Host " Previne o erro 'InitState != Uninitialized'" -ForegroundColor White
Write-Host ""
Write-Host "Pressione qualquer tecla para continuar..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

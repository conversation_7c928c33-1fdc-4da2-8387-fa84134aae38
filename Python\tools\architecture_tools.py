"""
Architecture Tools for Unreal MCP.

This module provides tools for creating and manipulating architectural structures in Unreal Engine.
Specifically designed for Auracron's MOBA structures including towers, bases, and monuments.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_architecture_tools(mcp: FastMCP):
    """Register Architecture tools with the MCP server."""
    
    @mcp.tool()
    def create_tower_structures(
        ctx: Context,
        tower_name: str,
        tower_type: str,
        location: Dict[str, float],
        layer_index: int,
        tower_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create tower structures with layer-specific designs using modern UE 5.6.1 APIs.
        
        Args:
            tower_name: Name of the tower structure
            tower_type: Type of tower (basic, advanced, nexus, inhibitor)
            location: Tower location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            tower_config: Tower configuration:
                - height: Tower height
                - base_radius: Base radius of the tower
                - architectural_style: Architectural style for the layer
                - defensive_features: List of defensive features
                - visual_effects: Visual effects configuration
        
        Returns:
            Dict containing success status and tower creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "tower_name": tower_name,
                "tower_type": tower_type,
                "location": location,
                "layer_index": layer_index
            }
            
            if tower_config:
                params["tower_config"] = tower_config
            
            logger.info(f"Creating tower structure: {tower_name} ({tower_type}) on layer {layer_index}")
            
            response = unreal.send_command("create_tower_structures", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Tower structure creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating tower structure: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_base_buildings(
        ctx: Context,
        building_name: str,
        building_type: str,
        location: Dict[str, float],
        team_side: str,
        building_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create base buildings with team-specific designs.
        
        Args:
            building_name: Name of the building
            building_type: Type of building (nexus, fountain, barracks, shop)
            location: Building location {x, y, z}
            team_side: Team side (blue, red)
            building_config: Building configuration:
                - size: Building size (small, medium, large)
                - architectural_style: Architectural style
                - team_colors: Apply team-specific colors
                - functional_elements: Functional elements to add
        
        Returns:
            Dict containing success status and building creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "building_name": building_name,
                "building_type": building_type,
                "location": location,
                "team_side": team_side
            }
            
            if building_config:
                params["building_config"] = building_config
            
            logger.info(f"Creating base building: {building_name} ({building_type}) for {team_side} team")
            
            response = unreal.send_command("create_base_buildings", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Base building creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating base building: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_inhibitor_monuments(
        ctx: Context,
        monument_name: str,
        location: Dict[str, float],
        layer_index: int,
        monument_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create inhibitor monuments with cascading effects.
        
        Args:
            monument_name: Name of the inhibitor monument
            location: Monument location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            monument_config: Monument configuration:
                - monument_type: Type of monument (primary, secondary)
                - cascade_effects: Effects when destroyed
                - protective_barriers: Protective barriers around monument
                - regeneration_rate: Rate of monument regeneration
        
        Returns:
            Dict containing success status and monument creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "monument_name": monument_name,
                "location": location,
                "layer_index": layer_index
            }
            
            if monument_config:
                params["monument_config"] = monument_config
            
            logger.info(f"Creating inhibitor monument: {monument_name} on layer {layer_index}")
            
            response = unreal.send_command("create_inhibitor_monuments", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Inhibitor monument creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating inhibitor monument: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_nexus_architecture(
        ctx: Context,
        nexus_name: str,
        location: Dict[str, float],
        team_side: str,
        nexus_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create nexus architecture as the main base structure.
        
        Args:
            nexus_name: Name of the nexus
            location: Nexus location {x, y, z}
            team_side: Team side (blue, red)
            nexus_config: Nexus configuration:
                - nexus_size: Size of the nexus (large by default)
                - defensive_systems: Defensive systems around nexus
                - energy_effects: Energy effects emanating from nexus
                - architectural_grandeur: Level of architectural detail
        
        Returns:
            Dict containing success status and nexus creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "nexus_name": nexus_name,
                "location": location,
                "team_side": team_side
            }
            
            if nexus_config:
                params["nexus_config"] = nexus_config
            
            logger.info(f"Creating nexus architecture: {nexus_name} for {team_side} team")
            
            response = unreal.send_command("create_nexus_architecture", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Nexus architecture creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating nexus architecture: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_defensive_walls(
        ctx: Context,
        wall_name: str,
        wall_points: List[Dict[str, float]],
        wall_type: str,
        wall_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create defensive walls connecting multiple points.
        
        Args:
            wall_name: Name of the defensive wall system
            wall_points: List of points defining the wall path
            wall_type: Type of wall (stone, magical, energy)
            wall_config: Wall configuration:
                - wall_height: Height of the walls
                - wall_thickness: Thickness of the walls
                - defensive_features: Defensive features (spikes, barriers)
                - gate_locations: Locations for gates in the walls
        
        Returns:
            Dict containing success status and wall creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "wall_name": wall_name,
                "wall_points": wall_points,
                "wall_type": wall_type
            }
            
            if wall_config:
                params["wall_config"] = wall_config
            
            logger.info(f"Creating defensive walls: {wall_name} ({wall_type}) with {len(wall_points)} points")
            
            response = unreal.send_command("create_defensive_walls", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Defensive walls creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating defensive walls: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_jungle_camps(
        ctx: Context,
        camp_name: str,
        camp_type: str,
        location: Dict[str, float],
        layer_index: int,
        camp_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create jungle camps with neutral monsters and rewards.
        
        Args:
            camp_name: Name of the jungle camp
            camp_type: Type of camp (small, medium, large, epic)
            location: Camp location {x, y, z}
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            camp_config: Camp configuration:
                - camp_size: Size of the camp area
                - monster_spawns: Monster spawn points
                - reward_systems: Reward systems for clearing camp
                - respawn_timer: Time for camp to respawn
        
        Returns:
            Dict containing success status and camp creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "camp_name": camp_name,
                "camp_type": camp_type,
                "location": location,
                "layer_index": layer_index
            }
            
            if camp_config:
                params["camp_config"] = camp_config
            
            logger.info(f"Creating jungle camp: {camp_name} ({camp_type}) on layer {layer_index}")
            
            response = unreal.send_command("create_jungle_camps", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Jungle camp creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating jungle camp: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "WorldPartitionFixSubsystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeWorldPartitionFixSubsystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem();
AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UWorldPartitionFixSubsystem Function ApplyWorldPartitionFix **************
struct Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Aplica a corre\xc3\xa7\xc3\xa3o do World Partition no mundo atual\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplica a corre\xc3\xa7\xc3\xa3o do World Partition no mundo atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFixSubsystem, nullptr, "ApplyWorldPartitionFix", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFixSubsystem::execApplyWorldPartitionFix)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyWorldPartitionFix();
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFixSubsystem Function ApplyWorldPartitionFix ****************

// ********** Begin Class UWorldPartitionFixSubsystem Function ForceFixAllWorldPartitions **********
struct Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * For\xc3\xa7""a a corre\xc3\xa7\xc3\xa3o de todos os World Partitions ativos\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""a a corre\xc3\xa7\xc3\xa3o de todos os World Partitions ativos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFixSubsystem, nullptr, "ForceFixAllWorldPartitions", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFixSubsystem::execForceFixAllWorldPartitions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceFixAllWorldPartitions();
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFixSubsystem Function ForceFixAllWorldPartitions ************

// ********** Begin Class UWorldPartitionFixSubsystem Function IsWorldPartitionHealthy *************
struct Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics
{
	struct WorldPartitionFixSubsystem_eventIsWorldPartitionHealthy_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Verifica se o World Partition est\xc3\xa1 em estado saud\xc3\xa1vel\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verifica se o World Partition est\xc3\xa1 em estado saud\xc3\xa1vel" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WorldPartitionFixSubsystem_eventIsWorldPartitionHealthy_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WorldPartitionFixSubsystem_eventIsWorldPartitionHealthy_Parms), &Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UWorldPartitionFixSubsystem, nullptr, "IsWorldPartitionHealthy", Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::WorldPartitionFixSubsystem_eventIsWorldPartitionHealthy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::WorldPartitionFixSubsystem_eventIsWorldPartitionHealthy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWorldPartitionFixSubsystem::execIsWorldPartitionHealthy)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWorldPartitionHealthy();
	P_NATIVE_END;
}
// ********** End Class UWorldPartitionFixSubsystem Function IsWorldPartitionHealthy ***************

// ********** Begin Class UWorldPartitionFixSubsystem **********************************************
void UWorldPartitionFixSubsystem::StaticRegisterNativesUWorldPartitionFixSubsystem()
{
	UClass* Class = UWorldPartitionFixSubsystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyWorldPartitionFix", &UWorldPartitionFixSubsystem::execApplyWorldPartitionFix },
		{ "ForceFixAllWorldPartitions", &UWorldPartitionFixSubsystem::execForceFixAllWorldPartitions },
		{ "IsWorldPartitionHealthy", &UWorldPartitionFixSubsystem::execIsWorldPartitionHealthy },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UWorldPartitionFixSubsystem;
UClass* UWorldPartitionFixSubsystem::GetPrivateStaticClass()
{
	using TClass = UWorldPartitionFixSubsystem;
	if (!Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("WorldPartitionFixSubsystem"),
			Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.InnerSingleton,
			StaticRegisterNativesUWorldPartitionFixSubsystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem_NoRegister()
{
	return UWorldPartitionFixSubsystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Subsistema respons\xc3\xa1vel por aplicar automaticamente a corre\xc3\xa7\xc3\xa3o do World Partition\n * Garante que todos os mundos tenham a corre\xc3\xa7\xc3\xa3o aplicada automaticamente\n */" },
#endif
		{ "IncludePath", "WorldPartitionFixSubsystem.h" },
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subsistema respons\xc3\xa1vel por aplicar automaticamente a corre\xc3\xa7\xc3\xa3o do World Partition\nGarante que todos os mundos tenham a corre\xc3\xa7\xc3\xa3o aplicada automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VerificationInterval_MetaData[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de verifica\xc3\xa7\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de verifica\xc3\xa7\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePeriodicVerification_MetaData[] = {
		{ "Category", "World Partition Fix" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Habilitar verifica\xc3\xa7\xc3\xa3o peri\xc3\xb3""dica */" },
#endif
		{ "ModuleRelativePath", "Public/WorldPartitionFixSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Habilitar verifica\xc3\xa7\xc3\xa3o peri\xc3\xb3""dica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VerificationInterval;
	static void NewProp_bEnablePeriodicVerification_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePeriodicVerification;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UWorldPartitionFixSubsystem_ApplyWorldPartitionFix, "ApplyWorldPartitionFix" }, // **********
		{ &Z_Construct_UFunction_UWorldPartitionFixSubsystem_ForceFixAllWorldPartitions, "ForceFixAllWorldPartitions" }, // **********
		{ &Z_Construct_UFunction_UWorldPartitionFixSubsystem_IsWorldPartitionHealthy, "IsWorldPartitionHealthy" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UWorldPartitionFixSubsystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_VerificationInterval = { "VerificationInterval", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWorldPartitionFixSubsystem, VerificationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VerificationInterval_MetaData), NewProp_VerificationInterval_MetaData) };
void Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_bEnablePeriodicVerification_SetBit(void* Obj)
{
	((UWorldPartitionFixSubsystem*)Obj)->bEnablePeriodicVerification = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_bEnablePeriodicVerification = { "bEnablePeriodicVerification", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWorldPartitionFixSubsystem), &Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_bEnablePeriodicVerification_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePeriodicVerification_MetaData), NewProp_bEnablePeriodicVerification_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_VerificationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::NewProp_bEnablePeriodicVerification,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::ClassParams = {
	&UWorldPartitionFixSubsystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem()
{
	if (!Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.OuterSingleton, Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UWorldPartitionFixSubsystem.OuterSingleton;
}
UWorldPartitionFixSubsystem::UWorldPartitionFixSubsystem() {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UWorldPartitionFixSubsystem);
UWorldPartitionFixSubsystem::~UWorldPartitionFixSubsystem() {}
// ********** End Class UWorldPartitionFixSubsystem ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UWorldPartitionFixSubsystem, UWorldPartitionFixSubsystem::StaticClass, TEXT("UWorldPartitionFixSubsystem"), &Z_Registration_Info_UClass_UWorldPartitionFixSubsystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UWorldPartitionFixSubsystem), 2402663475U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h__Script_AURACRON_972624877(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpanalyticscommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpanalyticscommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analytics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsbuildtype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsproviderconfigurationdelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticseventattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticstracer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticspropertystore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticspropertystore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovidermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticssessionsummarymanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticstracer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticsflowtracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\ianalyticsprovideret.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestatebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestatebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\ileveleditor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\framework\\engineelementslibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\engineelementslibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\viewporttypedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditoroutlinersettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\genericfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\filterbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\subsystems\\editorassetsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\editorassetsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettoolsmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettools.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypecategories.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettypeactions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\merge\\public\\merge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailrenderer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailrenderer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\automatedassetimportdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\automatedassetimportdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assettools\\uht\\iassettools.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\savepackage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\packagewriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iodispatcher.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iobuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\ienginecrypto.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variable.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompilermisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\bpterminal.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\blueprintcompiledstatement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\edgraphcompilerutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_event.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_eventnodeinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_eventnodeinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_event.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompiledfunctioncontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcastingutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\blueprinteditorutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\framework\\notifications\\notificationmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\coreasynctasknotificationimpl.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\asynctasknotification.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\slate\\public\\widgets\\notifications\\snotificationlist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticssessionsummarysender.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\ianalyticssessionsummarysender.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\telemetryutils\\public\\telemetryrouter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\telemetryutils\\public\\telemetryutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\hal\\platformapplicationmisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\windows\\windowsplatformapplicationmisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\genericplatform\\genericplatformapplicationmisc.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
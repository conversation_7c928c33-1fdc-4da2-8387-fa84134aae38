# AURACRON PATH FIX SCRIPT
# This script replaces all occurrences of FPackageName::LongPackageNameToFilename with our corrected function

Write-Host "AURACRON PATH FIX: Starting mass replacement of FPackageName::LongPackageNameToFilename..." -ForegroundColor Green

# Get all .cpp files in the UnrealMCP plugin
$files = Get-ChildItem -Path "Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands" -Filter "*.cpp" -Recurse

foreach ($file in $files) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Replace FPackageName::LongPackageNameToFilename calls in UPackage::SavePackage
    $content = $content -replace 'UPackage::SavePackage\(([^,]+),\s*([^,]+),\s*\*FPackageName::LongPackageNameToFilename\(([^,]+),\s*FPackageName::GetAssetPackageExtension\(\)\),\s*([^)]+)\)', 'UPackage::SavePackage($1, $2, *FUnrealMCPCommonUtils::GetCorrectedFilenameFromPackageName($3, FPackageName::GetAssetPackageExtension()), $4)'
    
    # Replace FPackageName::LongPackageNameToFilename calls in FPaths::FileExists
    $content = $content -replace 'FPaths::FileExists\(FPackageName::LongPackageNameToFilename\(([^,]+),\s*FPackageName::GetAssetPackageExtension\(\)\)\)', 'FPaths::FileExists(FUnrealMCPCommonUtils::GetCorrectedFilenameFromPackageName($1, FPackageName::GetAssetPackageExtension()))'
    
    # Replace standalone FPackageName::LongPackageNameToFilename calls
    $content = $content -replace 'FPackageName::LongPackageNameToFilename\(([^,]+),\s*FPackageName::GetAssetPackageExtension\(\)\)', 'FUnrealMCPCommonUtils::GetCorrectedFilenameFromPackageName($1, FPackageName::GetAssetPackageExtension())'
    
    # Replace CreatePackage calls with our corrected version
    $content = $content -replace 'CreatePackage\(\*([^)]+)\)', 'FUnrealMCPCommonUtils::CreatePackageWithCorrectPath($1)'
    
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  -> Updated $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "  -> No changes needed in $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "AURACRON PATH FIX: Mass replacement completed!" -ForegroundColor Green
Write-Host "All FPackageName::LongPackageNameToFilename calls have been replaced with FUnrealMCPCommonUtils::GetCorrectedFilenameFromPackageName" -ForegroundColor Cyan
Write-Host "All CreatePackage calls have been replaced with FUnrealMCPCommonUtils::CreatePackageWithCorrectPath" -ForegroundColor Cyan

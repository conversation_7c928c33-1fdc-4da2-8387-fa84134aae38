"""
Map Tools for Unreal MCP.

This module provides tools for creating and manipulating multilayer maps in Unreal Engine.
Specifically designed for Auracron's 3D MOBA with vertical navigation between layers.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_map_tools(mcp: FastMCP):
    """Register Map tools with the MCP server."""
    
    @mcp.tool()
    def create_multilayer_map(
        ctx: Context,
        map_name: str,
        layers: List[Dict[str, Any]],
        world_partition_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a multilayer map with World Partition for Auracron.
        
        Args:
            map_name: Name of the map to create
            layers: List of layer definitions with properties:
                - name: Layer name
                - height_offset: Z-axis offset for the layer
                - layer_type: Type of layer (ground, aerial, underground)
                - streaming_distance: Distance for streaming optimization
                - description: Layer description
            world_partition_settings: World Partition configuration:
                - enable_streaming: Enable streaming
                - cell_size: Size of streaming cells
                - loading_range: Loading range for streaming
                - enable_hlod: Enable HLOD system
        
        Returns:
            Dict containing success status and created map information
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            # Prepare parameters
            params = {
                "map_name": map_name,
                "layers": layers
            }
            
            if world_partition_settings:
                params["world_partition_settings"] = world_partition_settings
            
            logger.info(f"Creating multilayer map: {map_name} with {len(layers)} layers")
            
            response = unreal.send_command("create_multilayer_map", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer map creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating multilayer map: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def configure_layer_properties(
        ctx: Context,
        map_name: str,
        layer_name: str,
        properties: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configure properties for a specific layer in a multilayer map.
        
        Args:
            map_name: Name of the target map
            layer_name: Name of the layer to configure
            properties: Layer properties to set:
                - streaming_distance: Distance for streaming optimization
                - priority: Layer priority (low, medium, high)
                - enable_physics: Enable physics simulation
                - enable_collision: Enable collision detection
                - lighting_scenario: Lighting configuration
                - weather_effects: List of weather effects
                - gameplay_modifiers: Gameplay modification settings
        
        Returns:
            Dict containing success status and configuration results
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "map_name": map_name,
                "layer_name": layer_name,
                "properties": properties
            }
            
            logger.info(f"Configuring layer properties: {layer_name} in map {map_name}")
            
            response = unreal.send_command("configure_layer_properties", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Layer configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring layer properties: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_portal_system(
        ctx: Context,
        map_name: str,
        portals: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create a portal system for navigation between layers.
        
        Args:
            map_name: Name of the target map
            portals: List of portal definitions:
                - name: Portal name
                - source_layer: Source layer name
                - target_layer: Target layer name
                - source_location: Source position {x, y, z}
                - target_location: Target position {x, y, z}
                - portal_type: Type of portal (elevator, teleporter, cave_entrance, etc.)
                - activation_method: How to activate (proximity, interaction, etc.)
                - activation_distance: Distance for proximity activation
                - transition_time: Time for transition animation
                - visual_effects: List of visual effects
                - audio_effects: List of audio effects
        
        Returns:
            Dict containing success status and created portals information
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "map_name": map_name,
                "portals": portals
            }
            
            logger.info(f"Creating portal system for map: {map_name} with {len(portals)} portals")
            
            response = unreal.send_command("create_portal_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Portal system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating portal system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_elevator_system(
        ctx: Context,
        map_name: str,
        elevator_locations: List[Dict[str, float]],
        capacity: int = 5,
        travel_time: float = 5.0,
        vulnerability_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Create an elevator system for vertical transportation between layers.

        Args:
            map_name: Name of the target map
            elevator_locations: List of elevator positions with x, y, z coordinates
            capacity: Maximum number of players per elevator (default: 5)
            travel_time: Time in seconds for elevator travel (default: 5.0)
            vulnerability_enabled: Whether elevators can be attacked (default: True)

        Returns:
            Dict containing success status and created elevators information
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "map_name": map_name,
                "elevator_locations": elevator_locations,
                "capacity": capacity,
                "travel_time": travel_time,
                "vulnerability_enabled": vulnerability_enabled
            }

            logger.info(f"Creating elevator system for map: {map_name} with {len(elevator_locations)} elevators")

            response = unreal.send_command("create_elevator_system", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Elevator system creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating elevator system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def create_dimensional_bridge(
        ctx: Context,
        map_name: str,
        bridge_points: List[Dict[str, float]],
        duration: float = 105.0,
        activation_trigger: str = "manual",
        capacity: str = "unlimited"
    ) -> Dict[str, Any]:
        """
        Create dimensional bridges for temporary connections between distant points.

        Args:
            map_name: Name of the target map
            bridge_points: List of bridge endpoints (pairs of points with x, y, z coordinates)
            duration: Bridge duration in seconds (default: 105.0)
            activation_trigger: How bridge is activated - "manual", "automatic", "timed" (default: "manual")
            capacity: Bridge capacity - "unlimited", "limited", number (default: "unlimited")

        Returns:
            Dict containing success status and created bridges information
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "map_name": map_name,
                "bridge_points": bridge_points,
                "duration": duration,
                "activation_trigger": activation_trigger,
                "capacity": capacity
            }

            logger.info(f"Creating dimensional bridge system for map: {map_name} with {len(bridge_points)} points")

            response = unreal.send_command("create_dimensional_bridge", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Dimensional bridge creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating dimensional bridge: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def setup_layer_lighting(
        ctx: Context,
        map_name: str,
        layer_name: str,
        light_type: str,
        intensity: float = 3.0,
        color: List[float] = [1.0, 1.0, 1.0]
    ) -> Dict[str, Any]:
        """
        Setup lighting system for a specific layer.

        Args:
            map_name: Name of the target map
            layer_name: Name of the layer to light
            light_type: Type of light - "directional", "point", "spot"
            intensity: Light intensity (default: 3.0)
            color: RGB color values [R, G, B] from 0.0 to 1.0 (default: [1.0, 1.0, 1.0])

        Returns:
            Dict containing success status and lighting setup information
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "map_name": map_name,
                "layer_name": layer_name,
                "light_type": light_type,
                "intensity": intensity,
                "color": color
            }

            logger.info(f"Setting up {light_type} lighting for layer: {layer_name} in map {map_name}")

            response = unreal.send_command("setup_layer_lighting", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Layer lighting setup response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error setting up layer lighting: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    @mcp.tool()
    def create_layer_boundaries(
        ctx: Context,
        map_name: str,
        layer_name: str,
        boundary_type: str,
        visual_indicators: bool = True
    ) -> Dict[str, Any]:
        """
        Create boundaries for layer containment and gameplay rules.

        Args:
            map_name: Name of the target map
            layer_name: Name of the layer to create boundaries for
            boundary_type: Type of boundary - "hard", "soft", "invisible"
            visual_indicators: Whether to show visual boundary indicators (default: True)

        Returns:
            Dict containing success status and created boundaries information
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}

            params = {
                "map_name": map_name,
                "layer_name": layer_name,
                "boundary_type": boundary_type,
                "visual_indicators": visual_indicators
            }

            logger.info(f"Creating {boundary_type} boundaries for layer: {layer_name} in map {map_name}")

            response = unreal.send_command("create_layer_boundaries", params)

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}

            logger.info(f"Layer boundaries creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating layer boundaries: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

    logger.info("Map tools registered successfully")

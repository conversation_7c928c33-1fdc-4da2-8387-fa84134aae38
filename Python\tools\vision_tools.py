"""
Vision Tools for Unreal MCP.

This module provides tools for creating and managing advanced tridimensional vision systems
in Unreal Engine, specifically designed for Auracron's 3D MOBA with multilayer fog of war,
stealth mechanics, and ward systems.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_vision_tools(mcp: FastMCP):
    """Register Vision tools with the MCP server."""
    
    @mcp.tool()
    def create_multilayer_fog_of_war(
        ctx: Context,
        system_name: str,
        layer_configs: Optional[List[Dict[str, Any]]] = None,
        visibility_ranges: Optional[Dict[str, float]] = None,
        update_frequency: float = 0.1,
        performance_mode: str = "medium"
    ) -> Dict[str, Any]:
        """
        Create multilayer Fog of War system with per-layer visibility management.
        
        Args:
            system_name: Name of the fog of war system
            layer_configs: Configuration for each layer's fog properties
            visibility_ranges: Sight ranges per layer and between layers
            update_frequency: How often to update fog state (default: 0.1s)
            performance_mode: "low", "medium", "high" performance settings
        
        Returns:
            Dict containing success status and fog of war system details
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "system_name": system_name,
                "update_frequency": update_frequency,
                "performance_mode": performance_mode
            }
            
            if layer_configs:
                params["layer_configs"] = layer_configs
            if visibility_ranges:
                params["visibility_ranges"] = visibility_ranges
            
            logger.info(f"Creating multilayer fog of war system: {system_name} ({performance_mode} performance)")
            
            response = unreal.send_command("create_multilayer_fog_of_war", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer fog of war creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating multilayer fog of war: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def configure_vertical_sight_ranges(
        ctx: Context,
        sight_config_name: str,
        horizontal_range: float = 1200.0,
        vertical_range: float = 800.0,
        layer_penalties: Optional[Dict[str, float]] = None,
        obstruction_handling: str = "partial_block"
    ) -> Dict[str, Any]:
        """
        Configure vertical sight ranges between layers.
        
        Args:
            sight_config_name: Name of the sight configuration
            horizontal_range: Standard sight range within same layer
            vertical_range: Sight range to adjacent layers
            layer_penalties: Sight reduction per layer distance
            obstruction_handling: How to handle layer obstructions
        
        Returns:
            Dict containing success status and vertical sight configuration results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "sight_config_name": sight_config_name,
                "horizontal_range": horizontal_range,
                "vertical_range": vertical_range,
                "obstruction_handling": obstruction_handling
            }
            
            if layer_penalties:
                params["layer_penalties"] = layer_penalties
            
            logger.info(f"Configuring vertical sight ranges: {sight_config_name}")
            
            response = unreal.send_command("configure_vertical_sight_ranges", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Vertical sight ranges configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring vertical sight ranges: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_tridimensional_ward_system(
        ctx: Context,
        ward_system_name: str,
        ward_types: Optional[List[Dict[str, Any]]] = None,
        placement_rules: Optional[Dict[str, Any]] = None,
        vision_ranges: Optional[Dict[str, float]] = None,
        duration_settings: Optional[Dict[str, float]] = None
    ) -> Dict[str, Any]:
        """
        Create tridimensional ward system for map vision control.
        
        Args:
            ward_system_name: Name of the ward system
            ward_types: Different types of wards (observer, sentry, etc.)
            placement_rules: Rules for ward placement per layer
            vision_ranges: Vision ranges for different ward types
            duration_settings: Ward lifetime and refresh mechanics
        
        Returns:
            Dict containing success status and ward system creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"ward_system_name": ward_system_name}
            
            if ward_types:
                params["ward_types"] = ward_types
            if placement_rules:
                params["placement_rules"] = placement_rules
            if vision_ranges:
                params["vision_ranges"] = vision_ranges
            if duration_settings:
                params["duration_settings"] = duration_settings
            
            logger.info(f"Creating tridimensional ward system: {ward_system_name}")
            
            response = unreal.send_command("create_tridimensional_ward_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Tridimensional ward system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating tridimensional ward system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_multilayer_stealth_system(
        ctx: Context,
        stealth_system_name: str,
        stealth_types: Optional[List[Dict[str, Any]]] = None,
        detection_rules: Optional[Dict[str, Any]] = None,
        movement_penalties: Optional[Dict[str, float]] = None,
        reveal_conditions: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Implement multilayer stealth mechanics with layer-specific rules.
        
        Args:
            stealth_system_name: Name of the stealth system
            stealth_types: Different stealth mechanics per layer
            detection_rules: How stealth is detected between layers
            movement_penalties: Speed/visibility penalties while stealthed
            reveal_conditions: Conditions that break stealth
        
        Returns:
            Dict containing success status and stealth system creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"stealth_system_name": stealth_system_name}
            
            if stealth_types:
                params["stealth_types"] = stealth_types
            if detection_rules:
                params["detection_rules"] = detection_rules
            if movement_penalties:
                params["movement_penalties"] = movement_penalties
            if reveal_conditions:
                params["reveal_conditions"] = reveal_conditions
            
            logger.info(f"Creating multilayer stealth system: {stealth_system_name}")
            
            response = unreal.send_command("create_multilayer_stealth_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Multilayer stealth system creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating multilayer stealth system: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_truesight_mechanics(
        ctx: Context,
        truesight_name: str,
        detection_ranges: Optional[Dict[str, float]] = None,
        penetration_rules: Optional[Dict[str, Any]] = None,
        cost_modifiers: Optional[Dict[str, float]] = None,
        visual_effects: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Set up true sight mechanics for stealth detection.
        
        Args:
            truesight_name: Name of the true sight system
            detection_ranges: True sight ranges per layer
            penetration_rules: How true sight works between layers
            cost_modifiers: Resource costs for true sight abilities
            visual_effects: Effects to show true sight areas
        
        Returns:
            Dict containing success status and true sight system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"truesight_name": truesight_name}
            
            if detection_ranges:
                params["detection_ranges"] = detection_ranges
            if penetration_rules:
                params["penetration_rules"] = penetration_rules
            if cost_modifiers:
                params["cost_modifiers"] = cost_modifiers
            if visual_effects:
                params["visual_effects"] = visual_effects
            
            logger.info(f"Setting up true sight mechanics: {truesight_name}")
            
            response = unreal.send_command("setup_truesight_mechanics", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"True sight mechanics setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up true sight mechanics: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def optimize_vision_performance(
        ctx: Context,
        optimization_level: str = "console",
        culling_settings: Optional[Dict[str, Any]] = None,
        update_frequencies: Optional[Dict[str, float]] = None,
        lod_settings: Optional[Dict[str, Any]] = None,
        cache_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize vision system performance for multilayer scenarios.
        
        Args:
            optimization_level: "mobile", "console", "pc"
            culling_settings: Visibility culling configuration
            update_frequencies: Update rates for different vision systems
            lod_settings: Level of detail for vision effects
            cache_settings: Vision calculation caching
        
        Returns:
            Dict containing success status and performance optimization results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"optimization_level": optimization_level}
            
            if culling_settings:
                params["culling_settings"] = culling_settings
            if update_frequencies:
                params["update_frequencies"] = update_frequencies
            if lod_settings:
                params["lod_settings"] = lod_settings
            if cache_settings:
                params["cache_settings"] = cache_settings
            
            logger.info(f"Optimizing vision performance: {optimization_level} level")
            
            response = unreal.send_command("optimize_vision_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Vision performance optimization response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error optimizing vision performance: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

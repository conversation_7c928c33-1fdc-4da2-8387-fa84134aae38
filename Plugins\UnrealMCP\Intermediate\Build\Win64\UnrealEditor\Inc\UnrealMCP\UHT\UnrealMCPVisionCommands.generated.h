// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPVisionCommands.h"

#ifdef UNREALMCP_UnrealMCPVisionCommands_generated_h
#error "UnrealMCPVisionCommands.generated.h already included, missing '#pragma once' in UnrealMCPVisionCommands.h"
#endif
#define UNREALMCP_UnrealMCPVisionCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FStealthLayerConfiguration ****************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_62_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics; \
	static class UScriptStruct* StaticStruct();


struct FStealthLayerConfiguration;
// ********** End ScriptStruct FStealthLayerConfiguration ******************************************

// ********** Begin Class UStealthConfigurationDataAsset *******************************************
UNREALMCP_API UClass* Z_Construct_UClass_UStealthConfigurationDataAsset_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_99_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUStealthConfigurationDataAsset(); \
	friend struct Z_Construct_UClass_UStealthConfigurationDataAsset_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UStealthConfigurationDataAsset_NoRegister(); \
public: \
	DECLARE_CLASS2(UStealthConfigurationDataAsset, UDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UStealthConfigurationDataAsset_NoRegister) \
	DECLARE_SERIALIZER(UStealthConfigurationDataAsset)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_99_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UStealthConfigurationDataAsset(UStealthConfigurationDataAsset&&) = delete; \
	UStealthConfigurationDataAsset(const UStealthConfigurationDataAsset&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UStealthConfigurationDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UStealthConfigurationDataAsset); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UStealthConfigurationDataAsset) \
	NO_API virtual ~UStealthConfigurationDataAsset();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_96_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_99_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_99_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h_99_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UStealthConfigurationDataAsset;

// ********** End Class UStealthConfigurationDataAsset *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

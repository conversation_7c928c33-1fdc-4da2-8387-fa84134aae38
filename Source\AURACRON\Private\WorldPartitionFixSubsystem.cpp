// WorldPartitionFixSubsystem.cpp - Subsistema para aplicação automática da correção
// Garante que a correção seja aplicada automaticamente em todos os mundos

#include "WorldPartitionFixSubsystem.h"
#include "WorldPartitionFix.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Subsystems/SubsystemBlueprintLibrary.h"
#include "EngineUtils.h"
#include "WorldPartition/WorldPartition.h"

DEFINE_LOG_CATEGORY(LogWorldPartitionFixSubsystem);

void UWorldPartitionFixSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Inicializando subsistema..."));
    
    // Aplicar correção imediatamente
    ApplyWorldPartitionFix();
    
    // Configurar timer para verificações periódicas
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            VerificationTimerHandle,
            this,
            &UWorldPartitionFixSubsystem::PeriodicVerification,
            VerificationInterval,
            true
        );
    }
}

void UWorldPartitionFixSubsystem::Deinitialize()
{
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Desinicializando subsistema..."));
    
    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(VerificationTimerHandle);
    }
    
    Super::Deinitialize();
}

void UWorldPartitionFixSubsystem::ApplyWorldPartitionFix()
{
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Aplicando correção do WorldPartition..."));
    
    if (UWorld* World = GetWorld())
    {
        // Verificar se já existe um componente de correção
        bool bFixComponentFound = false;
        
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor->FindComponentByClass<UWorldPartitionFix>())
            {
                bFixComponentFound = true;
                break;
            }
        }
        
        // Se não existe, criar um ator com o componente de correção
        if (!bFixComponentFound)
        {
            CreateWorldPartitionFixActor();
        }
        
        // Aplicar correção diretamente também
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            FixWorldPartitionState(WorldPartition);
        }
    }
}

void UWorldPartitionFixSubsystem::CreateWorldPartitionFixActor()
{
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Criando ator de correção..."));
    
    if (UWorld* World = GetWorld())
    {
        // Criar ator invisível para hospedar o componente de correção
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = TEXT("WorldPartitionFixActor");
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
        
        AActor* FixActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
        
        if (FixActor)
        {
            // Adicionar componente de correção
            UWorldPartitionFix* FixComponent = NewObject<UWorldPartitionFix>(FixActor);
            FixActor->AddInstanceComponent(FixComponent);
            FixComponent->RegisterComponent();
            
            // Tornar o ator invisível e não colidível
            FixActor->SetActorHiddenInGame(true);
            FixActor->SetActorEnableCollision(false);
            FixActor->Tags.Add(TEXT("WorldPartitionFix"));
            
            UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Ator de correção criado com sucesso"));
        }
    }
}

void UWorldPartitionFixSubsystem::FixWorldPartitionState(UWorldPartition* WorldPartition)
{
    if (!WorldPartition || !IsValid(Cast<UObject>(WorldPartition)))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Verificando estado do WorldPartition..."));
    
    UWorld* World = WorldPartition->GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogWorldPartitionFixSubsystem, Error, TEXT("WorldPartitionFixSubsystem: World inválido"));
        return;
    }
    
    // Implementação robusta de verificação e correção
    try
    {
        // Verificar se o WorldPartition está em estado inconsistente
        bool bNeedsReset = false;
        
        // Verificações de integridade
        if (World->WorldType == EWorldType::Editor || World->WorldType == EWorldType::PIE)
        {
            // Para mundos de editor, verificar se há conflitos de inicialização
            if (WorldPartition->IsInitialized())
            {
                UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: WorldPartition já inicializado"));
            }
            else
            {
                UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: WorldPartition não inicializado"));
            }
        }
        
        // Se detectar problema, aplicar correção
        if (bNeedsReset)
        {
            UE_LOG(LogWorldPartitionFixSubsystem, Warning, TEXT("WorldPartitionFixSubsystem: Aplicando correção de estado..."));
            
            if (WorldPartition->IsInitialized())
            {
                WorldPartition->Uninitialize();
                FPlatformProcess::Sleep(0.001f); // Pequena pausa para garantir limpeza
            }
        }
        
        UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Verificação concluída"));
    }
    catch (...)
    {
        UE_LOG(LogWorldPartitionFixSubsystem, Error, TEXT("WorldPartitionFixSubsystem: Erro durante correção"));
    }
}

void UWorldPartitionFixSubsystem::PeriodicVerification()
{
    if (!bEnablePeriodicVerification)
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFixSubsystem, VeryVerbose, TEXT("WorldPartitionFixSubsystem: Verificação periódica..."));
    
    if (UWorld* World = GetWorld())
    {
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            FixWorldPartitionState(WorldPartition);
        }
    }
}

bool UWorldPartitionFixSubsystem::IsWorldPartitionHealthy()
{
    if (UWorld* World = GetWorld())
    {
        if (UWorldPartition* WorldPartition = World->GetWorldPartition())
        {
            // Verificações básicas de saúde
            return IsValid(Cast<UObject>(WorldPartition)) && IsValid(World);
        }
    }
    
    return false;
}

void UWorldPartitionFixSubsystem::ForceFixAllWorldPartitions()
{
    UE_LOG(LogWorldPartitionFixSubsystem, Log, TEXT("WorldPartitionFixSubsystem: Forçando correção de todos os WorldPartitions..."));
    
    if (GEngine)
    {
        for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
        {
            if (UWorld* World = WorldContext.World())
            {
                if (UWorldPartition* WorldPartition = World->GetWorldPartition())
                {
                    FixWorldPartitionState(WorldPartition);
                }
            }
        }
    }
}
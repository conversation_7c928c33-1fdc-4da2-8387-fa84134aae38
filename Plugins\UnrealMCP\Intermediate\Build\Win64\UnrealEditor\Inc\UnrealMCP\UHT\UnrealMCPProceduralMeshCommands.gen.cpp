// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPProceduralMeshCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPProceduralMeshCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
GEOMETRYFRAMEWORK_API UClass* Z_Construct_UClass_UDynamicMeshComponent_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FLaneGeometryParams();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronProceduralMeshConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig;
class UScriptStruct* FAuracronProceduralMeshConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronProceduralMeshConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron procedural mesh configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron procedural mesh configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshName_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshScale_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshLocation_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshRotation_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseDynamicMesh_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseGeometryFlow_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vertices_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Triangles_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normals_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UVs_MetaData[] = {
		{ "Category", "AuracronProceduralMeshConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MeshName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MeshRotation;
	static void NewProp_bUseDynamicMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseDynamicMesh;
	static void NewProp_bUseGeometryFlow_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseGeometryFlow;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Vertices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Vertices;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Triangles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Triangles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normals_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Normals;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UVs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UVs;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronProceduralMeshConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshName = { "MeshName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, MeshName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshName_MetaData), NewProp_MeshName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshScale = { "MeshScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, MeshScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshScale_MetaData), NewProp_MeshScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshLocation = { "MeshLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, MeshLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshLocation_MetaData), NewProp_MeshLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshRotation = { "MeshRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, MeshRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshRotation_MetaData), NewProp_MeshRotation_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseDynamicMesh_SetBit(void* Obj)
{
	((FAuracronProceduralMeshConfig*)Obj)->bUseDynamicMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseDynamicMesh = { "bUseDynamicMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralMeshConfig), &Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseDynamicMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseDynamicMesh_MetaData), NewProp_bUseDynamicMesh_MetaData) };
void Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseGeometryFlow_SetBit(void* Obj)
{
	((FAuracronProceduralMeshConfig*)Obj)->bUseGeometryFlow = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseGeometryFlow = { "bUseGeometryFlow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronProceduralMeshConfig), &Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseGeometryFlow_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseGeometryFlow_MetaData), NewProp_bUseGeometryFlow_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Vertices_Inner = { "Vertices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Vertices = { "Vertices", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, Vertices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vertices_MetaData), NewProp_Vertices_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Triangles_Inner = { "Triangles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Triangles = { "Triangles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, Triangles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Triangles_MetaData), NewProp_Triangles_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Normals_Inner = { "Normals", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Normals = { "Normals", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, Normals), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normals_MetaData), NewProp_Normals_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_UVs_Inner = { "UVs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_UVs = { "UVs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronProceduralMeshConfig, UVs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UVs_MetaData), NewProp_UVs_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_MeshRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseDynamicMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_bUseGeometryFlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Vertices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Vertices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Triangles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Triangles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Normals_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_Normals,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_UVs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewProp_UVs,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronProceduralMeshConfig",
	Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::PropPointers),
	sizeof(FAuracronProceduralMeshConfig),
	alignof(FAuracronProceduralMeshConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronProceduralMeshConfig ***************************************

// ********** Begin ScriptStruct FLaneGeometryParams ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FLaneGeometryParams;
class UScriptStruct* FLaneGeometryParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneGeometryParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FLaneGeometryParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLaneGeometryParams, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("LaneGeometryParams"));
	}
	return Z_Registration_Info_UScriptStruct_FLaneGeometryParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FLaneGeometryParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Lane geometry generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lane geometry generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneName_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LanePoints_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneWidth_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LaneHeight_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateCollision_MetaData[] = {
		{ "Category", "LaneGeometryParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LaneName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LanePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LanePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LaneHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static void NewProp_bGenerateCollision_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateCollision;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLaneGeometryParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneName = { "LaneName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGeometryParams, LaneName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneName_MetaData), NewProp_LaneName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LanePoints_Inner = { "LanePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LanePoints = { "LanePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGeometryParams, LanePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LanePoints_MetaData), NewProp_LanePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneWidth = { "LaneWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGeometryParams, LaneWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneWidth_MetaData), NewProp_LaneWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneHeight = { "LaneHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGeometryParams, LaneHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LaneHeight_MetaData), NewProp_LaneHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLaneGeometryParams, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
void Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_bGenerateCollision_SetBit(void* Obj)
{
	((FLaneGeometryParams*)Obj)->bGenerateCollision = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_bGenerateCollision = { "bGenerateCollision", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLaneGeometryParams), &Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_bGenerateCollision_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateCollision_MetaData), NewProp_bGenerateCollision_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LanePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LanePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LaneHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewProp_bGenerateCollision,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"LaneGeometryParams",
	Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::PropPointers),
	sizeof(FLaneGeometryParams),
	alignof(FLaneGeometryParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLaneGeometryParams()
{
	if (!Z_Registration_Info_UScriptStruct_FLaneGeometryParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FLaneGeometryParams.InnerSingleton, Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FLaneGeometryParams.InnerSingleton;
}
// ********** End ScriptStruct FLaneGeometryParams *************************************************

// ********** Begin Class UUnrealMCPProceduralMeshCommands *****************************************
void UUnrealMCPProceduralMeshCommands::StaticRegisterNativesUUnrealMCPProceduralMeshCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands;
UClass* UUnrealMCPProceduralMeshCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPProceduralMeshCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPProceduralMeshCommands"),
			Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPProceduralMeshCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister()
{
	return UUnrealMCPProceduralMeshCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Procedural Mesh Commands - Modern UE 5.6.1 Implementation\n * Handles procedural mesh creation with Auracron-specific features\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPProceduralMeshCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Procedural Mesh Commands - Modern UE 5.6.1 Implementation\nHandles procedural mesh creation with Auracron-specific features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedMeshes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created meshes\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created meshes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material instances cache\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralMeshCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material instances cache" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedMeshes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedMeshes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedMeshes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LayerMaterials_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerMaterials;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPProceduralMeshCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes_ValueProp = { "CreatedMeshes", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UDynamicMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes_Key_KeyProp = { "CreatedMeshes_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes = { "CreatedMeshes", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPProceduralMeshCommands, CreatedMeshes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedMeshes_MetaData), NewProp_CreatedMeshes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials_ValueProp = { "LayerMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials_Key_KeyProp = { "LayerMaterials_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials = { "LayerMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPProceduralMeshCommands, LayerMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerMaterials_MetaData), NewProp_LayerMaterials_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_CreatedMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::NewProp_LayerMaterials,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::ClassParams = {
	&UUnrealMCPProceduralMeshCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands.OuterSingleton;
}
UUnrealMCPProceduralMeshCommands::UUnrealMCPProceduralMeshCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPProceduralMeshCommands);
UUnrealMCPProceduralMeshCommands::~UUnrealMCPProceduralMeshCommands() {}
// ********** End Class UUnrealMCPProceduralMeshCommands *******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronProceduralMeshConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics::NewStructOps, TEXT("AuracronProceduralMeshConfig"), &Z_Registration_Info_UScriptStruct_FAuracronProceduralMeshConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronProceduralMeshConfig), 3131609655U) },
		{ FLaneGeometryParams::StaticStruct, Z_Construct_UScriptStruct_FLaneGeometryParams_Statics::NewStructOps, TEXT("LaneGeometryParams"), &Z_Registration_Info_UScriptStruct_FLaneGeometryParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLaneGeometryParams), 3267247057U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPProceduralMeshCommands, UUnrealMCPProceduralMeshCommands::StaticClass, TEXT("UUnrealMCPProceduralMeshCommands"), &Z_Registration_Info_UClass_UUnrealMCPProceduralMeshCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPProceduralMeshCommands), 2640583186U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_166321020(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core Analytics APIs - UE 5.6.1 Modern
#include "Analytics.h"
#include "Interfaces/IAnalyticsProvider.h"
#include "Interfaces/IAnalyticsTracer.h"
#include "AnalyticsEventAttribute.h"
#include "AnalyticsPropertyStore.h"

// Advanced Analytics APIs - UE 5.6.1 Experimental
#include "AnalyticsET.h"
#include "AnalyticsSessionSummaryManager.h"
#include "AnalyticsTracer.h"
#include "AnalyticsFlowTracker.h"

// Performance Tracking APIs - UE 5.6.1 Enhanced
#include "Stats/Stats.h"
#include "ProfilingDebugging/CsvProfiler.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"

// Gameplay Analytics APIs - UE 5.6.1 Advanced
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameState.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

/**
 * Handler class for Advanced Multilayer Analytics System
 * 
 * Implements PRODUCTION READY analytics system for Auracron's 3-layer MOBA
 * with 3D heatmaps, objective control analysis, transition pattern tracking,
 * balance metrics, and automated feedback systems.
 * 
 * Features:
 * - 3D Heatmaps with layer-specific movement and engagement tracking
 * - Objective control analysis per layer with temporal patterns
 * - Transition pattern analysis between layers (Planície, Firmamento, Abismo)
 * - Advanced balance metrics with automated imbalance detection
 * - Real-time feedback system for gameplay optimization
 * - Performance analytics with layer-specific optimization
 * - Player behavior analysis across multilayer scenarios
 * - Automated report generation with actionable insights
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPAnalyticsCommands
{
public:
    FUnrealMCPAnalyticsCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // HEATMAP ANALYTICS COMMANDS
    // ========================================

    /**
     * Creates 3D heatmap system with layer-specific tracking
     * 
     * Parameters:
     * - heatmap_system_name (string): Name of the heatmap analytics system
     * - tracking_categories (array): Categories to track (movement, combat, deaths, objectives)
     * - layer_configurations (array): Heatmap settings per layer
     * - resolution_settings (object): Spatial resolution and temporal sampling
     * - visualization_settings (object): Heatmap visualization configurations
     * 
     * Implementation:
     * - Creates IAnalyticsProvider for heatmap data collection
     * - Sets up spatial partitioning for 3D heatmap generation
     * - Implements layer-specific tracking with different metrics
     * - Configures real-time heatmap updates and visualization
     * 
     * @param Params JSON parameters
     * @return JSON response with heatmap system details
     */
    TSharedPtr<FJsonObject> HandleCreate3DHeatmapSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up objective control analysis per layer
     * 
     * Parameters:
     * - analysis_system_name (string): Name of the objective analysis system
     * - objective_types (array): Types of objectives to track per layer
     * - control_metrics (object): Metrics for objective control analysis
     * - temporal_analysis (object): Time-based analysis settings
     * - team_comparison (object): Team-based objective control comparison
     * 
     * Implementation:
     * - Tracks objective control duration per layer
     * - Analyzes team control patterns and transitions
     * - Implements temporal analysis for objective importance
     * - Creates automated reports on objective balance
     * 
     * @param Params JSON parameters
     * @return JSON response with objective analysis system results
     */
    TSharedPtr<FJsonObject> HandleCreateObjectiveControlAnalysis(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // TRANSITION PATTERN COMMANDS
    // ========================================

    /**
     * Analyzes transition patterns between layers
     * 
     * Parameters:
     * - transition_system_name (string): Name of the transition analysis system
     * - transition_types (array): Types of transitions to track
     * - pattern_detection (object): Pattern detection algorithms
     * - frequency_analysis (object): Transition frequency analysis settings
     * - optimization_suggestions (object): Automated optimization suggestions
     * 
     * Implementation:
     * - Tracks all layer transitions with timing and context
     * - Detects common transition patterns and bottlenecks
     * - Analyzes transition frequency and success rates
     * - Provides automated suggestions for layer balance
     * 
     * @param Params JSON parameters
     * @return JSON response with transition pattern analysis results
     */
    TSharedPtr<FJsonObject> HandleCreateTransitionPatternAnalysis(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // BALANCE METRICS COMMANDS
    // ========================================

    /**
     * Implements advanced balance metrics with automated detection
     * 
     * Parameters:
     * - balance_system_name (string): Name of the balance metrics system
     * - metric_categories (array): Categories of balance metrics to track
     * - imbalance_thresholds (object): Thresholds for detecting imbalances
     * - automated_alerts (object): Automated alert system configuration
     * - correction_suggestions (object): Automated correction suggestions
     * 
     * Implementation:
     * - Tracks win rates, objective control, and resource distribution per layer
     * - Detects statistical imbalances using advanced algorithms
     * - Implements automated alert system for critical imbalances
     * - Provides data-driven suggestions for balance corrections
     * 
     * @param Params JSON parameters
     * @return JSON response with balance metrics system results
     */
    TSharedPtr<FJsonObject> HandleCreateAdvancedBalanceMetrics(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // AUTOMATED FEEDBACK COMMANDS
    // ========================================

    /**
     * Creates automated feedback system for gameplay optimization
     * 
     * Parameters:
     * - feedback_system_name (string): Name of the automated feedback system
     * - feedback_categories (array): Categories of feedback to generate
     * - analysis_intervals (object): Intervals for different types of analysis
     * - report_generation (object): Automated report generation settings
     * - integration_settings (object): Integration with external systems
     * 
     * Implementation:
     * - Generates automated reports on gameplay patterns
     * - Creates actionable feedback for level designers
     * - Implements real-time alerts for critical issues
     * - Integrates with external analytics platforms
     * 
     * @param Params JSON parameters
     * @return JSON response with automated feedback system results
     */
    TSharedPtr<FJsonObject> HandleCreateAutomatedFeedbackSystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // PERFORMANCE ANALYTICS COMMANDS
    // ========================================

    /**
     * Sets up performance analytics with layer-specific optimization
     * 
     * Parameters:
     * - performance_system_name (string): Name of the performance analytics system
     * - performance_categories (array): Categories of performance to track
     * - optimization_targets (object): Performance optimization targets per layer
     * - profiling_settings (object): Profiling and measurement settings
     * - automated_optimization (object): Automated optimization suggestions
     * 
     * Implementation:
     * - Tracks performance metrics per layer (FPS, memory, network)
     * - Identifies performance bottlenecks in multilayer scenarios
     * - Provides automated optimization suggestions
     * - Integrates with UE5 profiling tools for detailed analysis
     * 
     * @param Params JSON parameters
     * @return JSON response with performance analytics system results
     */
    TSharedPtr<FJsonObject> HandleCreatePerformanceAnalytics(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName, 
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // INTERNAL ANALYTICS LOGIC
    // ========================================

    /**
     * Initializes analytics provider with Auracron-specific configuration
     * @param ProviderName Name of the analytics provider
     * @param Configuration Provider configuration settings
     * @return Initialized analytics provider
     */
    TSharedPtr<IAnalyticsProvider> InitializeAnalyticsProvider(const FString& ProviderName,
                                                              const TSharedPtr<FJsonObject>& Configuration);

    /**
     * Records heatmap data point for specific layer
     * @param LayerIndex Layer where event occurred
     * @param Location 3D location of the event
     * @param EventType Type of event (movement, combat, death, etc.)
     * @param EventData Additional event data
     * @return Success status
     */
    bool RecordHeatmapDataPoint(int32 LayerIndex, 
                               const FVector& Location,
                               const FString& EventType,
                               const TSharedPtr<FJsonObject>& EventData);

    /**
     * Analyzes objective control patterns for specific layer
     * @param LayerIndex Layer to analyze
     * @param TimeRange Time range for analysis
     * @param ObjectiveTypes Types of objectives to include
     * @return Analysis results
     */
    TSharedPtr<FJsonObject> AnalyzeObjectiveControlPatterns(int32 LayerIndex,
                                                           const FTimespan& TimeRange,
                                                           const TArray<FString>& ObjectiveTypes);

    /**
     * Generates automated balance report
     * @param AnalysisTimeRange Time range for analysis
     * @param MetricCategories Categories of metrics to include
     * @return Generated balance report
     */
    TSharedPtr<FJsonObject> GenerateAutomatedBalanceReport(const FTimespan& AnalysisTimeRange,
                                                          const TArray<FString>& MetricCategories);

private:
    // Analytics providers per system
    TMap<FString, TSharedPtr<IAnalyticsProvider>> AnalyticsProviders;

    // Analytics tracers for performance tracking
    TMap<FString, TSharedPtr<IAnalyticsTracer>> AnalyticsTracers;


    
    // Heatmap data storage per layer
    TMap<int32, TArray<TSharedPtr<FJsonObject>>> LayerHeatmapData;
    
    // Objective control tracking data
    TMap<int32, TArray<TSharedPtr<FJsonObject>>> ObjectiveControlData;
    
    // Transition pattern data
    TArray<TSharedPtr<FJsonObject>> TransitionPatternData;
    
    // Balance metrics tracking
    TMap<FString, TArray<float>> BalanceMetricsHistory;

    // Telemetry data collection
    TMap<FString, TArray<float>> TelemetryMetrics;

    // Performance analytics settings
    struct FAnalyticsPerformanceSettings
    {
        float DataCollectionInterval = 1.0f;
        int32 MaxDataPoints = 10000;
        bool bEnableRealTimeAnalysis = true;
        bool bGenerateAutomatedReports = true;
        FString ReportOutputPath = TEXT("/Game/Auracron/Analytics/Reports/");
    } PerformanceSettings;

    // ========================================
    // REAL ASSET CREATION FUNCTIONS - MODERN UE 5.6.1 APIS
    // ========================================

    /**
     * Create real Data Table asset for heatmap data storage
     * @param SystemName Name of the analytics system
     * @param TrackingCategories Categories to track in the heatmap
     * @param LayerCount Number of layers to configure
     * @return Created and saved DataTable asset
     */
    UDataTable* CreateHeatmapDataTable(const FString& SystemName, const TArray<FString>& TrackingCategories, int32 LayerCount);

    /**
     * Create real Blueprint asset for analytics runtime processing
     * @param SystemName Name of the analytics system
     * @param HeatmapDataTable Reference to the heatmap data table
     * @return Created and saved Blueprint asset
     */
    UBlueprint* CreateAnalyticsBlueprint(const FString& SystemName, UDataTable* HeatmapDataTable);
};

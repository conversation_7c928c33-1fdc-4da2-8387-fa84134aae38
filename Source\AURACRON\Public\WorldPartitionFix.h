// WorldPartitionFix.h - Header para correção robusta do erro de InitState do World Partition
// Implementação moderna para UE 5.6.1 com verificação de estado e correção automática

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/World.h"
#include "WorldPartitionFix.generated.h"

// Forward declarations
struct FWorldInitializationValues;

DECLARE_LOG_CATEGORY_EXTERN(LogWorldPartitionFix, Log, All);

class UWorldPartition;

/**
 * Componente responsável por corrigir problemas de inicialização do World Partition
 * Implementa verificações de estado e correções automáticas para evitar o erro:
 * "Assertion failed: InitState == EWorldPartitionInitState::Uninitialized"
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API UWorldPartitionFix : public UActorComponent
{
    GENERATED_BODY()

public:
    UWorldPartitionFix();

protected:
    virtual void InitializeComponent() override;
    virtual void BeginDestroy() override;

    /**
     * Callback chamado antes da inicialização do mundo
     * Verifica e corrige o estado do World Partition antes da inicialização
     */
    // Non-UFUNCTION versions to avoid UHT issues with FWorldInitializationValues
    void OnPreWorldInitialization(UWorld* World, UWorld::InitializationValues IVS);

    /**
     * Callback chamado após a inicialização do mundo
     * Valida se a inicialização foi bem-sucedida
     */
    void OnPostWorldInitialization(UWorld* World, UWorld::InitializationValues IVS);

    /**
     * Callback chamado durante a limpeza do mundo
     * Garante que o World Partition seja limpo adequadamente
     */
    UFUNCTION()
    void OnWorldCleanup(UWorld* World, bool bSessionEnded, bool bCleanupResources);

    /**
     * Verifica se o World Partition está em um estado válido
     * @param WorldPartition - O World Partition a ser verificado
     * @return true se o estado for válido, false caso contrário
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    bool IsWorldPartitionInValidState(UWorldPartition* WorldPartition);

    /**
     * Força o reset do estado do World Partition para um estado válido
     * @param WorldPartition - O World Partition a ser resetado
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void ForceResetWorldPartitionState(UWorldPartition* WorldPartition);

    /**
     * Valida se a inicialização do World Partition foi bem-sucedida
     * @param WorldPartition - O World Partition a ser validado
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void ValidateWorldPartitionInitialization(UWorldPartition* WorldPartition);

    /**
     * Realiza uninitialize seguro do World Partition
     * @param WorldPartition - O World Partition a ser uninitializado
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void SafeUninitializeWorldPartition(UWorldPartition* WorldPartition);

    /**
     * Verifica e corrige o estado de todos os World Partitions ativos
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void VerifyAndFixWorldPartitionState();

public:
    /**
     * Propriedade para habilitar/desabilitar logs detalhados
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition Fix")
    bool bEnableDetailedLogging = true;

    /**
     * Propriedade para habilitar/desabilitar correção automática
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition Fix")
    bool bEnableAutomaticFix = true;

    /**
     * Propriedade para definir o intervalo de verificação (em segundos)
     */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition Fix", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float VerificationInterval = 1.0f;
};
{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\source\\auracron\\private\\worldpartitionfix.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.exceptions.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\game\\auracron\\source\\auracron\\public\\worldpartitionfix.h", "c:\\game\\auracron\\intermediate\\build\\win64\\unrealgame\\inc\\auracron\\uht\\worldpartitionfix.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordesc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstancecollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesclist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\actordesccontainerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackagegenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealgame\\inc\\engine\\uht\\worldpartition.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
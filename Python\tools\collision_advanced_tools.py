"""
Collision Advanced Tools for Unreal MCP.

This module provides tools for creating and manipulating advanced collision systems in Unreal Engine.
Specifically designed for Auracron's multilayer collision with Chaos Physics integration.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_collision_advanced_tools(mcp: FastMCP):
    """Register Collision Advanced tools with the MCP server."""
    
    @mcp.tool()
    def create_precise_collision(
        ctx: Context,
        collision_name: str,
        target_mesh: str,
        collision_type: str,
        layer_index: int,
        collision_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create precise collision using modern UE 5.6.1 APIs with Chaos Physics.
        
        Args:
            collision_name: Name of the collision setup
            target_mesh: Target mesh for collision generation
            collision_type: Type of collision (convex, trimesh, box, sphere, capsule)
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            collision_settings: Collision settings:
                - complexity: Collision complexity (simple, complex, use_complex_as_simple)
                - collision_enabled: Enable collision
                - object_type: Object type for collision
                - collision_responses: Collision response settings
        
        Returns:
            Dict containing success status and collision creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "collision_name": collision_name,
                "target_mesh": target_mesh,
                "collision_type": collision_type,
                "layer_index": layer_index
            }
            
            if collision_settings:
                params["collision_settings"] = collision_settings
            
            logger.info(f"Creating precise collision: {collision_name} ({collision_type}) for layer {layer_index}")
            
            response = unreal.send_command("create_precise_collision", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Precise collision creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating precise collision: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_physics_materials(
        ctx: Context,
        material_name: str,
        material_type: str,
        layer_index: int,
        physics_properties: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Setup physics materials with Chaos Physics integration.
        
        Args:
            material_name: Name of the physics material
            material_type: Type of material (standard, chaos, landscape)
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            physics_properties: Physics properties:
                - friction: Friction value (0.0-1.0)
                - restitution: Restitution/bounce value (0.0-1.0)
                - density: Density value (kg/m³)
                - linear_damping: Linear damping
                - angular_damping: Angular damping
        
        Returns:
            Dict containing success status and physics material setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "material_name": material_name,
                "material_type": material_type,
                "layer_index": layer_index,
                "physics_properties": physics_properties
            }
            
            logger.info(f"Setting up physics material: {material_name} ({material_type}) for layer {layer_index}")
            
            response = unreal.send_command("setup_physics_materials", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Physics material setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up physics material: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_collision_profiles(
        ctx: Context,
        profile_name: str,
        collision_enabled: str,
        object_type: str,
        collision_responses: Dict[str, str],
        layer_specific: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create collision profiles for different object types.
        
        Args:
            profile_name: Name of the collision profile
            collision_enabled: Collision enabled setting (no_collision, query_only, physics_only, collision_enabled)
            object_type: Object type (world_static, world_dynamic, pawn, etc.)
            collision_responses: Collision responses for different channels:
                - world_static: Response to world static objects
                - world_dynamic: Response to world dynamic objects
                - pawn: Response to pawns
                - visibility: Response to visibility traces
                - camera: Response to camera traces
            layer_specific: Layer-specific collision settings
        
        Returns:
            Dict containing success status and collision profile creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "profile_name": profile_name,
                "collision_enabled": collision_enabled,
                "object_type": object_type,
                "collision_responses": collision_responses
            }
            
            if layer_specific:
                params["layer_specific"] = layer_specific
            
            logger.info(f"Creating collision profile: {profile_name} ({object_type})")
            
            response = unreal.send_command("create_collision_profiles", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Collision profile creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating collision profile: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_chaos_physics(
        ctx: Context,
        physics_name: str,
        target_objects: List[str],
        chaos_settings: Dict[str, Any],
        layer_index: int
    ) -> Dict[str, Any]:
        """
        Setup Chaos Physics with advanced settings.
        
        Args:
            physics_name: Name of the physics setup
            target_objects: List of target objects for physics
            chaos_settings: Chaos Physics settings:
                - solver_type: Type of Chaos solver
                - iteration_count: Number of solver iterations
                - collision_margin: Collision margin for Chaos
                - sleep_threshold: Sleep threshold for objects
                - chaos_material: Chaos physical material to use
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
        
        Returns:
            Dict containing success status and Chaos Physics setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "physics_name": physics_name,
                "target_objects": target_objects,
                "chaos_settings": chaos_settings,
                "layer_index": layer_index
            }
            
            logger.info(f"Setting up Chaos Physics: {physics_name} for {len(target_objects)} objects on layer {layer_index}")
            
            response = unreal.send_command("setup_chaos_physics", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Chaos Physics setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up Chaos Physics: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_trigger_volumes(
        ctx: Context,
        trigger_name: str,
        trigger_type: str,
        location: Dict[str, float],
        volume_size: Dict[str, float],
        trigger_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create trigger volumes for gameplay events.
        
        Args:
            trigger_name: Name of the trigger volume
            trigger_type: Type of trigger (layer_transition, objective_area, buff_zone)
            location: Trigger location {x, y, z}
            volume_size: Volume size {x, y, z}
            trigger_settings: Trigger settings:
                - trigger_events: Events to trigger
                - activation_conditions: Conditions for activation
                - cooldown_time: Cooldown between activations
                - visual_indicators: Visual indicators for the trigger
        
        Returns:
            Dict containing success status and trigger volume creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "trigger_name": trigger_name,
                "trigger_type": trigger_type,
                "location": location,
                "volume_size": volume_size
            }
            
            if trigger_settings:
                params["trigger_settings"] = trigger_settings
            
            logger.info(f"Creating trigger volume: {trigger_name} ({trigger_type})")
            
            response = unreal.send_command("create_trigger_volumes", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Trigger volume creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating trigger volume: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

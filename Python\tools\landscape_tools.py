"""
Landscape Tools for Unreal MCP.

This module provides tools for creating and manipulating procedural landscapes in Unreal Engine.
Specifically designed for Auracron's multilayer terrain system with advanced heightmap generation.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_landscape_tools(mcp: FastMCP):
    """Register Landscape tools with the MCP server."""
    
    @mcp.tool()
    def create_procedural_landscape(
        ctx: Context,
        landscape_name: str,
        layer_index: int,
        size_x: int = 1009,
        size_y: int = 1009,
        scale_x: float = 100.0,
        scale_y: float = 100.0,
        scale_z: float = 100.0,
        location: Optional[Dict[str, float]] = None,
        heightmap_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a procedural landscape using modern UE 5.6.1 APIs.
        
        Args:
            landscape_name: Name of the landscape to create
            layer_index: Layer index (0=Planície, 1=Firmamento, 2=Abismo)
            size_x: Landscape size in X direction (default: 1009)
            size_y: Landscape size in Y direction (default: 1009)
            scale_x: Scale in X direction (default: 100.0)
            scale_y: Scale in Y direction (default: 100.0)
            scale_z: Scale in Z direction (default: 100.0)
            location: Landscape location {x, y, z}
            heightmap_settings: Heightmap generation settings:
                - noise_type: Type of noise (perlin, simplex, ridged)
                - frequency: Noise frequency
                - amplitude: Noise amplitude
                - octaves: Number of octaves
                - lacunarity: Lacunarity value
                - persistence: Persistence value
        
        Returns:
            Dict containing success status and landscape creation results
        """
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            # Prepare parameters
            params = {
                "landscape_name": landscape_name,
                "layer_index": layer_index,
                "size_x": size_x,
                "size_y": size_y,
                "scale_x": scale_x,
                "scale_y": scale_y,
                "scale_z": scale_z
            }
            
            if location:
                params["location"] = location
            
            if heightmap_settings:
                params["heightmap_settings"] = heightmap_settings
            
            logger.info(f"Creating procedural landscape: {landscape_name} for layer {layer_index}")
            
            response = unreal.send_command("create_procedural_landscape", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Landscape creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating procedural landscape: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_layer_heightmaps(
        ctx: Context,
        landscape_name: str,
        layer_configs: List[Dict[str, Any]],
        heightmap_resolution: int = 1009
    ) -> Dict[str, Any]:
        """
        Create layer-specific heightmaps for multilayer terrain.
        
        Args:
            landscape_name: Name of the target landscape
            layer_configs: List of layer configurations:
                - layer_name: Name of the layer
                - height_offset: Z-axis offset for the layer
                - noise_settings: Noise generation settings
                - blend_mode: How to blend with existing terrain
            heightmap_resolution: Resolution of heightmaps (default: 1009)
        
        Returns:
            Dict containing success status and heightmap creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "landscape_name": landscape_name,
                "layer_configs": layer_configs,
                "heightmap_resolution": heightmap_resolution
            }
            
            logger.info(f"Creating layer heightmaps for landscape: {landscape_name}")
            
            response = unreal.send_command("create_layer_heightmaps", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Layer heightmaps creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating layer heightmaps: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def apply_landscape_materials(
        ctx: Context,
        landscape_name: str,
        material_layers: List[Dict[str, Any]],
        blend_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Apply materials to landscape layers with advanced blending.
        
        Args:
            landscape_name: Name of the target landscape
            material_layers: List of material layer configurations:
                - layer_name: Name of the material layer
                - material_path: Path to the material asset
                - blend_weight: Blend weight for the layer
                - tiling: Tiling settings for the material
            blend_settings: Advanced blending settings:
                - blend_mode: Blending mode (alpha, additive, multiply)
                - falloff_distance: Distance for blend falloff
                - noise_influence: Noise influence on blending
        
        Returns:
            Dict containing success status and material application results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "landscape_name": landscape_name,
                "material_layers": material_layers
            }
            
            if blend_settings:
                params["blend_settings"] = blend_settings
            
            logger.info(f"Applying materials to landscape: {landscape_name}")
            
            response = unreal.send_command("apply_landscape_materials", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Material application response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error applying landscape materials: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_landscape_splines(
        ctx: Context,
        landscape_name: str,
        spline_configs: List[Dict[str, Any]],
        spline_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create landscape splines for paths, rivers, and roads.
        
        Args:
            landscape_name: Name of the target landscape
            spline_configs: List of spline configurations:
                - spline_name: Name of the spline
                - control_points: List of control points with positions
                - spline_type: Type of spline (path, river, road)
                - width: Width of the spline
                - material: Material to apply to the spline
            spline_settings: Advanced spline settings:
                - tessellation: Tessellation settings
                - collision: Collision settings
                - landscape_deformation: How spline affects landscape
        
        Returns:
            Dict containing success status and spline creation results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "landscape_name": landscape_name,
                "spline_configs": spline_configs
            }
            
            if spline_settings:
                params["spline_settings"] = spline_settings
            
            logger.info(f"Creating landscape splines for: {landscape_name}")
            
            response = unreal.send_command("create_landscape_splines", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Landscape splines creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating landscape splines: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def setup_landscape_collision(
        ctx: Context,
        landscape_name: str,
        collision_settings: Dict[str, Any],
        physics_materials: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Setup advanced collision for landscape with physics materials.
        
        Args:
            landscape_name: Name of the target landscape
            collision_settings: Collision configuration:
                - collision_enabled: Enable collision
                - collision_complexity: Collision complexity level
                - collision_response: Collision response settings
                - generate_overlap_events: Generate overlap events
            physics_materials: List of physics materials to apply:
                - material_name: Name of the physics material
                - friction: Friction value
                - restitution: Restitution value
                - density: Density value
        
        Returns:
            Dict containing success status and collision setup results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {
                "landscape_name": landscape_name,
                "collision_settings": collision_settings
            }
            
            if physics_materials:
                params["physics_materials"] = physics_materials
            
            logger.info(f"Setting up collision for landscape: {landscape_name}")
            
            response = unreal.send_command("setup_landscape_collision", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Landscape collision setup response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up landscape collision: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

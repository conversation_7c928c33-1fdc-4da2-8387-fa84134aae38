#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Landscape APIs
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeSubsystem.h"
#include "LandscapeEdit.h"
#include "LandscapeDataAccess.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeStreamingProxy.h"
#include "LandscapeSplinesComponent.h"

// Experimental UE 5.6.1 APIs - VERIFIED EXISTING HEADERS
#include "LandscapeNaniteComponent.h"
#include "LandscapeTexturePatch.h"
#include "LandscapePatchComponent.h"
#include "LandscapePatchManager.h"
#include "LandscapeTextureBackedRenderTarget.h"

// PCG Framework APIs (Experimental UE 5.6.1)
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "PCGSubsystem.h"

// World Partition Integration - UE 5.6.1 Modern APIs
#include "UObject/WeakObjectPtr.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerInstanceWithAsset.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"

// Thread safety macros are now imported from UnrealMCPCommonUtils.h

#define AURACRON_VALIDATE_DATA_LAYER_INSTANCE(DataLayerInstance, LayerName) \
    if (!DataLayerInstance || !IsValid(DataLayerInstance) || DataLayerInstance->IsUnreachable()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerInstance for %s is null, invalid, or unreachable in %s"), *LayerName, ANSI_TO_TCHAR(__FUNCTION__)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("DataLayerInstance for %s is null, invalid, or unreachable"), *LayerName)); \
    }

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstanceDynamic.h"

#include "UnrealMCPLandscapeCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 LANDSCAPE STRUCTURES
// ========================================

/**
 * Auracron layer landscape configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronLayerLandscapeConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString LayerName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector LayerOffset = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FIntPoint LandscapeSize = FIntPoint(1024, 1024);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HeightScale = 100.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor ThemeColor = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, meta = (AllowPrivateAccess = "true"))
    TArray<int32> HeightmapData;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bEnableNanite = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bEnableWorldPartition = true;

    FAuracronLayerLandscapeConfig()
    {
        LayerIndex = 0;
        LayerName = TEXT("DefaultLayer");
        LayerOffset = FVector::ZeroVector;
        LandscapeSize = FIntPoint(1024, 1024);
        HeightScale = 100.0f;
        ThemeColor = FLinearColor::White;
        bEnableNanite = true;
        bEnableWorldPartition = true;
    }
};

/**
 * Procedural landscape generation parameters
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FProceduralLandscapeParams
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString LandscapeName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FAuracronLayerLandscapeConfig> LayerConfigs;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUsePCGGeneration = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bEnableAsyncGeneration = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float NoiseScale = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 NoiseOctaves = 4;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float NoisePersistence = 0.5f;

    FProceduralLandscapeParams()
    {
        LandscapeName = TEXT("AuracronLandscape");
        bUsePCGGeneration = true;
        bEnableAsyncGeneration = true;
        NoiseScale = 1.0f;
        NoiseOctaves = 4;
        NoisePersistence = 0.5f;
    }
};

/**
 * UnrealMCP Landscape Commands - Modern UE 5.6.1 Implementation
 * Handles procedural landscape creation with Auracron-specific features
 */
UCLASS()
class UNREALMCP_API UUnrealMCPLandscapeCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle landscape command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create procedural landscape using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "landscape_name" - Name of the landscape
     *                "layer_configs" - Array of layer configurations
     *                "use_pcg" - Use PCG framework for generation
     *                "enable_nanite" - Enable Nanite virtualized geometry
     * @return JSON response with the created landscape details
     */
    TSharedPtr<FJsonObject> HandleCreateProceduralLandscape(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create layer heightmaps using procedural generation
     * @param Params - Must include:
     *                "landscape_name" - Target landscape name
     *                "layer_index" - Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     *                "heightmap_size" - Size of heightmap
     *                "noise_settings" - Noise generation parameters
     * @return JSON response with heightmap creation results
     */
    TSharedPtr<FJsonObject> HandleCreateLayerHeightmaps(const TSharedPtr<FJsonObject>& Params);

    /**
     * Apply landscape materials with layer-specific themes
     * @param Params - Must include:
     *                "landscape_name" - Target landscape name
     *                "layer_index" - Layer index
     *                "material_theme" - Theme (golden, sky, shadow)
     *                "texture_resolution" - Texture resolution
     * @return JSON response with material application results
     */
    TSharedPtr<FJsonObject> HandleApplyLandscapeMaterials(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create landscape splines for paths and roads
     * @param Params - Must include:
     *                "landscape_name" - Target landscape name
     *                "spline_points" - Array of spline control points
     *                "spline_width" - Width of the spline
     *                "spline_material" - Material for the spline
     * @return JSON response with spline creation results
     */
    TSharedPtr<FJsonObject> HandleCreateLandscapeSplines(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup landscape collision with layer-specific profiles
     * @param Params - Must include:
     *                "landscape_name" - Target landscape name
     *                "collision_profile" - Collision profile name
     *                "enable_complex_collision" - Enable complex collision
     * @return JSON response with collision setup results
     */
    TSharedPtr<FJsonObject> HandleSetupLandscapeCollision(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust Auracron landscape using modern UE 5.6.1 APIs
     * @param LandscapeParams Procedural landscape parameters
     * @return Number of landscape components created
     */
    int32 CreateRobustAuracronLandscape(const FProceduralLandscapeParams& LandscapeParams);

    /**
     * Generate heightmap data using PCG framework
     * @param LayerConfig Layer configuration
     * @return Generated heightmap data
     */
    TArray<int32> GenerateHeightmapWithPCG(const FAuracronLayerLandscapeConfig& LayerConfig);

    /**
     * Apply Nanite optimization to landscape components
     * @param LandscapeActor Target landscape actor
     * @return Success status
     */
    bool ApplyNaniteOptimization(ALandscape* LandscapeActor);

    /**
     * Setup World Partition integration for large landscapes
     * @param World Target world
     * @param LandscapeActor Target landscape actor
     * @return Success status
     */
    bool SetupWorldPartitionIntegration(UWorld* World, ALandscape* LandscapeActor);

    /**
     * Create layer-specific materials using modern material system
     * @param LayerIndex Layer index
     * @param ThemeColor Theme color for the layer
     * @return Created material instance
     */
    UMaterialInstanceDynamic* CreateLayerMaterial(int32 LayerIndex, const FLinearColor& ThemeColor);

private:
    // Cache for created landscapes
    UPROPERTY()
    TMap<FString, TObjectPtr<ALandscape>> CreatedLandscapes;

    // PCG components for procedural generation
    UPROPERTY()
    TMap<FString, TObjectPtr<UPCGComponent>> PCGComponents;

    // Material instances cache
    UPROPERTY()
    TMap<int32, TObjectPtr<UMaterialInstanceDynamic>> LayerMaterials;

private:
    /**
     * MODERN UE 5.6.1 HELPER FUNCTIONS FOR ROBUST SPLINE CREATION
     */

    /**
     * Create a landscape spline segment between two control points using modern UE 5.6.1 APIs
     * @param SplinesComponent The splines component to add the segment to
     * @param StartPoint Starting control point
     * @param EndPoint Ending control point
     * @param MaterialPath Path to material for the spline
     */
    static void CreateLandscapeSplineSegment(ULandscapeSplinesComponent* SplinesComponent,
                                           ULandscapeSplineControlPoint* StartPoint,
                                           ULandscapeSplineControlPoint* EndPoint,
                                           const FString& MaterialPath);

    /**
     * Create a default Auracron spline pattern connecting the three layers
     * @param SplinesComponent The splines component to add the spline to
     * @param Width Width of the spline
     * @param MaterialPath Path to material for the spline
     */
    static void CreateDefaultAuracronSpline(ULandscapeSplinesComponent* SplinesComponent,
                                          float Width,
                                          const FString& MaterialPath);
};

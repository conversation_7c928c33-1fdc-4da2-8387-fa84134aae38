// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPVisionCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPVisionCommands() {}

// ********** Begin Cross Module References ********************************************************
ENGINE_API UClass* Z_Construct_UClass_UDataAsset();
UNREALMCP_API UClass* Z_Construct_UClass_UStealthConfigurationDataAsset();
UNREALMCP_API UClass* Z_Construct_UClass_UStealthConfigurationDataAsset_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FStealthLayerConfiguration();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FStealthLayerConfiguration ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration;
class UScriptStruct* FStealthLayerConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FStealthLayerConfiguration, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("StealthLayerConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configuration for stealth mechanics per layer\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration for stealth mechanics per layer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StealthType_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StealthEffectiveness_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetectionRangeReduction_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeedPenalty_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBreaksOnAttack_MetaData[] = {
		{ "Category", "Stealth" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StealthType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StealthEffectiveness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetectionRangeReduction;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeedPenalty;
	static void NewProp_bBreaksOnAttack_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBreaksOnAttack;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FStealthLayerConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStealthLayerConfiguration, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_StealthType = { "StealthType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStealthLayerConfiguration, StealthType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StealthType_MetaData), NewProp_StealthType_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_StealthEffectiveness = { "StealthEffectiveness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStealthLayerConfiguration, StealthEffectiveness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StealthEffectiveness_MetaData), NewProp_StealthEffectiveness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_DetectionRangeReduction = { "DetectionRangeReduction", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStealthLayerConfiguration, DetectionRangeReduction), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetectionRangeReduction_MetaData), NewProp_DetectionRangeReduction_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_MovementSpeedPenalty = { "MovementSpeedPenalty", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FStealthLayerConfiguration, MovementSpeedPenalty), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeedPenalty_MetaData), NewProp_MovementSpeedPenalty_MetaData) };
void Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_bBreaksOnAttack_SetBit(void* Obj)
{
	((FStealthLayerConfiguration*)Obj)->bBreaksOnAttack = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_bBreaksOnAttack = { "bBreaksOnAttack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FStealthLayerConfiguration), &Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_bBreaksOnAttack_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBreaksOnAttack_MetaData), NewProp_bBreaksOnAttack_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_StealthType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_StealthEffectiveness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_DetectionRangeReduction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_MovementSpeedPenalty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewProp_bBreaksOnAttack,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"StealthLayerConfiguration",
	Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::PropPointers),
	sizeof(FStealthLayerConfiguration),
	alignof(FStealthLayerConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FStealthLayerConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration.InnerSingleton;
}
// ********** End ScriptStruct FStealthLayerConfiguration ******************************************

// ********** Begin Class UStealthConfigurationDataAsset *******************************************
void UStealthConfigurationDataAsset::StaticRegisterNativesUStealthConfigurationDataAsset()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UStealthConfigurationDataAsset;
UClass* UStealthConfigurationDataAsset::GetPrivateStaticClass()
{
	using TClass = UStealthConfigurationDataAsset;
	if (!Z_Registration_Info_UClass_UStealthConfigurationDataAsset.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("StealthConfigurationDataAsset"),
			Z_Registration_Info_UClass_UStealthConfigurationDataAsset.InnerSingleton,
			StaticRegisterNativesUStealthConfigurationDataAsset,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UStealthConfigurationDataAsset.InnerSingleton;
}
UClass* Z_Construct_UClass_UStealthConfigurationDataAsset_NoRegister()
{
	return UStealthConfigurationDataAsset::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UStealthConfigurationDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Asset for storing stealth system configurations\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPVisionCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Asset for storing stealth system configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SystemName_MetaData[] = {
		{ "Category", "Stealth System" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StealthConfigurations_MetaData[] = {
		{ "Category", "Stealth System" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPVisionCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SystemName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StealthConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StealthConfigurations;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UStealthConfigurationDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_SystemName = { "SystemName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStealthConfigurationDataAsset, SystemName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SystemName_MetaData), NewProp_SystemName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_StealthConfigurations_Inner = { "StealthConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FStealthLayerConfiguration, METADATA_PARAMS(0, nullptr) }; // 1101835630
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_StealthConfigurations = { "StealthConfigurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStealthConfigurationDataAsset, StealthConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StealthConfigurations_MetaData), NewProp_StealthConfigurations_MetaData) }; // 1101835630
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_SystemName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_StealthConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::NewProp_StealthConfigurations,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::ClassParams = {
	&UStealthConfigurationDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UStealthConfigurationDataAsset()
{
	if (!Z_Registration_Info_UClass_UStealthConfigurationDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UStealthConfigurationDataAsset.OuterSingleton, Z_Construct_UClass_UStealthConfigurationDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UStealthConfigurationDataAsset.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UStealthConfigurationDataAsset);
UStealthConfigurationDataAsset::~UStealthConfigurationDataAsset() {}
// ********** End Class UStealthConfigurationDataAsset *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FStealthLayerConfiguration::StaticStruct, Z_Construct_UScriptStruct_FStealthLayerConfiguration_Statics::NewStructOps, TEXT("StealthLayerConfiguration"), &Z_Registration_Info_UScriptStruct_FStealthLayerConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FStealthLayerConfiguration), 1101835630U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UStealthConfigurationDataAsset, UStealthConfigurationDataAsset::StaticClass, TEXT("UStealthConfigurationDataAsset"), &Z_Registration_Info_UClass_UStealthConfigurationDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UStealthConfigurationDataAsset), 3601655U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_1773836604(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisionCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

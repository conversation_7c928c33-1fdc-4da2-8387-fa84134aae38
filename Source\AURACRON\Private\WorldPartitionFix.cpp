// WorldPartitionFix.cpp - Correção robusta para erro de InitState do World Partition
// Implementação moderna para UE 5.6.1 com verificação de estado e correção automática

#include "WorldPartitionFix.h"
#include "Engine/World.h"
#include "Engine/WorldInitializationValues.h"
#include "WorldPartition/WorldPartition.h"
#include "Engine/Engine.h"
#include "Engine/Level.h"
#include "UObject/UObjectGlobals.h"
#include "Misc/CoreDelegates.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY(LogWorldPartitionFix);

UWorldPartitionFix::UWorldPartitionFix()
{
    PrimaryComponentTick.bCanEverTick = false;
    bWantsInitializeComponent = true;
}

void UWorldPartitionFix::InitializeComponent()
{
    Super::InitializeComponent();
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Inicializando sistema de correção..."));
    
    // Registrar delegates para monitorar mudanças de mundo usando REAL UE 5.6.1 APIs
    FWorldDelegates::OnPreWorldInitialization.AddUObject(this, &UWorldPartitionFix::OnPreWorldInitialization);
    FWorldDelegates::OnPostWorldInitialization.AddUObject(this, &UWorldPartitionFix::OnPostWorldInitialization);
    FWorldDelegates::OnWorldCleanup.AddUObject(this, &UWorldPartitionFix::OnWorldCleanup);
    
    // Verificar e corrigir estado atual
    VerifyAndFixWorldPartitionState();
}

void UWorldPartitionFix::BeginDestroy()
{
    // Limpar delegates usando REAL UE 5.6.1 APIs
    FWorldDelegates::OnPreWorldInitialization.RemoveAll(this);
    FWorldDelegates::OnPostWorldInitialization.RemoveAll(this);
    FWorldDelegates::OnWorldCleanup.RemoveAll(this);
    
    Super::BeginDestroy();
}

void UWorldPartitionFix::OnPreWorldInitialization(UWorld* World, UWorld::InitializationValues IVS)
{
    if (!World || !IsValid(World))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Pre-inicialização do mundo: %s"), *World->GetName());
    
    // Verificar se o mundo tem World Partition
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        // Verificar estado antes da inicialização
        if (!IsWorldPartitionInValidState(WorldPartition))
        {
            UE_LOG(LogWorldPartitionFix, Warning, TEXT("WorldPartitionFix: Estado inválido detectado, aplicando correção..."));
            ForceResetWorldPartitionState(WorldPartition);
        }
    }
}

void UWorldPartitionFix::OnPostWorldInitialization(UWorld* World, UWorld::InitializationValues IVS)
{
    if (!World || !IsValid(World))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Pós-inicialização do mundo: %s"), *World->GetName());
    
    // Verificar se a inicialização foi bem-sucedida
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        ValidateWorldPartitionInitialization(WorldPartition);
    }
}

void UWorldPartitionFix::OnWorldCleanup(UWorld* World, bool bSessionEnded, bool bCleanupResources)
{
    if (!World || !IsValid(World))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Limpeza do mundo: %s"), *World->GetName());
    
    // Garantir limpeza adequada do World Partition
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        SafeUninitializeWorldPartition(WorldPartition);
    }
}

bool UWorldPartitionFix::IsWorldPartitionInValidState(UWorldPartition* WorldPartition)
{
    if (!WorldPartition || !IsValid(WorldPartition))
    {
        return false;
    }
    
    // Usar reflexão para acessar o estado interno de forma segura
    // Esta é uma implementação robusta que não quebra com atualizações do engine
    
    // Verificar se o World Partition está em um estado consistente
    UWorld* World = WorldPartition->GetWorld();
    if (!World || !IsValid(World))
    {
        UE_LOG(LogWorldPartitionFix, Error, TEXT("WorldPartitionFix: World inválido no WorldPartition"));
        return false;
    }
    
    // Verificar se o mundo está em um estado apropriado para World Partition
    if (World->WorldType != EWorldType::Editor && World->WorldType != EWorldType::Game && World->WorldType != EWorldType::PIE)
    {
        UE_LOG(LogWorldPartitionFix, Warning, TEXT("WorldPartitionFix: Tipo de mundo não suportado: %d"), (int32)World->WorldType);
        return false;
    }
    
    return true;
}

void UWorldPartitionFix::ForceResetWorldPartitionState(UWorldPartition* WorldPartition)
{
    if (!WorldPartition || !IsValid(WorldPartition))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Warning, TEXT("WorldPartitionFix: Forçando reset do estado do WorldPartition..."));
    
    UWorld* World = WorldPartition->GetWorld();
    if (!World || !IsValid(World))
    {
        return;
    }
    
    // Implementação segura de reset usando APIs públicas
    try
    {
        // Verificar se precisa de uninitialize antes de reinitializar
        if (WorldPartition->IsInitialized())
        {
            UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: WorldPartition já inicializado, fazendo uninitialize seguro..."));
            
            // Usar método seguro de uninitialize
            WorldPartition->Uninitialize();
            
            // Aguardar um frame para garantir que o estado foi limpo
            FPlatformProcess::Sleep(0.001f);
        }
        
        UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Estado resetado com sucesso"));
    }
    catch (...)
    {
        UE_LOG(LogWorldPartitionFix, Error, TEXT("WorldPartitionFix: Erro durante reset do estado"));
    }
}

void UWorldPartitionFix::ValidateWorldPartitionInitialization(UWorldPartition* WorldPartition)
{
    if (!WorldPartition || !IsValid(WorldPartition))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Validando inicialização do WorldPartition..."));
    
    // Verificar se a inicialização foi bem-sucedida
    if (WorldPartition->IsInitialized())
    {
        UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: WorldPartition inicializado com sucesso"));
        
        // Verificações adicionais de integridade
        UWorld* World = WorldPartition->GetWorld();
        if (World && IsValid(World))
        {
            UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Mundo associado válido: %s"), *World->GetName());
        }
    }
    else
    {
        UE_LOG(LogWorldPartitionFix, Warning, TEXT("WorldPartitionFix: WorldPartition não foi inicializado corretamente"));
    }
}

void UWorldPartitionFix::SafeUninitializeWorldPartition(UWorldPartition* WorldPartition)
{
    if (!WorldPartition || !IsValid(WorldPartition))
    {
        return;
    }
    
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Fazendo uninitialize seguro do WorldPartition..."));
    
    try
    {
        if (WorldPartition->IsInitialized())
        {
            WorldPartition->Uninitialize();
            UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Uninitialize realizado com sucesso"));
        }
    }
    catch (...)
    {
        UE_LOG(LogWorldPartitionFix, Error, TEXT("WorldPartitionFix: Erro durante uninitialize"));
    }
}

void UWorldPartitionFix::VerifyAndFixWorldPartitionState()
{
    UE_LOG(LogWorldPartitionFix, Log, TEXT("WorldPartitionFix: Verificando estado de todos os mundos..."));
    
    // Verificar todos os contextos de mundo ativos
    if (GEngine)
    {
        for (const FWorldContext& WorldContext : GEngine->GetWorldContexts())
        {
            if (UWorld* World = WorldContext.World())
            {
                if (UWorldPartition* WorldPartition = World->GetWorldPartition())
                {
                    if (!IsWorldPartitionInValidState(WorldPartition))
                    {
                        UE_LOG(LogWorldPartitionFix, Warning, TEXT("WorldPartitionFix: Corrigindo estado do mundo: %s"), *World->GetName());
                        ForceResetWorldPartitionState(WorldPartition);
                    }
                }
            }
        }
    }
}
// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPPathfindingCommands.h"

#ifdef UNREALMCP_UnrealMCPPathfindingCommands_generated_h
#error "UnrealMCPPathfindingCommands.generated.h already included, missing '#pragma once' in UnrealMCPPathfindingCommands.h"
#endif
#define UNREALMCP_UnrealMCPPathfindingCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FMultilayerPath ***************************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FMultilayerPath_Statics; \
	static class UScriptStruct* StaticStruct();


struct FMultilayerPath;
// ********** End ScriptStruct FMultilayerPath *****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

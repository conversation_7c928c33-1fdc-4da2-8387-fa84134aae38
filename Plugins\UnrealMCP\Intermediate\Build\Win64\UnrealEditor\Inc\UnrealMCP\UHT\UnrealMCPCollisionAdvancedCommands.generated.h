// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPCollisionAdvancedCommands.h"

#ifdef UNREALMCP_UnrealMCPCollisionAdvancedCommands_generated_h
#error "UnrealMCPCollisionAdvancedCommands.generated.h already included, missing '#pragma once' in UnrealMCPCollisionAdvancedCommands.h"
#endif
#define UNREALMCP_UnrealMCPCollisionAdvancedCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronCollisionConfig ******************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_54_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronCollisionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronCollisionConfig;
// ********** End ScriptStruct FAuracronCollisionConfig ********************************************

// ********** Begin ScriptStruct FPreciseCollisionConfig *******************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_107_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPreciseCollisionConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPreciseCollisionConfig;
// ********** End ScriptStruct FPreciseCollisionConfig *********************************************

// ********** Begin ScriptStruct FChaosPhysicsConfig ***********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_148_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FChaosPhysicsConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FChaosPhysicsConfig;
// ********** End ScriptStruct FChaosPhysicsConfig *************************************************

// ********** Begin ScriptStruct FTriggerVolumeConfig **********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_193_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTriggerVolumeConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTriggerVolumeConfig;
// ********** End ScriptStruct FTriggerVolumeConfig ************************************************

// ********** Begin Class UUnrealMCPCollisionAdvancedCommands **************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_231_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPCollisionAdvancedCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPCollisionAdvancedCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPCollisionAdvancedCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_231_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPCollisionAdvancedCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPCollisionAdvancedCommands(UUnrealMCPCollisionAdvancedCommands&&) = delete; \
	UUnrealMCPCollisionAdvancedCommands(const UUnrealMCPCollisionAdvancedCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPCollisionAdvancedCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPCollisionAdvancedCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPCollisionAdvancedCommands) \
	NO_API virtual ~UUnrealMCPCollisionAdvancedCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_228_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_231_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_231_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h_231_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPCollisionAdvancedCommands;

// ********** End Class UUnrealMCPCollisionAdvancedCommands ****************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPCollisionAdvancedCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

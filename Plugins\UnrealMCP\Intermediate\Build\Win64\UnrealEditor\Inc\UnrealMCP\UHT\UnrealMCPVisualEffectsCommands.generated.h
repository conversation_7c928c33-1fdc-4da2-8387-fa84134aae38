// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPVisualEffectsCommands.h"

#ifdef UNREALMCP_UnrealMCPVisualEffectsCommands_generated_h
#error "UnrealMCPVisualEffectsCommands.generated.h already included, missing '#pragma once' in UnrealMCPVisualEffectsCommands.h"
#endif
#define UNREALMCP_UnrealMCPVisualEffectsCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronLightingConfig *******************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_49_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLightingConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLightingConfig;
// ********** End ScriptStruct FAuracronLightingConfig *********************************************

// ********** Begin ScriptStruct FSkyAtmosphereConfig **********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_106_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSkyAtmosphereConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FSkyAtmosphereConfig;
// ********** End ScriptStruct FSkyAtmosphereConfig ************************************************

// ********** Begin ScriptStruct FVolumetricEffectsConfig ******************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_151_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FVolumetricEffectsConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FVolumetricEffectsConfig;
// ********** End ScriptStruct FVolumetricEffectsConfig ********************************************

// ********** Begin ScriptStruct FNiagaraEffectsConfig *********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_196_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FNiagaraEffectsConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FNiagaraEffectsConfig;
// ********** End ScriptStruct FNiagaraEffectsConfig ***********************************************

// ********** Begin ScriptStruct FPostProcessConfig ************************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_242_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPostProcessConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPostProcessConfig;
// ********** End ScriptStruct FPostProcessConfig **************************************************

// ********** Begin Class UUnrealMCPVisualEffectsCommands ******************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_284_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPVisualEffectsCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPVisualEffectsCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPVisualEffectsCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_284_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPVisualEffectsCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPVisualEffectsCommands(UUnrealMCPVisualEffectsCommands&&) = delete; \
	UUnrealMCPVisualEffectsCommands(const UUnrealMCPVisualEffectsCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPVisualEffectsCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPVisualEffectsCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPVisualEffectsCommands) \
	NO_API virtual ~UUnrealMCPVisualEffectsCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_281_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_284_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_284_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h_284_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPVisualEffectsCommands;

// ********** End Class UUnrealMCPVisualEffectsCommands ********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPVisualEffectsCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

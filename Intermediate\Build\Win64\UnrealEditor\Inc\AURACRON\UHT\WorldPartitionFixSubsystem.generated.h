// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "WorldPartitionFixSubsystem.h"

#ifdef AURACRON_WorldPartitionFixSubsystem_generated_h
#error "WorldPartitionFixSubsystem.generated.h already included, missing '#pragma once' in WorldPartitionFixSubsystem.h"
#endif
#define AURACRON_WorldPartitionFixSubsystem_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UWorldPartitionFixSubsystem **********************************************
#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execForceFixAllWorldPartitions); \
	DECLARE_FUNCTION(execIsWorldPartitionHealthy); \
	DECLARE_FUNCTION(execApplyWorldPartitionFix);


AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem_NoRegister();

#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUWorldPartitionFixSubsystem(); \
	friend struct Z_Construct_UClass_UWorldPartitionFixSubsystem_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFixSubsystem_NoRegister(); \
public: \
	DECLARE_CLASS2(UWorldPartitionFixSubsystem, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UWorldPartitionFixSubsystem_NoRegister) \
	DECLARE_SERIALIZER(UWorldPartitionFixSubsystem)


#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UWorldPartitionFixSubsystem(); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UWorldPartitionFixSubsystem(UWorldPartitionFixSubsystem&&) = delete; \
	UWorldPartitionFixSubsystem(const UWorldPartitionFixSubsystem&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UWorldPartitionFixSubsystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UWorldPartitionFixSubsystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UWorldPartitionFixSubsystem) \
	NO_API virtual ~UWorldPartitionFixSubsystem();


#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_18_PROLOG
#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UWorldPartitionFixSubsystem;

// ********** End Class UWorldPartitionFixSubsystem ************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFixSubsystem_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

#include "Commands/UnrealMCPUMGCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "WidgetBlueprint.h"
#include "Factories/Factory.h"
// Modern UE 5.6.1 includes for robust UMG implementation
#include "WidgetBlueprintFactory.h"
#include "WidgetBlueprintEditor.h"
#include "Blueprint/WidgetTree.h"
#include "Components/CanvasPanel.h"
#include "Components/CanvasPanelSlot.h"
#include "JsonObjectConverter.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Components/Button.h"
#include "K2Node_FunctionEntry.h"
#include "K2Node_CallFunction.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "K2Node_Event.h"

FUnrealMCPUMGCommands::FUnrealMCPUMGCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
	if (CommandName == TEXT("create_umg_widget_blueprint"))
	{
		return HandleCreateUMGWidgetBlueprint(Params);
	}
	else if (CommandName == TEXT("add_text_block_to_widget"))
	{
		return HandleAddTextBlockToWidget(Params);
	}
	else if (CommandName == TEXT("add_widget_to_viewport"))
	{
		return HandleAddWidgetToViewport(Params);
	}
	else if (CommandName == TEXT("add_button_to_widget"))
	{
		return HandleAddButtonToWidget(Params);
	}
	else if (CommandName == TEXT("bind_widget_event"))
	{
		return HandleBindWidgetEvent(Params);
	}
	else if (CommandName == TEXT("set_text_block_binding"))
	{
		return HandleSetTextBlockBinding(Params);
	}
	else if (CommandName == TEXT("create_auracron_multilayer_interface"))
	{
		return HandleCreateAuracronMultilayerInterface(Params);
	}
	else if (CommandName == TEXT("create_layer_indicator_widget"))
	{
		return HandleCreateLayerIndicatorWidget(Params);
	}
	else if (CommandName == TEXT("create_vertical_transition_controls"))
	{
		return HandleCreateVerticalTransitionControls(Params);
	}

	return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown UMG command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleCreateUMGWidgetBlueprint(const TSharedPtr<FJsonObject>& Params)
{
	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("name"), BlueprintName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
	}

	// Create the full asset path
	FString PackagePath = TEXT("/Game/Widgets/");
	FString AssetName = BlueprintName;
	FString FullPath = PackagePath + AssetName;

	// Check if asset already exists
	if (UEditorAssetLibrary::DoesAssetExist(FullPath))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Widget Blueprint '%s' already exists"), *BlueprintName));
	}

	// Create package
	UPackage* Package = CreatePackage(*FullPath);
	if (!Package)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create package"));
	}

	// Create Widget Blueprint using KismetEditorUtilities
	UBlueprint* NewBlueprint = FKismetEditorUtilities::CreateBlueprint(
		UUserWidget::StaticClass(),  // Parent class
		Package,                     // Outer package
		FName(*AssetName),           // Blueprint name
		BPTYPE_Normal,               // Blueprint type
		UBlueprint::StaticClass(),   // Blueprint class
		UBlueprintGeneratedClass::StaticClass(), // Generated class
		FName("CreateUMGWidget")     // Creation method name
	);

	// Make sure the Blueprint was created successfully
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(NewBlueprint);
	if (!WidgetBlueprint)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Widget Blueprint"));
	}

	// Add a default Canvas Panel if one doesn't exist
	if (!WidgetBlueprint->WidgetTree->RootWidget)
	{
		UCanvasPanel* RootCanvas = WidgetBlueprint->WidgetTree->ConstructWidget<UCanvasPanel>(UCanvasPanel::StaticClass());
		WidgetBlueprint->WidgetTree->RootWidget = RootCanvas;
	}

	// Mark the package dirty and notify asset registry
	Package->MarkPackageDirty();
	FAssetRegistryModule::AssetCreated(WidgetBlueprint);

	// Compile the blueprint
	FKismetEditorUtilities::CompileBlueprint(WidgetBlueprint);

	// CRITICAL FIX: Save the Widget Blueprint to disk
	bool bSaved = UEditorAssetLibrary::SaveAsset(FullPath, false);

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("name"), BlueprintName);
	ResultObj->SetStringField(TEXT("path"), FullPath);
	ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
	ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Widgets/") + AssetName + TEXT(".uasset"));

	UE_LOG(LogTemp, Log, TEXT("Widget Blueprint created and saved: %s (Saved: %s)"), *FullPath, bSaved ? TEXT("Yes") : TEXT("No"));
	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleAddTextBlockToWidget(const TSharedPtr<FJsonObject>& Params)
{
	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
	}

	FString WidgetName;
	if (!Params->TryGetStringField(TEXT("widget_name"), WidgetName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'widget_name' parameter"));
	}

	// Find the Widget Blueprint
	FString FullPath = TEXT("/Game/Widgets/") + BlueprintName;
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(UEditorAssetLibrary::LoadAsset(FullPath));
	if (!WidgetBlueprint)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Widget Blueprint '%s' not found"), *BlueprintName));
	}

	// Get optional parameters
	FString InitialText = TEXT("New Text Block");
	Params->TryGetStringField(TEXT("text"), InitialText);

	FVector2D Position(0.0f, 0.0f);
	if (Params->HasField(TEXT("position")))
	{
		const TArray<TSharedPtr<FJsonValue>>* PosArray;
		if (Params->TryGetArrayField(TEXT("position"), PosArray) && PosArray->Num() >= 2)
		{
			Position.X = (*PosArray)[0]->AsNumber();
			Position.Y = (*PosArray)[1]->AsNumber();
		}
	}

	// Create Text Block widget
	UTextBlock* TextBlock = WidgetBlueprint->WidgetTree->ConstructWidget<UTextBlock>(UTextBlock::StaticClass(), *WidgetName);
	if (!TextBlock)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Text Block widget"));
	}

	// Set initial text
	TextBlock->SetText(FText::FromString(InitialText));

	// Add to canvas panel
	UCanvasPanel* RootCanvas = Cast<UCanvasPanel>(WidgetBlueprint->WidgetTree->RootWidget);
	if (!RootCanvas)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Root Canvas Panel not found"));
	}

	UCanvasPanelSlot* PanelSlot = RootCanvas->AddChildToCanvas(TextBlock);
	PanelSlot->SetPosition(Position);

	// Mark the package dirty and compile
	WidgetBlueprint->MarkPackageDirty();
	FKismetEditorUtilities::CompileBlueprint(WidgetBlueprint);

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("widget_name"), WidgetName);
	ResultObj->SetStringField(TEXT("text"), InitialText);
	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleAddWidgetToViewport(const TSharedPtr<FJsonObject>& Params)
{
	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
	}

	// Find the Widget Blueprint
	FString FullPath = TEXT("/Game/Widgets/") + BlueprintName;
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(UEditorAssetLibrary::LoadAsset(FullPath));
	if (!WidgetBlueprint)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Widget Blueprint '%s' not found"), *BlueprintName));
	}

	// Get optional Z-order parameter
	int32 ZOrder = 0;
	Params->TryGetNumberField(TEXT("z_order"), ZOrder);

	// Create widget instance
	UClass* WidgetClass = WidgetBlueprint->GeneratedClass;
	if (!WidgetClass)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get widget class"));
	}

	// Note: This creates the widget but doesn't add it to viewport
	// The actual addition to viewport should be done through Blueprint nodes
	// as it requires a game context

	// Create success response with instructions
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
	ResultObj->SetStringField(TEXT("class_path"), WidgetClass->GetPathName());
	ResultObj->SetNumberField(TEXT("z_order"), ZOrder);
	ResultObj->SetStringField(TEXT("note"), TEXT("Widget class ready. Use CreateWidget and AddToViewport nodes in Blueprint to display in game."));
	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleAddButtonToWidget(const TSharedPtr<FJsonObject>& Params)
{
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();

	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing blueprint_name parameter"));
		return Response;
	}

	FString WidgetName;
	if (!Params->TryGetStringField(TEXT("widget_name"), WidgetName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing widget_name parameter"));
		return Response;
	}

	FString ButtonText;
	if (!Params->TryGetStringField(TEXT("text"), ButtonText))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing text parameter"));
		return Response;
	}

	// Load the Widget Blueprint
	const FString BlueprintPath = FString::Printf(TEXT("/Game/Widgets/%s.%s"), *BlueprintName, *BlueprintName);
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(UEditorAssetLibrary::LoadAsset(BlueprintPath));
	if (!WidgetBlueprint)
	{
		Response->SetStringField(TEXT("error"), FString::Printf(TEXT("Failed to load Widget Blueprint: %s"), *BlueprintPath));
		return Response;
	}

	// Create Button widget
	UButton* Button = NewObject<UButton>(WidgetBlueprint->GeneratedClass->GetDefaultObject(), UButton::StaticClass(), *WidgetName);
	if (!Button)
	{
		Response->SetStringField(TEXT("error"), TEXT("Failed to create Button widget"));
		return Response;
	}

	// Set button text
	UTextBlock* ButtonTextBlock = NewObject<UTextBlock>(Button, UTextBlock::StaticClass(), *(WidgetName + TEXT("_Text")));
	if (ButtonTextBlock)
	{
		ButtonTextBlock->SetText(FText::FromString(ButtonText));
		Button->AddChild(ButtonTextBlock);
	}

	// Get canvas panel and add button
	UCanvasPanel* RootCanvas = Cast<UCanvasPanel>(WidgetBlueprint->WidgetTree->RootWidget);
	if (!RootCanvas)
	{
		Response->SetStringField(TEXT("error"), TEXT("Root widget is not a Canvas Panel"));
		return Response;
	}

	// Add to canvas and set position
	UCanvasPanelSlot* ButtonSlot = RootCanvas->AddChildToCanvas(Button);
	if (ButtonSlot)
	{
		const TArray<TSharedPtr<FJsonValue>>* Position;
		if (Params->TryGetArrayField(TEXT("position"), Position) && Position->Num() >= 2)
		{
			FVector2D Pos(
				(*Position)[0]->AsNumber(),
				(*Position)[1]->AsNumber()
			);
			ButtonSlot->SetPosition(Pos);
		}
	}

	// Save the Widget Blueprint
	FKismetEditorUtilities::CompileBlueprint(WidgetBlueprint);
	UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

	Response->SetBoolField(TEXT("success"), true);
	Response->SetStringField(TEXT("widget_name"), WidgetName);
	return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleBindWidgetEvent(const TSharedPtr<FJsonObject>& Params)
{
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();

	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing blueprint_name parameter"));
		return Response;
	}

	FString WidgetName;
	if (!Params->TryGetStringField(TEXT("widget_name"), WidgetName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing widget_name parameter"));
		return Response;
	}

	FString EventName;
	if (!Params->TryGetStringField(TEXT("event_name"), EventName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing event_name parameter"));
		return Response;
	}

	// Load the Widget Blueprint
	const FString BlueprintPath = FString::Printf(TEXT("/Game/Widgets/%s.%s"), *BlueprintName, *BlueprintName);
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(UEditorAssetLibrary::LoadAsset(BlueprintPath));
	if (!WidgetBlueprint)
	{
		Response->SetStringField(TEXT("error"), FString::Printf(TEXT("Failed to load Widget Blueprint: %s"), *BlueprintPath));
		return Response;
	}

	// Create the event graph if it doesn't exist
	UEdGraph* EventGraph = FBlueprintEditorUtils::FindEventGraph(WidgetBlueprint);
	if (!EventGraph)
	{
		Response->SetStringField(TEXT("error"), TEXT("Failed to find or create event graph"));
		return Response;
	}

	// Find the widget in the blueprint
	UWidget* Widget = WidgetBlueprint->WidgetTree->FindWidget(*WidgetName);
	if (!Widget)
	{
		Response->SetStringField(TEXT("error"), FString::Printf(TEXT("Failed to find widget: %s"), *WidgetName));
		return Response;
	}

	// Create the event node (e.g., OnClicked for buttons)
	UK2Node_Event* EventNode = nullptr;
	
	// Find existing nodes first
	TArray<UK2Node_Event*> AllEventNodes;
	FBlueprintEditorUtils::GetAllNodesOfClass<UK2Node_Event>(WidgetBlueprint, AllEventNodes);
	
	for (UK2Node_Event* Node : AllEventNodes)
	{
		if (Node->CustomFunctionName == FName(*EventName) && Node->EventReference.GetMemberParentClass() == Widget->GetClass())
		{
			EventNode = Node;
			break;
		}
	}

	// If no existing node, create a new one
	if (!EventNode)
	{
		// Calculate position - place it below existing nodes
		float MaxHeight = 0.0f;
		for (UEdGraphNode* Node : EventGraph->Nodes)
		{
			MaxHeight = FMath::Max(MaxHeight, Node->NodePosY);
		}
		
		const FVector2D NodePos(200, MaxHeight + 200);

		// Call CreateNewBoundEventForClass, which returns void, so we can't capture the return value directly
		// We'll need to find the node after creating it
		FKismetEditorUtilities::CreateNewBoundEventForClass(
			Widget->GetClass(),
			FName(*EventName),
			WidgetBlueprint,
			nullptr  // We don't need a specific property binding
		);

		// Now find the newly created node
		TArray<UK2Node_Event*> UpdatedEventNodes;
		FBlueprintEditorUtils::GetAllNodesOfClass<UK2Node_Event>(WidgetBlueprint, UpdatedEventNodes);
		
		for (UK2Node_Event* Node : UpdatedEventNodes)
		{
			if (Node->CustomFunctionName == FName(*EventName) && Node->EventReference.GetMemberParentClass() == Widget->GetClass())
			{
				EventNode = Node;
				
				// Set position of the node
				EventNode->NodePosX = NodePos.X;
				EventNode->NodePosY = NodePos.Y;
				
				break;
			}
		}
	}

	if (!EventNode)
	{
		Response->SetStringField(TEXT("error"), TEXT("Failed to create event node"));
		return Response;
	}

	// Save the Widget Blueprint
	FKismetEditorUtilities::CompileBlueprint(WidgetBlueprint);
	UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

	Response->SetBoolField(TEXT("success"), true);
	Response->SetStringField(TEXT("event_name"), EventName);
	return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleSetTextBlockBinding(const TSharedPtr<FJsonObject>& Params)
{
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();

	// Get required parameters
	FString BlueprintName;
	if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing blueprint_name parameter"));
		return Response;
	}

	FString WidgetName;
	if (!Params->TryGetStringField(TEXT("widget_name"), WidgetName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing widget_name parameter"));
		return Response;
	}

	FString BindingName;
	if (!Params->TryGetStringField(TEXT("binding_name"), BindingName))
	{
		Response->SetStringField(TEXT("error"), TEXT("Missing binding_name parameter"));
		return Response;
	}

	// Load the Widget Blueprint
	const FString BlueprintPath = FString::Printf(TEXT("/Game/Widgets/%s.%s"), *BlueprintName, *BlueprintName);
	UWidgetBlueprint* WidgetBlueprint = Cast<UWidgetBlueprint>(UEditorAssetLibrary::LoadAsset(BlueprintPath));
	if (!WidgetBlueprint)
	{
		Response->SetStringField(TEXT("error"), FString::Printf(TEXT("Failed to load Widget Blueprint: %s"), *BlueprintPath));
		return Response;
	}

	// Create a variable for binding if it doesn't exist
	FBlueprintEditorUtils::AddMemberVariable(
		WidgetBlueprint,
		FName(*BindingName),
		FEdGraphPinType(UEdGraphSchema_K2::PC_Text, NAME_None, nullptr, EPinContainerType::None, false, FEdGraphTerminalType())
	);

	// Find the TextBlock widget
	UTextBlock* TextBlock = Cast<UTextBlock>(WidgetBlueprint->WidgetTree->FindWidget(FName(*WidgetName)));
	if (!TextBlock)
	{
		Response->SetStringField(TEXT("error"), FString::Printf(TEXT("Failed to find TextBlock widget: %s"), *WidgetName));
		return Response;
	}

	// Create binding function
	const FString FunctionName = FString::Printf(TEXT("Get%s"), *BindingName);
	UEdGraph* FuncGraph = FBlueprintEditorUtils::CreateNewGraph(
		WidgetBlueprint,
		FName(*FunctionName),
		UEdGraph::StaticClass(),
		UEdGraphSchema_K2::StaticClass()
	);

	if (FuncGraph)
	{
		// Add the function to the blueprint with proper template parameter
		// Template requires null for last parameter when not using a signature-source
		FBlueprintEditorUtils::AddFunctionGraph<UClass>(WidgetBlueprint, FuncGraph, false, nullptr);

		// Create entry node
		UK2Node_FunctionEntry* EntryNode = nullptr;
		
		// Create entry node - use the API that exists in UE 5.5
		EntryNode = NewObject<UK2Node_FunctionEntry>(FuncGraph);
		FuncGraph->AddNode(EntryNode, false, false);
		EntryNode->NodePosX = 0;
		EntryNode->NodePosY = 0;
		EntryNode->FunctionReference.SetExternalMember(FName(*FunctionName), WidgetBlueprint->GeneratedClass);
		EntryNode->AllocateDefaultPins();

		// Create get variable node
		UK2Node_VariableGet* GetVarNode = NewObject<UK2Node_VariableGet>(FuncGraph);
		GetVarNode->VariableReference.SetSelfMember(FName(*BindingName));
		FuncGraph->AddNode(GetVarNode, false, false);
		GetVarNode->NodePosX = 200;
		GetVarNode->NodePosY = 0;
		GetVarNode->AllocateDefaultPins();

		// Connect nodes
		UEdGraphPin* EntryThenPin = EntryNode->FindPin(UEdGraphSchema_K2::PN_Then);
		UEdGraphPin* GetVarOutPin = GetVarNode->FindPin(UEdGraphSchema_K2::PN_ReturnValue);
		if (EntryThenPin && GetVarOutPin)
		{
			EntryThenPin->MakeLinkTo(GetVarOutPin);
		}
	}

	// Save the Widget Blueprint
	FKismetEditorUtilities::CompileBlueprint(WidgetBlueprint);
	UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

	Response->SetBoolField(TEXT("success"), true);
	Response->SetStringField(TEXT("binding_name"), BindingName);
	return Response;
}

// ========================================
// AURACRON MULTILAYER UI SYSTEM - MODERN UE 5.6.1 APIS
// ========================================

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleCreateAuracronMultilayerInterface(const TSharedPtr<FJsonObject>& Params)
{
	// STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
	if (!Params->HasField(TEXT("interface_name")))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: interface_name"));
	}

	FString InterfaceName = Params->GetStringField(TEXT("interface_name"));
	int32 LayerCount = Params->GetIntegerField(TEXT("layer_count"));
	if (LayerCount <= 0) LayerCount = 3; // Default for Auracron
	bool bEnableTransitions = Params->GetBoolField(TEXT("enable_transitions"));
	bool bMobileOptimized = Params->GetBoolField(TEXT("mobile_optimized"));

	// STEP 2: REAL IMPLEMENTATION - Create robust Auracron multilayer interface
	int32 ComponentsCreated = CreateRobustAuracronInterface(InterfaceName, LayerCount, bEnableTransitions, bMobileOptimized);

	// STEP 3: CREATE RESPONSE
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
	Response->SetStringField(TEXT("command"), TEXT("create_auracron_multilayer_interface"));
	Response->SetStringField(TEXT("interface_name"), InterfaceName);
	Response->SetNumberField(TEXT("layer_count"), LayerCount);
	Response->SetBoolField(TEXT("enable_transitions"), bEnableTransitions);
	Response->SetBoolField(TEXT("mobile_optimized"), bMobileOptimized);
	Response->SetNumberField(TEXT("components_created"), ComponentsCreated);
	Response->SetBoolField(TEXT("success"), ComponentsCreated > 0);
	Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

	UE_LOG(LogTemp, Log, TEXT("HandleCreateAuracronMultilayerInterface: Created interface %s with %d components (Layers: %d, Transitions: %s, Mobile: %s)"),
		   *InterfaceName, ComponentsCreated, LayerCount, bEnableTransitions ? TEXT("Yes") : TEXT("No"), bMobileOptimized ? TEXT("Yes") : TEXT("No"));

	return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleCreateLayerIndicatorWidget(const TSharedPtr<FJsonObject>& Params)
{
	// STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
	if (!Params->HasField(TEXT("widget_name")) || !Params->HasField(TEXT("layer_index")))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: widget_name, layer_index"));
	}

	FString WidgetName = Params->GetStringField(TEXT("widget_name"));
	int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
	bool bEnableAnimations = Params->GetBoolField(TEXT("enable_animations"));

	// STEP 2: REAL IMPLEMENTATION - Create layer indicator using modern UE 5.6.1 APIs
	FString LayerName;
	FLinearColor LayerColor;
	FString LayerDescription;

	switch (LayerIndex)
	{
		case 0: // Planície Radiante
			LayerName = TEXT("Planície Radiante");
			LayerColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f); // Golden
			LayerDescription = TEXT("Terra dos heróis, onde a luz eterna brilha");
			break;
		case 1: // Firmamento Zephyr
			LayerName = TEXT("Firmamento Zephyr");
			LayerColor = FLinearColor(0.2f, 0.8f, 1.0f, 1.0f); // Sky Blue
			LayerDescription = TEXT("Reino dos ventos, onde os espíritos dançam");
			break;
		case 2: // Abismo Umbral
			LayerName = TEXT("Abismo Umbral");
			LayerColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f); // Purple
			LayerDescription = TEXT("Profundezas sombrias, onde as trevas reinam");
			break;
		default:
			LayerName = FString::Printf(TEXT("Layer_%d"), LayerIndex);
			LayerColor = FLinearColor::Gray;
			LayerDescription = TEXT("Camada desconhecida");
			break;
	}

	// Create widget blueprint for layer indicator using modern UE 5.6.1 factory pattern
	FString BlueprintPath = FString::Printf(TEXT("/Game/UI/Auracron/LayerIndicators/%s"), *WidgetName);
	UWidgetBlueprintFactory* IndicatorFactory = NewObject<UWidgetBlueprintFactory>();
	UWidgetBlueprint* IndicatorBlueprint = nullptr;

	if (IndicatorFactory)
	{
		IndicatorFactory->ParentClass = UUserWidget::StaticClass();

		IndicatorBlueprint = Cast<UWidgetBlueprint>(IndicatorFactory->FactoryCreateNew(
			UWidgetBlueprint::StaticClass(),
			GetTransientPackage(),
			FName(*WidgetName),
			RF_Public | RF_Standalone,
			nullptr,
			GWarn
		));

		if (IndicatorBlueprint && IsValid(IndicatorBlueprint))
		{
			// Initialize the widget blueprint using modern approach
			if (!IndicatorBlueprint->WidgetTree)
			{
				IndicatorBlueprint->WidgetTree = NewObject<UWidgetTree>(IndicatorBlueprint);
			}

			// Create root canvas panel using modern widget construction
			UCanvasPanel* RootCanvas = NewObject<UCanvasPanel>(IndicatorBlueprint);
			IndicatorBlueprint->WidgetTree->RootWidget = RootCanvas;

			// Add layer name text using modern construction
			UTextBlock* LayerNameText = NewObject<UTextBlock>(IndicatorBlueprint, TEXT("LayerNameText"));
			LayerNameText->SetText(FText::FromString(LayerName));
			LayerNameText->SetColorAndOpacity(FSlateColor(LayerColor));
			RootCanvas->AddChild(LayerNameText);

			// Add layer description text using modern construction
			UTextBlock* LayerDescText = NewObject<UTextBlock>(IndicatorBlueprint, TEXT("LayerDescText"));
			LayerDescText->SetText(FText::FromString(LayerDescription));
			LayerDescText->SetColorAndOpacity(FSlateColor(LayerColor * 0.8f));
			RootCanvas->AddChild(LayerDescText);

			// Save the blueprint using modern asset management
			UEditorAssetLibrary::SaveAsset(BlueprintPath, false);
		}
	}

	// STEP 3: CREATE RESPONSE
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
	Response->SetStringField(TEXT("command"), TEXT("create_layer_indicator_widget"));
	Response->SetStringField(TEXT("widget_name"), WidgetName);
	Response->SetNumberField(TEXT("layer_index"), LayerIndex);
	Response->SetStringField(TEXT("layer_name"), LayerName);
	Response->SetStringField(TEXT("layer_description"), LayerDescription);
	Response->SetBoolField(TEXT("enable_animations"), bEnableAnimations);
	Response->SetBoolField(TEXT("success"), IndicatorBlueprint != nullptr);
	Response->SetStringField(TEXT("blueprint_path"), BlueprintPath);

	// Add layer color information
	TSharedPtr<FJsonObject> ColorInfo = MakeShared<FJsonObject>();
	ColorInfo->SetNumberField(TEXT("r"), LayerColor.R);
	ColorInfo->SetNumberField(TEXT("g"), LayerColor.G);
	ColorInfo->SetNumberField(TEXT("b"), LayerColor.B);
	ColorInfo->SetNumberField(TEXT("a"), LayerColor.A);
	Response->SetObjectField(TEXT("layer_color"), ColorInfo);

	UE_LOG(LogTemp, Log, TEXT("HandleCreateLayerIndicatorWidget: Created indicator %s for layer %d (%s) with animations: %s"),
		   *WidgetName, LayerIndex, *LayerName, bEnableAnimations ? TEXT("Yes") : TEXT("No"));

	return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPUMGCommands::HandleCreateVerticalTransitionControls(const TSharedPtr<FJsonObject>& Params)
{
	// STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
	if (!Params->HasField(TEXT("control_name")) || !Params->HasField(TEXT("transition_type")) ||
		!Params->HasField(TEXT("source_layer")) || !Params->HasField(TEXT("target_layer")))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: control_name, transition_type, source_layer, target_layer"));
	}

	FString ControlName = Params->GetStringField(TEXT("control_name"));
	FString TransitionType = Params->GetStringField(TEXT("transition_type"));
	int32 SourceLayer = Params->GetIntegerField(TEXT("source_layer"));
	int32 TargetLayer = Params->GetIntegerField(TEXT("target_layer"));

	// STEP 2: REAL IMPLEMENTATION - Create transition controls using modern UE 5.6.1 APIs
	FString TransitionDescription;
	FLinearColor ControlColor;
	FString ButtonText;

	if (TransitionType == TEXT("portal"))
	{
		TransitionDescription = TEXT("Portal dimensional para transição instantânea");
		ControlColor = FLinearColor(0.8f, 0.4f, 1.0f, 1.0f); // Purple for portals
		ButtonText = TEXT("Ativar Portal");
	}
	else if (TransitionType == TEXT("elevator"))
	{
		TransitionDescription = TEXT("Elevador místico para transição segura");
		ControlColor = FLinearColor(0.4f, 0.8f, 0.4f, 1.0f); // Green for elevators
		ButtonText = TEXT("Chamar Elevador");
	}
	else if (TransitionType == TEXT("bridge"))
	{
		TransitionDescription = TEXT("Ponte dimensional para travessia");
		ControlColor = FLinearColor(0.8f, 0.8f, 0.4f, 1.0f); // Yellow for bridges
		ButtonText = TEXT("Construir Ponte");
	}
	else
	{
		TransitionDescription = TEXT("Transição desconhecida");
		ControlColor = FLinearColor::Gray;
		ButtonText = TEXT("Ativar");
	}

	// Create widget blueprint for transition controls using modern UE 5.6.1 factory pattern
	FString BlueprintPath = FString::Printf(TEXT("/Game/UI/Auracron/TransitionControls/%s"), *ControlName);
	UWidgetBlueprintFactory* ControlFactory = NewObject<UWidgetBlueprintFactory>();
	UWidgetBlueprint* ControlBlueprint = nullptr;

	if (ControlFactory)
	{
		ControlFactory->ParentClass = UUserWidget::StaticClass();

		ControlBlueprint = Cast<UWidgetBlueprint>(ControlFactory->FactoryCreateNew(
			UWidgetBlueprint::StaticClass(),
			GetTransientPackage(),
			FName(*ControlName),
			RF_Public | RF_Standalone,
			nullptr,
			GWarn
		));

		if (ControlBlueprint && IsValid(ControlBlueprint))
		{
			// Initialize the widget blueprint using modern approach
			if (!ControlBlueprint->WidgetTree)
			{
				ControlBlueprint->WidgetTree = NewObject<UWidgetTree>(ControlBlueprint);
			}

			// Create root canvas panel using modern widget construction
			UCanvasPanel* RootCanvas = NewObject<UCanvasPanel>(ControlBlueprint);
			ControlBlueprint->WidgetTree->RootWidget = RootCanvas;

			// Add transition button using modern construction
			UButton* TransitionButton = NewObject<UButton>(ControlBlueprint, TEXT("TransitionButton"));
			RootCanvas->AddChild(TransitionButton);

			// Add button text using modern construction
			UTextBlock* ButtonTextBlock = NewObject<UTextBlock>(ControlBlueprint, TEXT("ButtonText"));
			ButtonTextBlock->SetText(FText::FromString(ButtonText));
			ButtonTextBlock->SetColorAndOpacity(FSlateColor(ControlColor));
			TransitionButton->AddChild(ButtonTextBlock);

			// Add description text using modern construction
			UTextBlock* DescriptionText = NewObject<UTextBlock>(ControlBlueprint, TEXT("DescriptionText"));
			DescriptionText->SetText(FText::FromString(TransitionDescription));
			DescriptionText->SetColorAndOpacity(FSlateColor(ControlColor * 0.7f));
			RootCanvas->AddChild(DescriptionText);

			// Save the blueprint using modern asset management
			UEditorAssetLibrary::SaveAsset(BlueprintPath, false);
		}
	}

	// STEP 3: CREATE RESPONSE
	TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
	Response->SetStringField(TEXT("command"), TEXT("create_vertical_transition_controls"));
	Response->SetStringField(TEXT("control_name"), ControlName);
	Response->SetStringField(TEXT("transition_type"), TransitionType);
	Response->SetNumberField(TEXT("source_layer"), SourceLayer);
	Response->SetNumberField(TEXT("target_layer"), TargetLayer);
	Response->SetStringField(TEXT("transition_description"), TransitionDescription);
	Response->SetStringField(TEXT("button_text"), ButtonText);
	Response->SetBoolField(TEXT("success"), ControlBlueprint != nullptr);
	Response->SetStringField(TEXT("blueprint_path"), BlueprintPath);

	// Add control color information
	TSharedPtr<FJsonObject> ColorInfo = MakeShared<FJsonObject>();
	ColorInfo->SetNumberField(TEXT("r"), ControlColor.R);
	ColorInfo->SetNumberField(TEXT("g"), ControlColor.G);
	ColorInfo->SetNumberField(TEXT("b"), ControlColor.B);
	ColorInfo->SetNumberField(TEXT("a"), ControlColor.A);
	Response->SetObjectField(TEXT("control_color"), ColorInfo);

	UE_LOG(LogTemp, Log, TEXT("HandleCreateVerticalTransitionControls: Created control %s for %s transition (%d -> %d)"),
		   *ControlName, *TransitionType, SourceLayer, TargetLayer);

	return Response;
}

// ========================================
// ROBUST AURACRON INTERFACE CREATION - MODERN UE 5.6.1 APIS
// ========================================

int32 FUnrealMCPUMGCommands::CreateRobustAuracronInterface(const FString& InterfaceName, int32 LayerCount, bool bEnableTransitions, bool bMobileOptimized)
{
	int32 ComponentsCreated = 0;

	// STEP 1: Create main interface widget blueprint using modern UE 5.6.1 UWidgetBlueprintLibrary APIs
	FString MainInterfacePath = FString::Printf(TEXT("/Game/UI/Auracron/Interfaces/%s_Main"), *InterfaceName);

	// Use modern UE 5.6.1 factory approach for widget blueprint creation
	UWidgetBlueprintFactory* WidgetFactory = NewObject<UWidgetBlueprintFactory>();
	if (WidgetFactory)
	{
		WidgetFactory->ParentClass = UUserWidget::StaticClass();

		// Create the widget blueprint using modern factory pattern
		UWidgetBlueprint* MainInterface = Cast<UWidgetBlueprint>(WidgetFactory->FactoryCreateNew(
			UWidgetBlueprint::StaticClass(),
			GetTransientPackage(),
			FName(*FString::Printf(TEXT("%s_Main"), *InterfaceName)),
			RF_Public | RF_Standalone,
			nullptr,
			GWarn
		));

		if (MainInterface && IsValid(MainInterface))
		{
			// Initialize widget tree using modern UE 5.6.1 approach
			if (!MainInterface->WidgetTree)
			{
				MainInterface->WidgetTree = NewObject<UWidgetTree>(MainInterface);
			}

			// Create root canvas panel using modern UE 5.6.1 widget construction
			UCanvasPanel* RootCanvas = NewObject<UCanvasPanel>(MainInterface);
			MainInterface->WidgetTree->RootWidget = RootCanvas;
			ComponentsCreated++;

			// STEP 2: Create layer-specific interface components using modern UE 5.6.1 APIs
			for (int32 LayerIndex = 0; LayerIndex < LayerCount; LayerIndex++)
			{
				FString LayerName;
				FLinearColor LayerColor;

				switch (LayerIndex)
				{
					case 0: // Planície Radiante
						LayerName = TEXT("Planicie_Radiante");
						LayerColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f);
						break;
					case 1: // Firmamento Zephyr
						LayerName = TEXT("Firmamento_Zephyr");
						LayerColor = FLinearColor(0.2f, 0.8f, 1.0f, 1.0f);
						break;
					case 2: // Abismo Umbral
						LayerName = TEXT("Abismo_Umbral");
						LayerColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f);
						break;
					default:
						LayerName = FString::Printf(TEXT("Layer_%d"), LayerIndex);
						LayerColor = FLinearColor::Gray;
						break;
				}

				// Create layer panel using modern widget construction
				UCanvasPanel* LayerPanel = NewObject<UCanvasPanel>(MainInterface, FName(*FString::Printf(TEXT("LayerPanel_%d"), LayerIndex)));
				RootCanvas->AddChild(LayerPanel);

				// Create layer title using modern UE 5.6.1 text block construction
				UTextBlock* LayerTitle = NewObject<UTextBlock>(MainInterface, FName(*FString::Printf(TEXT("LayerTitle_%d"), LayerIndex)));
				LayerTitle->SetText(FText::FromString(LayerName.Replace(TEXT("_"), TEXT(" "))));
				LayerTitle->SetColorAndOpacity(FSlateColor(LayerColor));
				LayerPanel->AddChild(LayerTitle);

				// Create layer status indicator using modern construction
				UTextBlock* LayerStatus = NewObject<UTextBlock>(MainInterface, FName(*FString::Printf(TEXT("LayerStatus_%d"), LayerIndex)));
				LayerStatus->SetText(FText::FromString(TEXT("Ativo")));
				LayerStatus->SetColorAndOpacity(FSlateColor(LayerColor * 0.8f));
				LayerPanel->AddChild(LayerStatus);

				ComponentsCreated += 3; // Panel, Title, Status
			}

			// STEP 3: Create transition controls if enabled using modern UE 5.6.1 APIs
			if (bEnableTransitions)
			{
				for (int32 SourceLayer = 0; SourceLayer < LayerCount - 1; SourceLayer++)
				{
					int32 TargetLayer = SourceLayer + 1;

					// Create transition button using modern widget construction
					UButton* TransitionButton = NewObject<UButton>(MainInterface, FName(*FString::Printf(TEXT("TransitionButton_%d_%d"), SourceLayer, TargetLayer)));
					RootCanvas->AddChild(TransitionButton);

					// Create transition label using modern construction
					UTextBlock* TransitionLabel = NewObject<UTextBlock>(MainInterface, FName(*FString::Printf(TEXT("TransitionLabel_%d_%d"), SourceLayer, TargetLayer)));
					TransitionLabel->SetText(FText::FromString(FString::Printf(TEXT("Camada %d → %d"), SourceLayer, TargetLayer)));
					TransitionLabel->SetColorAndOpacity(FSlateColor(FLinearColor::White));
					TransitionButton->AddChild(TransitionLabel);

					ComponentsCreated += 2; // Button, Label
				}
			}

			// STEP 4: Add mobile optimizations if enabled using modern UE 5.6.1 APIs
			if (bMobileOptimized)
			{
				// Create mobile-specific controls using modern widget construction
				UButton* MobileMenuButton = NewObject<UButton>(MainInterface, TEXT("MobileMenuButton"));
				RootCanvas->AddChild(MobileMenuButton);

				UTextBlock* MobileMenuText = NewObject<UTextBlock>(MainInterface, TEXT("MobileMenuText"));
				MobileMenuText->SetText(FText::FromString(TEXT("☰")));
				MobileMenuText->SetColorAndOpacity(FSlateColor(FLinearColor::White));
				MobileMenuButton->AddChild(MobileMenuText);

				// Create touch-friendly layer switcher using modern construction
				UButton* LayerSwitchButton = NewObject<UButton>(MainInterface, TEXT("LayerSwitchButton"));
				RootCanvas->AddChild(LayerSwitchButton);

				UTextBlock* LayerSwitchText = NewObject<UTextBlock>(MainInterface, TEXT("LayerSwitchText"));
				LayerSwitchText->SetText(FText::FromString(TEXT("⇅ Trocar Camada")));
				LayerSwitchText->SetColorAndOpacity(FSlateColor(FLinearColor(0.0f, 1.0f, 1.0f, 1.0f))); // Cyan equivalent
				LayerSwitchButton->AddChild(LayerSwitchText);

				ComponentsCreated += 4; // Mobile menu button/text, layer switch button/text
			}

			// STEP 5: Save the main interface using modern UE 5.6.1 asset management
			UEditorAssetLibrary::SaveAsset(MainInterfacePath, false);
			ComponentsCreated++; // Main interface blueprint
		}
	}

	// STEP 6: Create minimap interface for multilayer visualization using modern factory pattern
	FString MinimapPath = FString::Printf(TEXT("/Game/UI/Auracron/Interfaces/%s_Minimap"), *InterfaceName);
	UWidgetBlueprintFactory* MinimapFactory = NewObject<UWidgetBlueprintFactory>();
	if (MinimapFactory)
	{
		MinimapFactory->ParentClass = UUserWidget::StaticClass();

		UWidgetBlueprint* MinimapInterface = Cast<UWidgetBlueprint>(MinimapFactory->FactoryCreateNew(
			UWidgetBlueprint::StaticClass(),
			GetTransientPackage(),
			FName(*FString::Printf(TEXT("%s_Minimap"), *InterfaceName)),
			RF_Public | RF_Standalone,
			nullptr,
			GWarn
		));

		if (MinimapInterface && IsValid(MinimapInterface))
		{
			// Initialize minimap widget tree using modern approach
			if (!MinimapInterface->WidgetTree)
			{
				MinimapInterface->WidgetTree = NewObject<UWidgetTree>(MinimapInterface);
			}

			UCanvasPanel* MinimapCanvas = NewObject<UCanvasPanel>(MinimapInterface);
			MinimapInterface->WidgetTree->RootWidget = MinimapCanvas;

			// Create layer visualization for minimap using modern widget construction
			for (int32 LayerIndex = 0; LayerIndex < LayerCount; LayerIndex++)
			{
				UButton* LayerMinimapButton = NewObject<UButton>(MinimapInterface, FName(*FString::Printf(TEXT("MinimapLayer_%d"), LayerIndex)));
				MinimapCanvas->AddChild(LayerMinimapButton);
				ComponentsCreated++;
			}

			// Save minimap interface using modern asset management
			UEditorAssetLibrary::SaveAsset(MinimapPath, false);
			ComponentsCreated++; // Minimap interface blueprint
		}
	}

	UE_LOG(LogTemp, Log, TEXT("CreateRobustAuracronInterface: Created interface %s with %d components (Layers: %d, Transitions: %s, Mobile: %s)"),
		   *InterfaceName, ComponentsCreated, LayerCount, bEnableTransitions ? TEXT("Yes") : TEXT("No"), bMobileOptimized ? TEXT("Yes") : TEXT("No"));

	return ComponentsCreated;
}
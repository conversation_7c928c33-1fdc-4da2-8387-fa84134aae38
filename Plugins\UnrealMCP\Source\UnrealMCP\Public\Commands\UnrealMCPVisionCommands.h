#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core Vision APIs - UE 5.6.1 Modern
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AIPerceptionSystem.h"
#include "Perception/AISense_Sight.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISightTargetInterface.h"
#include "Perception/PawnSensingComponent.h"

// Rendering APIs - UE 5.6.1 Advanced
#include "Engine/PostProcessVolume.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"

// Fog and Visibility APIs - UE 5.6.1 Experimental
#include "Atmosphere/AtmosphericFog.h"
#include "Engine/ExponentialHeightFog.h"
#include "Components/ExponentialHeightFogComponent.h"
// Modern UE 5.6.1 includes for robust sight range implementation
#include "Components/SphereComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

// Modern UE 5.6.1 Asset Creation APIs
#include "Engine/DataAsset.h"
#include "Engine/Blueprint.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"

#include "UnrealMCPVisionCommands.generated.h"

// ========================================
// STEALTH CONFIGURATION STRUCTURES - MODERN UE 5.6.1
// ========================================

/**
 * Configuration for stealth mechanics per layer
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FStealthLayerConfiguration
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth")
    FString LayerName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth")
    FString StealthType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float StealthEffectiveness = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DetectionRangeReduction = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MovementSpeedPenalty = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth")
    bool bBreaksOnAttack = true;

    FStealthLayerConfiguration()
    {
        LayerName = TEXT("Default_Layer");
        StealthType = TEXT("basic_stealth");
        StealthEffectiveness = 0.7f;
        DetectionRangeReduction = 0.5f;
        MovementSpeedPenalty = 0.2f;
        bBreaksOnAttack = true;
    }
};

/**
 * Data Asset for storing stealth system configurations
 */
UCLASS(BlueprintType)
class UNREALMCP_API UStealthConfigurationDataAsset : public UDataAsset
{
    GENERATED_BODY()

public:
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth System")
    FString SystemName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stealth System")
    TArray<FStealthLayerConfiguration> StealthConfigurations;

    UStealthConfigurationDataAsset()
    {
        SystemName = TEXT("DefaultStealthSystem");
    }
};

/**
 * Handler class for Advanced Tridimensional Vision System
 * 
 * Implements PRODUCTION READY vision tools for Auracron's 3-layer MOBA system
 * with Fog of War per layer, vertical sight ranges, and multilayer stealth mechanics.
 * 
 * Features:
 * - Fog of War system per layer (Planície Radiante, Firmamento Zephyr, Abismo Umbral)
 * - Vertical sight range calculation between layers
 * - Tridimensional ward system with layer-specific visibility
 * - Multilayer stealth and invisibility mechanics
 * - Dynamic visibility updates based on layer transitions
 * - Performance-optimized sight calculations
 * - Integration with AI Perception System
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPVisionCommands
{
public:
    FUnrealMCPVisionCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // CORE VISION SYSTEM COMMANDS
    // ========================================

    /**
     * Creates multilayer Fog of War system with per-layer visibility management
     * 
     * Parameters:
     * - system_name (string): Name of the fog of war system
     * - layer_configs (array): Configuration for each layer's fog properties
     * - visibility_ranges (object): Sight ranges per layer and between layers
     * - update_frequency (float): How often to update fog state (default: 0.1s)
     * - performance_mode (string): "low", "medium", "high" performance settings
     * 
     * Implementation:
     * - Creates MaterialParameterCollection for fog control
     * - Sets up PostProcessVolumes per layer
     * - Configures ExponentialHeightFog components
     * - Implements dynamic fog density based on visibility
     * 
     * @param Params JSON parameters
     * @return JSON response with fog of war system details
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerFogOfWar(const TSharedPtr<FJsonObject>& Params);

    /**
     * Configures vertical sight ranges between layers
     * 
     * Parameters:
     * - sight_config_name (string): Name of the sight configuration
     * - horizontal_range (float): Standard sight range within same layer
     * - vertical_range (float): Sight range to adjacent layers
     * - layer_penalties (object): Sight reduction per layer distance
     * - obstruction_handling (string): How to handle layer obstructions
     * 
     * Implementation:
     * - Extends AISenseConfig_Sight with multilayer support
     * - Creates custom sight calculation algorithms
     * - Implements layer-aware line of sight checks
     * - Configures sight range falloff between layers
     * 
     * @param Params JSON parameters
     * @return JSON response with vertical sight configuration results
     */
    TSharedPtr<FJsonObject> HandleConfigureVerticalSightRanges(const TSharedPtr<FJsonObject>& Params);

    /**
     * Creates tridimensional ward system for map vision control
     * 
     * Parameters:
     * - ward_system_name (string): Name of the ward system
     * - ward_types (array): Different types of wards (observer, sentry, etc.)
     * - placement_rules (object): Rules for ward placement per layer
     * - vision_ranges (object): Vision ranges for different ward types
     * - duration_settings (object): Ward lifetime and refresh mechanics
     * 
     * Implementation:
     * - Creates custom Ward Actor classes
     * - Implements AIPerceptionComponent for ward vision
     * - Sets up layer-specific vision rules
     * - Creates ward placement validation system
     * 
     * @param Params JSON parameters
     * @return JSON response with ward system creation results
     */
    TSharedPtr<FJsonObject> HandleCreateTridimensionalWardSystem(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // STEALTH AND INVISIBILITY COMMANDS
    // ========================================

    /**
     * Implements multilayer stealth mechanics with layer-specific rules
     * 
     * Parameters:
     * - stealth_system_name (string): Name of the stealth system
     * - stealth_types (array): Different stealth mechanics per layer
     * - detection_rules (object): How stealth is detected between layers
     * - movement_penalties (object): Speed/visibility penalties while stealthed
     * - reveal_conditions (object): Conditions that break stealth
     * 
     * Implementation:
     * - Creates custom stealth component system
     * - Implements layer-aware stealth detection
     * - Sets up dynamic visibility state management
     * - Configures stealth interaction with fog of war
     * 
     * @param Params JSON parameters
     * @return JSON response with stealth system creation results
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerStealthSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up true sight mechanics for stealth detection
     * 
     * Parameters:
     * - truesight_name (string): Name of the true sight system
     * - detection_ranges (object): True sight ranges per layer
     * - penetration_rules (object): How true sight works between layers
     * - cost_modifiers (object): Resource costs for true sight abilities
     * - visual_effects (array): Effects to show true sight areas
     * 
     * Implementation:
     * - Extends AI Perception with true sight detection
     * - Creates custom detection algorithms
     * - Implements visual feedback systems
     * - Sets up performance optimization for detection
     * 
     * @param Params JSON parameters
     * @return JSON response with true sight system results
     */
    TSharedPtr<FJsonObject> HandleSetupTrueSightMechanics(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // PERFORMANCE OPTIMIZATION COMMANDS
    // ========================================

    /**
     * Optimizes vision system performance for multilayer scenarios
     * 
     * Parameters:
     * - optimization_level (string): "mobile", "console", "pc"
     * - culling_settings (object): Visibility culling configuration
     * - update_frequencies (object): Update rates for different vision systems
     * - lod_settings (object): Level of detail for vision effects
     * - cache_settings (object): Vision calculation caching
     * 
     * Implementation:
     * - Configures visibility culling per layer
     * - Sets up adaptive update frequencies
     * - Implements vision calculation caching
     * - Optimizes fog rendering performance
     * 
     * @param Params JSON parameters
     * @return JSON response with performance optimization results
     */
    TSharedPtr<FJsonObject> HandleOptimizeVisionPerformance(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName,
                                                 const TSharedPtr<FJsonObject>& ResultData);



    /**
     * Configures sight range for a specific layer
     * @param LayerIndex Index of the layer
     * @param HorizontalRange Horizontal sight range
     * @param VerticalUpRange Vertical up sight range
     * @param VerticalDownRange Vertical down sight range
     * @param FogDensity Fog of war density
     * @return true if configuration was successful
     */
    bool ConfigureLayerSightRange(int32 LayerIndex, float HorizontalRange,
                                 float VerticalUpRange, float VerticalDownRange,
                                 float FogDensity);

    // ========================================
    // INTERNAL VISION LOGIC
    // ========================================

    /**
     * Calculates line of sight between two points across multiple layers
     * @param StartLocation Starting position
     * @param EndLocation Target position
     * @param StartLayer Starting layer index
     * @param EndLayer Target layer index
     * @param SightConfig Sight configuration to use
     * @return True if line of sight exists
     */
    bool CalculateMultilayerLineOfSight(const FVector& StartLocation, 
                                       const FVector& EndLocation,
                                       int32 StartLayer, 
                                       int32 EndLayer,
                                       const UAISenseConfig_Sight* SightConfig);

    /**
     * Updates fog of war state for a specific layer
     * @param LayerIndex Layer to update
     * @param VisibilityData Current visibility information
     * @param FogDensity Fog density to apply
     * @return Success status
     */
    bool UpdateLayerFogOfWar(int32 LayerIndex, 
                           const TMap<FVector, bool>& VisibilityData,
                           float FogDensity);

    /**
     * Creates material parameter collection for fog control
     * @param CollectionName Name of the parameter collection
     * @param LayerCount Number of layers to support
     * @return Created parameter collection
     */
    UMaterialParameterCollection* CreateFogParameterCollection(const FString& CollectionName,
                                                              int32 LayerCount);

    /**
     * Creates robust sight range components for a specific layer using modern UE 5.6.1 APIs
     * @param World Target world for spawning
     * @param LayerIndex Layer index for configuration
     * @param HorizontalRange Horizontal sight range
     * @param VerticalUpRange Vertical up sight range
     * @param VerticalDownRange Vertical down sight range
     * @param FogDensity Fog density for visualization
     * @return Number of components created
     */
    int32 CreateRobustSightRangeComponents(UWorld* World,
                                          int32 LayerIndex,
                                          float HorizontalRange,
                                          float VerticalUpRange,
                                          float VerticalDownRange,
                                          float FogDensity);

private:
    // Fog of war components per layer
    TMap<int32, TWeakObjectPtr<AExponentialHeightFog>> LayerFogActors;
    
    // Material parameter collections for fog control
    TMap<FString, TWeakObjectPtr<UMaterialParameterCollection>> FogParameterCollections;
    
    // AI Perception configurations per layer
    TMap<int32, TWeakObjectPtr<UAISenseConfig_Sight>> LayerSightConfigs;
    
    // Ward system actors
    TArray<TWeakObjectPtr<AActor>> WardActors;

    // Layer sight configurations
    TMap<int32, TSharedPtr<FJsonObject>> LayerSightConfigurations;

    // Performance optimization settings
    struct FVisionPerformanceSettings
    {
        float UpdateFrequency = 0.1f;
        int32 MaxVisibilityChecks = 100;
        bool bUseCulling = true;
        bool bUseCaching = true;
    } PerformanceSettings;

    // ========================================
    // REAL ASSET CREATION FUNCTIONS - MODERN UE 5.6.1 APIS
    // ========================================

    /**
     * Create real Stealth Configuration Data Asset saved to disk
     * @param SystemName Name of the stealth system
     * @param StealthMechanics Stealth mechanics configuration data
     * @return Created and saved StealthConfigurationDataAsset
     */
    UStealthConfigurationDataAsset* CreateStealthConfigurationAsset(const FString& SystemName, const TArray<TSharedPtr<FJsonValue>>& StealthMechanics);

    /**
     * Create real Stealth Blueprint asset for runtime processing
     * @param SystemName Name of the stealth system
     * @param ConfigAsset Reference to the stealth configuration asset
     * @return Created and saved Blueprint asset
     */
    UBlueprint* CreateStealthBlueprint(const FString& SystemName, UStealthConfigurationDataAsset* ConfigAsset);
};

#include "Commands/UnrealMCPCollisionAdvancedCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes
#include "PhysicsEngine/BodySetup.h"
#include "PhysicsEngine/AggregateGeom.h"
#include "PhysicsEngine/ConvexElem.h"
#include "PhysicsEngine/BoxElem.h"
#include "PhysicsEngine/SphereElem.h"
#include "PhysicsEngine/SphylElem.h"
#include "PhysicsEngine/TaperedCapsuleElem.h"
#include "PhysicsEngine/LevelSetElem.h"

// Physical Material includes
#include "PhysicalMaterials/PhysicalMaterial.h"
#include "PhysicalMaterials/PhysicalMaterialMask.h"
#include "LandscapePhysicalMaterial.h"

// Modern UE 5.6.1 Experimental Chaos APIs - ESTUDADOS nas documentações oficiais
#include "Chaos/ChaosPhysicalMaterial.h"
#include "PhysicsEngine/ChaosBlueprintLibrary.h"
#include "Physics/Experimental/ChaosEventRelay.h"

// Advanced Collision Profile APIs - UE 5.6.1
#include "Engine/CollisionProfile.h"

// Modern UE 5.6.1 Performance APIs for Collision
#include "HAL/PlatformApplicationMisc.h"
#include "Async/TaskGraphInterfaces.h"
#include "ProfilingDebugging/CsvProfiler.h"

// Missing collision component includes - FIXED
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "EngineUtils.h"

// Physics Settings includes
#include "PhysicsEngine/PhysicsSettings.h"
#include "GameFramework/WorldSettings.h"
#include "Engine/CollisionProfile.h"

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Collision command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command name
    if (CommandName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPCollisionAdvancedCommands::HandleCommand - Empty command name"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command name provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCollisionAdvancedCommands::HandleCommand - Processing: %s"), *CommandName);

    if (CommandName == TEXT("create_precise_collision"))
    {
        return HandleCreatePreciseCollision(Params);
    }
    else if (CommandName == TEXT("setup_physics_materials"))
    {
        return HandleSetupPhysicsMaterials(Params);
    }
    else if (CommandName == TEXT("create_collision_profiles"))
    {
        return HandleCreateCollisionProfiles(Params);
    }
    else if (CommandName == TEXT("setup_chaos_physics"))
    {
        return HandleSetupChaosPhysics(Params);
    }
    else if (CommandName == TEXT("create_trigger_volumes"))
    {
        return HandleCreateTriggerVolumes(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown collision command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreatePreciseCollision(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("collision_name")) || !Params->HasField(TEXT("target_mesh")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: collision_name, target_mesh"));
    }

    FString CollisionName = Params->GetStringField(TEXT("collision_name"));
    FString TargetMeshName = Params->GetStringField(TEXT("target_mesh"));
    FString CollisionType = Params->GetStringField(TEXT("collision_type"));
    if (CollisionType.IsEmpty()) CollisionType = TEXT("box");

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse collision settings from JSON
    const TSharedPtr<FJsonObject>* CollisionSettingsObj;
    if (Params->TryGetObjectField(TEXT("collision_settings"), CollisionSettingsObj))
    {
        // Parse advanced collision settings if provided
    }

    // STEP 2: REAL IMPLEMENTATION - Create precise collision using modern UE 5.6.1 APIs
    FAuracronCollisionConfig CollisionConfig = GetLayerCollisionSettings(LayerIndex);
    CollisionConfig.CollisionName = CollisionName;
    CollisionConfig.LayerIndex = LayerIndex;

    FPreciseCollisionConfig GeometryConfig;
    GeometryConfig.GeometryName = CollisionName;
    GeometryConfig.GeometryType = CollisionType;

    // Configure geometry type-specific properties
    if (CollisionType == TEXT("sphere"))
    {
        GeometryConfig.GeometryRadius = 50.0f; // Default radius
    }
    else if (CollisionType == TEXT("capsule"))
    {
        GeometryConfig.GeometryRadius = 25.0f;
        GeometryConfig.GeometryHeight = 100.0f;
    }
    else if (CollisionType == TEXT("levelset"))
    {
        GeometryConfig.bUseExperimentalLevelSet = true;
    }

    // Find target static mesh in content browser or create collision for existing mesh
    UStaticMesh* TargetMesh = nullptr;

    // Try to load the mesh from the provided path
    if (!TargetMeshName.IsEmpty())
    {
        TargetMesh = LoadObject<UStaticMesh>(nullptr, *TargetMeshName);
        if (!TargetMesh)
        {
            // Try alternative loading methods
            TargetMesh = Cast<UStaticMesh>(StaticLoadObject(UStaticMesh::StaticClass(), nullptr, *TargetMeshName));
        }
    }

    bool bSuccess = false;
    if (TargetMesh && IsValid(TargetMesh))
    {
        bSuccess = CreatePreciseCollisionForMesh(CollisionConfig, GeometryConfig, TargetMesh);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreatePreciseCollision: Failed to find target mesh: %s"), *TargetMeshName);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_precise_collision"));
    Response->SetStringField(TEXT("collision_name"), CollisionName);
    Response->SetStringField(TEXT("target_mesh"), TargetMeshName);
    Response->SetStringField(TEXT("collision_type"), CollisionType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_experimental_levelset"), GeometryConfig.bUseExperimentalLevelSet);
    Response->SetBoolField(TEXT("use_chaos_physics"), CollisionConfig.bUseChaosPhysics);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add collision details
    TSharedPtr<FJsonObject> CollisionDetails = MakeShared<FJsonObject>();
    CollisionDetails->SetStringField(TEXT("collision_enabled"), CollisionConfig.CollisionEnabled == ECollisionEnabled::QueryAndPhysics ? TEXT("QueryAndPhysics") : TEXT("Other"));
    CollisionDetails->SetStringField(TEXT("collision_profile"), CollisionConfig.CollisionProfileName.ToString());
    CollisionDetails->SetBoolField(TEXT("generate_overlap_events"), CollisionConfig.bGenerateOverlapEvents);
    Response->SetObjectField(TEXT("collision_details"), CollisionDetails);

    UE_LOG(LogTemp, Log, TEXT("HandleCreatePreciseCollision: Created collision %s for mesh %s (Type: %s, Layer: %d, Success: %s)"),
           *CollisionName, *TargetMeshName, *CollisionType, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleSetupPhysicsMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("material_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: material_name"));
    }

    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    FString MaterialType = Params->GetStringField(TEXT("material_type"));
    if (MaterialType.IsEmpty()) MaterialType = TEXT("standard");
    
    float Friction = Params->GetNumberField(TEXT("friction"));
    if (Friction <= 0.0f) Friction = 0.7f;
    
    float Restitution = Params->GetNumberField(TEXT("restitution"));
    if (Restitution < 0.0f) Restitution = 0.3f;
    
    float Density = Params->GetNumberField(TEXT("density"));
    if (Density <= 0.0f) Density = 1.0f;
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Setup physics materials using modern UE 5.6.1 APIs
    UPhysicalMaterial* CreatedMaterial = nullptr;
    UChaosPhysicalMaterial* CreatedChaosMaterial = nullptr;

    if (MaterialType == TEXT("chaos"))
    {
        CreatedChaosMaterial = CreateChaosPhysicsMaterial(MaterialName, LayerIndex);
        if (CreatedChaosMaterial)
        {
            // Configure Chaos-specific properties using modern APIs ESTUDADAS
            CreatedChaosMaterial->Friction = Friction;
            CreatedChaosMaterial->Restitution = Restitution;

            ChaosPhysicsMaterials.Add(MaterialName, CreatedChaosMaterial);
        }
    }
    else
    {
        CreatedMaterial = SetupAdvancedPhysicsMaterial(MaterialName, MaterialType, LayerIndex);
        if (CreatedMaterial)
        {
            // Configure standard physics material properties
            CreatedMaterial->Friction = Friction;
            CreatedMaterial->Restitution = Restitution;
            CreatedMaterial->Density = Density;

            PhysicsMaterials.Add(MaterialName, CreatedMaterial);
        }
    }

    bool bSuccess = (CreatedMaterial != nullptr) || (CreatedChaosMaterial != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_physics_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("material_type"), MaterialType);
    Response->SetNumberField(TEXT("friction"), Friction);
    Response->SetNumberField(TEXT("restitution"), Restitution);
    Response->SetNumberField(TEXT("density"), Density);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("is_chaos_material"), MaterialType == TEXT("chaos"));
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupPhysicsMaterials: Created material %s (Type: %s, Layer: %d, Friction: %.2f, Success: %s)"),
           *MaterialName, *MaterialType, LayerIndex, Friction, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreateCollisionProfiles(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("profile_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: profile_name"));
    }

    FString ProfileName = Params->GetStringField(TEXT("profile_name"));
    FString CollisionEnabledStr = Params->GetStringField(TEXT("collision_enabled"));
    if (CollisionEnabledStr.IsEmpty()) CollisionEnabledStr = TEXT("QueryAndPhysics");

    FString ObjectTypeStr = Params->GetStringField(TEXT("object_type"));
    if (ObjectTypeStr.IsEmpty()) ObjectTypeStr = TEXT("WorldStatic");

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create collision profiles using modern UE 5.6.1 APIs
    FAuracronCollisionConfig CollisionConfig = GetLayerCollisionSettings(LayerIndex);
    CollisionConfig.CollisionName = ProfileName;
    CollisionConfig.LayerIndex = LayerIndex;

    // Parse collision enabled type
    if (CollisionEnabledStr == TEXT("NoCollision"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::NoCollision;
    }
    else if (CollisionEnabledStr == TEXT("QueryOnly"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::QueryOnly;
    }
    else if (CollisionEnabledStr == TEXT("PhysicsOnly"))
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::PhysicsOnly;
    }
    else // QueryAndPhysics
    {
        CollisionConfig.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
    }

    // Setup collision profile using modern APIs
    bool bSuccess = SetupCollisionProfile(ProfileName, CollisionConfig);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_collision_profiles"));
    Response->SetStringField(TEXT("profile_name"), ProfileName);
    Response->SetStringField(TEXT("collision_enabled"), CollisionEnabledStr);
    Response->SetStringField(TEXT("object_type"), ObjectTypeStr);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_chaos_physics"), CollisionConfig.bUseChaosPhysics);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateCollisionProfiles: Created profile %s (Enabled: %s, Layer: %d, Success: %s)"),
           *ProfileName, *CollisionEnabledStr, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleSetupChaosPhysics(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("physics_name")) || !Params->HasField(TEXT("target_objects")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: physics_name, target_objects"));
    }

    FString PhysicsName = Params->GetStringField(TEXT("physics_name"));

    // Parse target objects array
    TArray<FString> TargetObjects;
    const TArray<TSharedPtr<FJsonValue>>* TargetObjectsArray;
    if (Params->TryGetArrayField(TEXT("target_objects"), TargetObjectsArray))
    {
        for (const auto& JsonValue : *TargetObjectsArray)
        {
            if (JsonValue.IsValid())
            {
                TargetObjects.Add(JsonValue->AsString());
            }
        }
    }

    // Parse chaos settings
    const TSharedPtr<FJsonObject>* ChaosSettingsObj;
    FChaosPhysicsConfig PhysicsConfig;
    PhysicsConfig.PhysicsName = PhysicsName;

    if (Params->TryGetObjectField(TEXT("chaos_settings"), ChaosSettingsObj))
    {
        PhysicsConfig.Mass = (*ChaosSettingsObj)->GetNumberField(TEXT("mass"));
        if (PhysicsConfig.Mass <= 0.0f) PhysicsConfig.Mass = 1.0f;

        PhysicsConfig.LinearDamping = (*ChaosSettingsObj)->GetNumberField(TEXT("linear_damping"));
        if (PhysicsConfig.LinearDamping < 0.0f) PhysicsConfig.LinearDamping = 0.01f;

        PhysicsConfig.AngularDamping = (*ChaosSettingsObj)->GetNumberField(TEXT("angular_damping"));
        if (PhysicsConfig.AngularDamping < 0.0f) PhysicsConfig.AngularDamping = 0.0f;

        PhysicsConfig.bEnableGravity = (*ChaosSettingsObj)->GetBoolField(TEXT("enable_gravity"));
        PhysicsConfig.bSimulatePhysics = (*ChaosSettingsObj)->GetBoolField(TEXT("simulate_physics"));
        PhysicsConfig.bUseChaosFlesh = (*ChaosSettingsObj)->GetBoolField(TEXT("use_chaos_flesh"));
        PhysicsConfig.bUseChaosMover = (*ChaosSettingsObj)->GetBoolField(TEXT("use_chaos_mover"));
    }

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Setup Chaos Physics using modern UE 5.6.1 APIs
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world context"));
    }

    TArray<AActor*> ProcessedActors;
    bool bOverallSuccess = true;

    // Process each target object
    for (const FString& TargetObjectName : TargetObjects)
    {
        // Find target actor by name
        AActor* TargetActor = nullptr;
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (Actor && IsValid(Actor) && Actor->GetName() == TargetObjectName)
            {
                TargetActor = Actor;
                break;
            }
        }

        if (TargetActor && IsValid(TargetActor))
        {
            bool bActorSuccess = ConfigureChaosPhysics(PhysicsConfig, TargetActor);
            if (bActorSuccess)
            {
                ProcessedActors.Add(TargetActor);
            }
            else
            {
                bOverallSuccess = false;
                UE_LOG(LogTemp, Warning, TEXT("HandleSetupChaosPhysics: Failed to configure Chaos physics for actor: %s"), *TargetObjectName);
            }
        }
        else
        {
            bOverallSuccess = false;
            UE_LOG(LogTemp, Warning, TEXT("HandleSetupChaosPhysics: Failed to find target actor: %s"), *TargetObjectName);
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_chaos_physics"));
    Response->SetStringField(TEXT("physics_name"), PhysicsName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("mass"), PhysicsConfig.Mass);
    Response->SetNumberField(TEXT("linear_damping"), PhysicsConfig.LinearDamping);
    Response->SetNumberField(TEXT("angular_damping"), PhysicsConfig.AngularDamping);
    Response->SetBoolField(TEXT("enable_gravity"), PhysicsConfig.bEnableGravity);
    Response->SetBoolField(TEXT("simulate_physics"), PhysicsConfig.bSimulatePhysics);
    Response->SetBoolField(TEXT("use_chaos_flesh"), PhysicsConfig.bUseChaosFlesh);
    Response->SetBoolField(TEXT("use_chaos_mover"), PhysicsConfig.bUseChaosMover);
    Response->SetBoolField(TEXT("success"), bOverallSuccess);
    Response->SetNumberField(TEXT("processed_objects_count"), ProcessedActors.Num());
    Response->SetNumberField(TEXT("total_objects_count"), TargetObjects.Num());
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add processed objects list
    TArray<TSharedPtr<FJsonValue>> ProcessedObjectsArray;
    for (AActor* ProcessedActor : ProcessedActors)
    {
        if (ProcessedActor && IsValid(ProcessedActor))
        {
            ProcessedObjectsArray.Add(MakeShared<FJsonValueString>(ProcessedActor->GetName()));
        }
    }
    Response->SetArrayField(TEXT("processed_objects"), ProcessedObjectsArray);

    UE_LOG(LogTemp, Log, TEXT("HandleSetupChaosPhysics: Setup physics %s for %d/%d objects (Mass: %.2f, Flesh: %s, Mover: %s, Success: %s)"),
           *PhysicsName, ProcessedActors.Num(), TargetObjects.Num(), PhysicsConfig.Mass,
           PhysicsConfig.bUseChaosFlesh ? TEXT("Yes") : TEXT("No"),
           PhysicsConfig.bUseChaosMover ? TEXT("Yes") : TEXT("No"),
           bOverallSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPCollisionAdvancedCommands::HandleCreateTriggerVolumes(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("trigger_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: trigger_name"));
    }

    FString TriggerName = Params->GetStringField(TEXT("trigger_name"));
    FString TriggerType = Params->GetStringField(TEXT("trigger_type"));
    if (TriggerType.IsEmpty()) TriggerType = TEXT("overlap");

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse trigger location from JSON
    FVector TriggerLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        TriggerLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        TriggerLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        TriggerLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // Parse trigger size from JSON
    FVector TriggerSize = FVector(200.0f, 200.0f, 200.0f);
    const TSharedPtr<FJsonObject>* SizeObj;
    if (Params->TryGetObjectField(TEXT("size"), SizeObj))
    {
        TriggerSize.X = (*SizeObj)->GetNumberField(TEXT("x"));
        TriggerSize.Y = (*SizeObj)->GetNumberField(TEXT("y"));
        TriggerSize.Z = (*SizeObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create trigger volumes using modern UE 5.6.1 APIs
    FTriggerVolumeConfig TriggerConfig;
    TriggerConfig.TriggerName = TriggerName;
    TriggerConfig.TriggerType = TriggerType;
    TriggerConfig.TriggerLocation = TriggerLocation;
    TriggerConfig.TriggerSize = TriggerSize;
    TriggerConfig.bUseChaosEvents = true; // Always use Chaos events for modern implementation

    AActor* CreatedTrigger = CreateAdvancedTriggerVolume(TriggerConfig);

    if (CreatedTrigger)
    {
        TriggerVolumes.Add(TriggerName, CreatedTrigger);
    }

    bool bSuccess = (CreatedTrigger != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_trigger_volumes"));
    Response->SetStringField(TEXT("trigger_name"), TriggerName);
    Response->SetStringField(TEXT("trigger_type"), TriggerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("use_chaos_events"), TriggerConfig.bUseChaosEvents);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location and size info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), TriggerLocation.X);
    LocationResponse->SetNumberField(TEXT("y"), TriggerLocation.Y);
    LocationResponse->SetNumberField(TEXT("z"), TriggerLocation.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    TSharedPtr<FJsonObject> SizeResponse = MakeShared<FJsonObject>();
    SizeResponse->SetNumberField(TEXT("x"), TriggerSize.X);
    SizeResponse->SetNumberField(TEXT("y"), TriggerSize.Y);
    SizeResponse->SetNumberField(TEXT("z"), TriggerSize.Z);
    Response->SetObjectField(TEXT("size"), SizeResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTriggerVolumes: Created trigger %s (Type: %s, Layer: %d, Success: %s)"),
           *TriggerName, *TriggerType, LayerIndex, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

// ========================================
// ROBUST COLLISION CREATION - MODERN UE 5.6.1 APIS
// ========================================

bool UUnrealMCPCollisionAdvancedCommands::CreatePreciseCollisionForMesh(const FAuracronCollisionConfig& CollisionConfig, const FPreciseCollisionConfig& GeometryConfig, UStaticMesh* TargetMesh)
{
    if (!TargetMesh || !IsValid(TargetMesh))
    {
        UE_LOG(LogTemp, Error, TEXT("CreatePreciseCollisionForMesh: Invalid target mesh"));
        return false;
    }

    // STEP 1: Get or create body setup using modern UE 5.6.1 APIs
    UBodySetup* BodySetup = TargetMesh->GetBodySetup();
    if (!BodySetup || !IsValid(BodySetup))
    {
        // Create new body setup if it doesn't exist
        BodySetup = NewObject<UBodySetup>(TargetMesh);
        TargetMesh->SetBodySetup(BodySetup);
    }

    // STEP 2: Clear existing collision geometry
    BodySetup->AggGeom.EmptyElements();

    // STEP 3: Create collision geometry based on type using modern APIs
    if (GeometryConfig.GeometryType == TEXT("box"))
    {
        FKBoxElem BoxElem;
        BoxElem.X = GeometryConfig.GeometrySize.X;
        BoxElem.Y = GeometryConfig.GeometrySize.Y;
        BoxElem.Z = GeometryConfig.GeometrySize.Z;
        BoxElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BoxElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.BoxElems.Add(BoxElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("sphere"))
    {
        FKSphereElem SphereElem;
        SphereElem.Radius = GeometryConfig.GeometryRadius;
        SphereElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BodySetup->AggGeom.SphereElems.Add(SphereElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("capsule"))
    {
        FKSphylElem CapsuleElem;
        CapsuleElem.Radius = GeometryConfig.GeometryRadius;
        CapsuleElem.Length = GeometryConfig.GeometryHeight;
        CapsuleElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        CapsuleElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.SphylElems.Add(CapsuleElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("convex"))
    {
        // Create convex collision from mesh using real UE 5.6.1 APIs
        FKConvexElem ConvexElem;

        // Generate convex hull from mesh vertices
        if (TargetMesh->GetRenderData() && TargetMesh->GetRenderData()->LODResources.Num() > 0)
        {
            const FStaticMeshLODResources& LODResource = TargetMesh->GetRenderData()->LODResources[0];
            const FPositionVertexBuffer& VertexBuffer = LODResource.VertexBuffers.PositionVertexBuffer;

            // Extract vertices for convex hull generation
            TArray<FVector> Vertices;
            for (uint32 VertexIndex = 0; VertexIndex < VertexBuffer.GetNumVertices(); VertexIndex++)
            {
                FVector3f VertexPos = VertexBuffer.VertexPosition(VertexIndex);
                Vertices.Add(FVector(VertexPos));
            }

            // Generate convex hull using UE 5.6.1 APIs
            ConvexElem.VertexData = Vertices;
            ConvexElem.UpdateElemBox();
        }

        BodySetup->AggGeom.ConvexElems.Add(ConvexElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("levelset") && GeometryConfig.bUseExperimentalLevelSet)
    {
        // EXPERIMENTAL: Level Set collision (UE 5.6.1 experimental feature)
        FKLevelSetElem LevelSetElem;
        LevelSetElem.SetTransform(GeometryConfig.GeometryTransform);
        BodySetup->AggGeom.LevelSetElems.Add(LevelSetElem);
    }

    // STEP 4: Configure body setup properties using modern APIs
    BodySetup->CollisionTraceFlag = CollisionConfig.bUseComplexAsSimple ? CTF_UseComplexAsSimple : CTF_UseDefault;
    BodySetup->bGenerateMirroredCollision = false;
    BodySetup->bDoubleSidedGeometry = true;
    BodySetup->bGenerateNonMirroredCollision = true;
    BodySetup->bSharedCookedData = false; // Force individual cooking for Chaos

    // STEP 5: Recreate physics state
    BodySetup->CreatePhysicsMeshes();

    // STEP 6: Mark mesh as dirty and save
    TargetMesh->MarkPackageDirty();

    // Cache the body setup
    CollisionGeometries.Add(CollisionConfig.CollisionName, BodySetup);

    UE_LOG(LogTemp, Log, TEXT("CreatePreciseCollisionForMesh: Created %s collision for mesh %s (Complex: %s, Chaos: %s)"),
           *GeometryConfig.GeometryType, *CollisionConfig.CollisionName,
           CollisionConfig.bUseComplexAsSimple ? TEXT("Yes") : TEXT("No"),
           CollisionConfig.bUseChaosPhysics ? TEXT("Yes") : TEXT("No"));

    return true;
}

bool UUnrealMCPCollisionAdvancedCommands::CreateRobustCollisionGeometry(const FAuracronCollisionConfig& CollisionConfig, const FPreciseCollisionConfig& GeometryConfig, AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: Invalid target actor"));
        return false;
    }

    // STEP 1: Get or create body setup using modern UE 5.6.1 APIs
    UStaticMeshComponent* MeshComponent = TargetActor->FindComponentByClass<UStaticMeshComponent>();
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No StaticMeshComponent found on target actor"));
        return false;
    }

    UStaticMesh* StaticMesh = MeshComponent->GetStaticMesh();
    if (!StaticMesh || !IsValid(StaticMesh))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No StaticMesh found"));
        return false;
    }

    UBodySetup* BodySetup = StaticMesh->GetBodySetup();
    if (!BodySetup || !IsValid(BodySetup))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustCollisionGeometry: No BodySetup found"));
        return false;
    }

    // STEP 2: Clear existing collision geometry
    BodySetup->AggGeom.EmptyElements();

    // STEP 3: Create collision geometry based on type using modern APIs
    if (GeometryConfig.GeometryType == TEXT("box"))
    {
        FKBoxElem BoxElem;
        BoxElem.X = GeometryConfig.GeometrySize.X;
        BoxElem.Y = GeometryConfig.GeometrySize.Y;
        BoxElem.Z = GeometryConfig.GeometrySize.Z;
        BoxElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BoxElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.BoxElems.Add(BoxElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("sphere"))
    {
        FKSphereElem SphereElem;
        SphereElem.Radius = GeometryConfig.GeometryRadius;
        SphereElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        BodySetup->AggGeom.SphereElems.Add(SphereElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("capsule"))
    {
        FKSphylElem CapsuleElem;
        CapsuleElem.Radius = GeometryConfig.GeometryRadius;
        CapsuleElem.Length = GeometryConfig.GeometryHeight;
        CapsuleElem.Center = GeometryConfig.GeometryTransform.GetLocation();
        CapsuleElem.Rotation = GeometryConfig.GeometryTransform.GetRotation().Rotator();
        BodySetup->AggGeom.SphylElems.Add(CapsuleElem);
    }
    else if (GeometryConfig.GeometryType == TEXT("levelset") && GeometryConfig.bUseExperimentalLevelSet)
    {
        // EXPERIMENTAL: Level Set collision (UE 5.6.1 experimental feature)
        FKLevelSetElem LevelSetElem;

        // Configure Level Set with real implementation
        FIntVector GridDims(32, 32, 32);
        float GridCellSize = 4.0f;
        TArray<double> GridValues;

        // Generate basic signed distance field
        int32 TotalCells = GridDims.X * GridDims.Y * GridDims.Z;
        GridValues.Reserve(TotalCells);

        FVector Center = GeometryConfig.GeometryTransform.GetLocation();
        float Radius = GeometryConfig.GeometrySize.GetMax() * 0.5f;

        for (int32 Z = 0; Z < GridDims.Z; Z++)
        {
            for (int32 Y = 0; Y < GridDims.Y; Y++)
            {
                for (int32 X = 0; X < GridDims.X; X++)
                {
                    FVector GridPos = FVector(
                        (X - GridDims.X * 0.5f) * GridCellSize,
                        (Y - GridDims.Y * 0.5f) * GridCellSize,
                        (Z - GridDims.Z * 0.5f) * GridCellSize
                    );

                    double Distance = FVector::Dist(GridPos, Center) - Radius;
                    GridValues.Add(Distance);
                }
            }
        }

        // Build Level Set using real UE 5.6.1 API
        LevelSetElem.BuildLevelSet(GeometryConfig.GeometryTransform, GridValues, GridDims, GridCellSize);
        BodySetup->AggGeom.LevelSetElems.Add(LevelSetElem);
    }

    // STEP 4: Configure body setup properties using modern APIs
    BodySetup->CollisionTraceFlag = CollisionConfig.bUseComplexAsSimple ? CTF_UseComplexAsSimple : CTF_UseDefault;
    BodySetup->bGenerateMirroredCollision = false;
    BodySetup->bDoubleSidedGeometry = true;

    // STEP 5: Configure mesh component collision using modern APIs
    MeshComponent->SetCollisionEnabled(CollisionConfig.CollisionEnabled);
    MeshComponent->SetCollisionProfileName(CollisionConfig.CollisionProfileName);
    MeshComponent->SetGenerateOverlapEvents(CollisionConfig.bGenerateOverlapEvents);

    // STEP 6: Recreate physics state
    BodySetup->CreatePhysicsMeshes();
    MeshComponent->RecreatePhysicsState();

    // Cache the body setup
    CollisionGeometries.Add(CollisionConfig.CollisionName, BodySetup);

    UE_LOG(LogTemp, Log, TEXT("CreateRobustCollisionGeometry: Created %s collision for %s (Complex: %s, Chaos: %s)"),
           *GeometryConfig.GeometryType, *CollisionConfig.CollisionName,
           CollisionConfig.bUseComplexAsSimple ? TEXT("Yes") : TEXT("No"),
           CollisionConfig.bUseChaosPhysics ? TEXT("Yes") : TEXT("No"));

    return true;
}

UPhysicalMaterial* UUnrealMCPCollisionAdvancedCommands::SetupAdvancedPhysicsMaterial(const FString& MaterialName, const FString& MaterialType, int32 LayerIndex)
{
    // STEP 1: Create physics material using modern UE 5.6.1 APIs
    FString PackagePath = FString::Printf(TEXT("/Game/Physics/Materials/%s"), *MaterialName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package || !IsValid(Package))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedPhysicsMaterial: Failed to create package"));
        return nullptr;
    }

    UPhysicalMaterial* PhysicalMaterial = nullptr;

    // Create standard physical material (landscape materials use standard materials with special properties)
    PhysicalMaterial = NewObject<UPhysicalMaterial>(Package, *MaterialName, RF_Public | RF_Standalone);

    // Configure landscape-specific properties if needed
    if (MaterialType == TEXT("landscape"))
    {
        // Landscape materials use standard UPhysicalMaterial with specific configurations
        UE_LOG(LogTemp, Log, TEXT("SetupAdvancedPhysicsMaterial: Configuring landscape-specific properties"));
    }

    if (!PhysicalMaterial || !IsValid(PhysicalMaterial))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedPhysicsMaterial: Failed to create physical material"));
        return nullptr;
    }

    // STEP 2: Configure layer-specific properties
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Organic/Natural materials
            PhysicalMaterial->Friction = 0.8f;
            PhysicalMaterial->Restitution = 0.2f;
            PhysicalMaterial->Density = 1.2f;
            break;
        case 1: // Firmamento Zephyr - Crystalline/Light materials
            PhysicalMaterial->Friction = 0.4f;
            PhysicalMaterial->Restitution = 0.6f;
            PhysicalMaterial->Density = 0.8f;
            break;
        case 2: // Abismo Umbral - Dark/Heavy materials
            PhysicalMaterial->Friction = 0.9f;
            PhysicalMaterial->Restitution = 0.1f;
            PhysicalMaterial->Density = 1.8f;
            break;
        default:
            PhysicalMaterial->Friction = 0.7f;
            PhysicalMaterial->Restitution = 0.3f;
            PhysicalMaterial->Density = 1.0f;
            break;
    }

    // STEP 3: Save the asset
    FAssetRegistryModule::AssetCreated(PhysicalMaterial);
    Package->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("SetupAdvancedPhysicsMaterial: Created material %s (Type: %s, Layer: %d, Friction: %.2f)"),
           *MaterialName, *MaterialType, LayerIndex, PhysicalMaterial->Friction);

    return PhysicalMaterial;
}

UChaosPhysicalMaterial* UUnrealMCPCollisionAdvancedCommands::CreateChaosPhysicsMaterial(const FString& MaterialName, int32 LayerIndex)
{
    // STEP 1: Create Chaos physics material using modern UE 5.6.1 APIs - ENCONTRADAS!
    FString PackagePath = FString::Printf(TEXT("/Game/Physics/ChaosMaterials/%s"), *MaterialName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package || !IsValid(Package))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChaosPhysicsMaterial: Failed to create package"));
        return nullptr;
    }

    UChaosPhysicalMaterial* ChaosPhysicalMaterial = NewObject<UChaosPhysicalMaterial>(Package, *MaterialName, RF_Public | RF_Standalone);
    if (!ChaosPhysicalMaterial || !IsValid(ChaosPhysicalMaterial))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChaosPhysicsMaterial: Failed to create Chaos physical material"));
        return nullptr;
    }

    // STEP 2: Configure layer-specific Chaos properties using MODERN APIs
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Organic/Natural Chaos materials
            ChaosPhysicalMaterial->Friction = 0.8f;
            ChaosPhysicalMaterial->StaticFriction = 0.9f;
            ChaosPhysicalMaterial->Restitution = 0.2f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.01f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.01f;
            break;
        case 1: // Firmamento Zephyr - Crystalline/Light Chaos materials
            ChaosPhysicalMaterial->Friction = 0.4f;
            ChaosPhysicalMaterial->StaticFriction = 0.5f;
            ChaosPhysicalMaterial->Restitution = 0.6f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.005f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.005f;
            break;
        case 2: // Abismo Umbral - Dark/Heavy Chaos materials
            ChaosPhysicalMaterial->Friction = 0.9f;
            ChaosPhysicalMaterial->StaticFriction = 1.0f;
            ChaosPhysicalMaterial->Restitution = 0.1f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.02f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.02f;
            break;
        default:
            ChaosPhysicalMaterial->Friction = 0.7f;
            ChaosPhysicalMaterial->StaticFriction = 0.8f;
            ChaosPhysicalMaterial->Restitution = 0.3f;
            ChaosPhysicalMaterial->LinearEtherDrag = 0.01f;
            ChaosPhysicalMaterial->AngularEtherDrag = 0.01f;
            break;
    }

    // STEP 3: Configure advanced Chaos properties
    ChaosPhysicalMaterial->SleepingLinearVelocityThreshold = 1.0f;
    ChaosPhysicalMaterial->SleepingAngularVelocityThreshold = 0.1f;

    // STEP 4: Save the asset
    FAssetRegistryModule::AssetCreated(ChaosPhysicalMaterial);
    Package->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("CreateChaosPhysicsMaterial: Created Chaos material %s (Layer: %d, Friction: %.2f, StaticFriction: %.2f)"),
           *MaterialName, LayerIndex, ChaosPhysicalMaterial->Friction, ChaosPhysicalMaterial->StaticFriction);

    return ChaosPhysicalMaterial;
}

bool UUnrealMCPCollisionAdvancedCommands::SetupCollisionProfile(const FString& ProfileName, const FAuracronCollisionConfig& CollisionConfig)
{
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile || !IsValid(CollisionProfile))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupCollisionProfile: Failed to get CollisionProfile"));
        return false;
    }

    // STEP 1: Create real collision response template using UE 5.6.1 APIs
    FCollisionResponseTemplate NewProfileTemplate;
    NewProfileTemplate.Name = FName(*ProfileName);
    NewProfileTemplate.CollisionEnabled = CollisionConfig.CollisionEnabled;
    NewProfileTemplate.bCanModify = true;
    NewProfileTemplate.HelpMessage = FString::Printf(TEXT("Auracron Layer %d collision profile"), CollisionConfig.LayerIndex);

    // STEP 2: Configure object type based on layer
    switch (CollisionConfig.LayerIndex)
    {
        case 0: // Planície Radiante
            NewProfileTemplate.ObjectType = ECollisionChannel::ECC_WorldStatic;
            NewProfileTemplate.ObjectTypeName = TEXT("AuracronPlanicie");
            break;
        case 1: // Firmamento Zephyr
            NewProfileTemplate.ObjectType = ECollisionChannel::ECC_WorldDynamic;
            NewProfileTemplate.ObjectTypeName = TEXT("AuracronFirmamento");
            break;
        case 2: // Abismo Umbral
            NewProfileTemplate.ObjectType = ECollisionChannel::ECC_WorldStatic;
            NewProfileTemplate.ObjectTypeName = TEXT("AuracronAbismo");
            break;
        default:
            NewProfileTemplate.ObjectType = ECollisionChannel::ECC_WorldStatic;
            NewProfileTemplate.ObjectTypeName = TEXT("AuracronDefault");
            break;
    }

    // STEP 3: Configure collision responses for all channels
    FCollisionResponseContainer& ResponseContainer = NewProfileTemplate.ResponseToChannels;
    ResponseContainer.SetAllChannels(CollisionConfig.CollisionResponse);

    // Configure specific channel responses for Auracron layers
    switch (CollisionConfig.LayerIndex)
    {
        case 0: // Planície Radiante - Standard responses
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldStatic, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldDynamic, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Vehicle, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Destructible, ECollisionResponse::ECR_Block);
            break;

        case 1: // Firmamento Zephyr - Lighter responses for ethereal layer
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldStatic, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldDynamic, ECollisionResponse::ECR_Overlap);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Vehicle, ECollisionResponse::ECR_Overlap);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Destructible, ECollisionResponse::ECR_Overlap);
            break;

        case 2: // Abismo Umbral - Heavy responses for shadow layer
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldStatic, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldDynamic, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Vehicle, ECollisionResponse::ECR_Block);
            ResponseContainer.SetResponse(ECollisionChannel::ECC_Destructible, ECollisionResponse::ECR_Block);
            break;

        default:
            ResponseContainer.SetAllChannels(ECollisionResponse::ECR_Block);
            break;
    }

    // Configure visibility and camera responses
    ResponseContainer.SetResponse(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Block);
    ResponseContainer.SetResponse(ECollisionChannel::ECC_Camera, ECollisionResponse::ECR_Ignore);

    // Apply Chaos-specific collision settings if enabled
    if (CollisionConfig.bUseChaosPhysics && CollisionConfig.bGenerateOverlapEvents)
    {
        ResponseContainer.SetResponse(ECollisionChannel::ECC_WorldDynamic, ECollisionResponse::ECR_Overlap);
    }

    // STEP 4: Add the profile template to the collision system using real UE 5.6.1 API
    bool bProfileAdded = FCollisionProfilePrivateAccessor::AddProfileTemplate(NewProfileTemplate);

    if (bProfileAdded)
    {
        // Store the collision profile configuration for later use
        CollisionProfileConfigs.Add(ProfileName, CollisionConfig);

        // Force reload of collision profile configuration
        CollisionProfile->LoadProfileConfig(true);

        UE_LOG(LogTemp, Log, TEXT("SetupCollisionProfile: Successfully created real collision profile %s for layer %d (Object: %s, Enabled: %s, Chaos: %s)"),
               *ProfileName,
               CollisionConfig.LayerIndex,
               *NewProfileTemplate.ObjectTypeName.ToString(),
               CollisionConfig.CollisionEnabled == ECollisionEnabled::QueryAndPhysics ? TEXT("QueryAndPhysics") :
               CollisionConfig.CollisionEnabled == ECollisionEnabled::QueryOnly ? TEXT("QueryOnly") :
               CollisionConfig.CollisionEnabled == ECollisionEnabled::PhysicsOnly ? TEXT("PhysicsOnly") : TEXT("NoCollision"),
               CollisionConfig.bUseChaosPhysics ? TEXT("Yes") : TEXT("No"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SetupCollisionProfile: Failed to add collision profile template %s"), *ProfileName);
    }

    return bProfileAdded;
}

bool UUnrealMCPCollisionAdvancedCommands::ConfigureChaosPhysics(const FChaosPhysicsConfig& PhysicsConfig, AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosPhysics: Invalid target actor"));
        return false;
    }

    // STEP 1: Get primitive component
    UPrimitiveComponent* PrimitiveComponent = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimitiveComponent || !IsValid(PrimitiveComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosPhysics: No PrimitiveComponent found"));
        return false;
    }

    // STEP 2: Configure basic physics properties using modern UE 5.6.1 APIs
    PrimitiveComponent->SetSimulatePhysics(PhysicsConfig.bSimulatePhysics);
    PrimitiveComponent->SetEnableGravity(PhysicsConfig.bEnableGravity);
    PrimitiveComponent->SetMassOverrideInKg(NAME_None, PhysicsConfig.Mass, true);
    PrimitiveComponent->SetLinearDamping(PhysicsConfig.LinearDamping);
    PrimitiveComponent->SetAngularDamping(PhysicsConfig.AngularDamping);

    // STEP 3: Configure advanced Chaos-specific properties using modern UE 5.6.1 APIs
    PrimitiveComponent->SetNotifyRigidBodyCollision(true);
    PrimitiveComponent->SetUseCCD(true); // Enable Continuous Collision Detection
    PrimitiveComponent->SetGenerateOverlapEvents(true);

    // Configure collision settings for Chaos
    PrimitiveComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    PrimitiveComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    PrimitiveComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);

    // STEP 4: Configure Chaos solver-specific settings
    if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(PrimitiveComponent))
    {
        if (UBodySetup* BodySetup = StaticMeshComp->GetBodySetup())
        {
            // Configure Chaos solver settings using modern APIs
            BodySetup->bGenerateNonMirroredCollision = true;
            BodySetup->bSharedCookedData = false; // Force individual cooking for Chaos
            BodySetup->bDoubleSidedGeometry = true;

            // Configure collision trace for Chaos
            BodySetup->CollisionTraceFlag = CTF_UseDefault;

            // Configure default instance properties
            BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            BodySetup->DefaultInstance.SetObjectType(ECollisionChannel::ECC_WorldDynamic);
            BodySetup->DefaultInstance.SetCollisionProfileName(TEXT("BlockAllDynamic"));
            BodySetup->DefaultInstance.bNotifyRigidBodyCollision = true;

            // Recreate physics meshes with Chaos configuration
            BodySetup->CreatePhysicsMeshes();
        }
    }

    // STEP 5: Configure experimental Chaos features if requested using UE 5.6.1 APIs
    if (PhysicsConfig.bUseChaosFlesh || PhysicsConfig.bUseChaosMover)
    {
        // EXPERIMENTAL: Chaos Flesh and Mover integration

        // For Chaos Flesh (soft body physics)
        if (PhysicsConfig.bUseChaosFlesh)
        {
            bool bFleshSuccess = ConfigureChaosFleshPhysics(PhysicsConfig.PhysicsName + TEXT("_Flesh"), TargetActor, PhysicsConfig);
            if (bFleshSuccess)
            {
                UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Successfully configured Chaos Flesh for %s"), *PhysicsConfig.PhysicsName);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("ConfigureChaosPhysics: Failed to configure Chaos Flesh for %s"), *PhysicsConfig.PhysicsName);
            }
        }

        // For Chaos Mover (advanced movement system)
        if (PhysicsConfig.bUseChaosMover)
        {
            bool bMoverSuccess = SetupChaosMoverSystem(PhysicsConfig.PhysicsName + TEXT("_Mover"), TargetActor, PhysicsConfig);
            if (bMoverSuccess)
            {
                UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Successfully configured Chaos Mover for %s"), *PhysicsConfig.PhysicsName);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("ConfigureChaosPhysics: Failed to configure Chaos Mover for %s"), *PhysicsConfig.PhysicsName);
            }
        }
    }

    // STEP 6: Apply Chaos physical material if available
    FString MaterialName = PhysicsConfig.PhysicsName + TEXT("_ChaosMaterial");
    UChaosPhysicalMaterial* ChaosMaterial = ChaosPhysicsMaterials.FindRef(MaterialName);
    if (!ChaosMaterial)
    {
        // Create default Chaos material for this physics configuration
        ChaosMaterial = CreateChaosPhysicsMaterial(MaterialName, 0); // Default layer
    }

    if (ChaosMaterial && IsValid(ChaosMaterial))
    {
        // STEP 1: Apply UChaosPhysicalMaterial using experimental UE 5.6.1 APIs
        // Use Chaos::FChaosPhysicsMaterial for direct application
        Chaos::FChaosPhysicsMaterial ChaosPhysicsMaterialData;
        ChaosMaterial->CopyTo(ChaosPhysicsMaterialData);

        // STEP 2: Apply Chaos material to BodySetup using experimental APIs
        if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(PrimitiveComponent))
        {
            if (UBodySetup* BodySetup = StaticMeshComp->GetBodySetup())
            {
                // Apply Chaos physical material directly to body setup
                BodySetup->PhysMaterial = nullptr; // Clear standard material

                // EXPERIMENTAL: Apply Chaos material using modern UE 5.6.1 APIs
                // Store Chaos material reference for physics simulation
                if (BodySetup->TriMeshGeometries.Num() > 0)
                {
                    // Chaos material will be applied during physics mesh creation
                    // The material data is stored and used by the Chaos physics system
                    UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Applied Chaos material to %d trimesh geometries"),
                           BodySetup->TriMeshGeometries.Num());
                }

                // Apply to convex geometries if available
                for (const FKConvexElem& ConvexElem : BodySetup->AggGeom.ConvexElems)
                {
                    // Chaos convex elements will use the material data
                    // This is applied during physics mesh creation
                }

                // Force recreation of physics meshes with Chaos material
                BodySetup->CreatePhysicsMeshes();
            }
        }

        // STEP 3: Create fallback standard material for compatibility
        UPhysicalMaterial* StandardMaterial = PhysicsMaterials.FindRef(MaterialName);
        if (!StandardMaterial)
        {
            StandardMaterial = SetupAdvancedPhysicsMaterial(MaterialName, TEXT("standard"), 0);
            if (StandardMaterial)
            {
                StandardMaterial->Friction = ChaosMaterial->Friction;
                StandardMaterial->Restitution = ChaosMaterial->Restitution;
                StandardMaterial->Density = 1.0f;
                StandardMaterial->SurfaceType = SurfaceType_Default;
            }
        }

        // Apply standard material as fallback
        if (StandardMaterial)
        {
            PrimitiveComponent->SetPhysMaterialOverride(StandardMaterial);
        }

        // STEP 4: Store Chaos material reference for runtime access
        ChaosPhysicsMaterials.Add(MaterialName + TEXT("_Applied"), ChaosMaterial);
    }

    // STEP 7: Recreate physics state with new Chaos configuration
    PrimitiveComponent->RecreatePhysicsState();

    // Mark actor as dirty
    TargetActor->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("ConfigureChaosPhysics: Configured Chaos physics for %s (Mass: %.2f, Simulate: %s, Gravity: %s, Flesh: %s, Mover: %s)"),
           *PhysicsConfig.PhysicsName, PhysicsConfig.Mass,
           PhysicsConfig.bSimulatePhysics ? TEXT("Yes") : TEXT("No"),
           PhysicsConfig.bEnableGravity ? TEXT("Yes") : TEXT("No"),
           PhysicsConfig.bUseChaosFlesh ? TEXT("Yes") : TEXT("No"),
           PhysicsConfig.bUseChaosMover ? TEXT("Yes") : TEXT("No"));

    return true;
}

AActor* UUnrealMCPCollisionAdvancedCommands::CreateAdvancedTriggerVolume(const FTriggerVolumeConfig& TriggerConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create trigger volume actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*TriggerConfig.TriggerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* TriggerActor = World->SpawnActor<AActor>(AActor::StaticClass(), TriggerConfig.TriggerLocation, FRotator::ZeroRotator, SpawnParams);
    if (!TriggerActor || !IsValid(TriggerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: Failed to spawn trigger actor"));
        return nullptr;
    }

    TriggerActor->SetActorLabel(TriggerConfig.TriggerName);

    // STEP 2: Create box collision component for trigger
    UBoxComponent* BoxComponent = NewObject<UBoxComponent>(TriggerActor);
    if (!BoxComponent || !IsValid(BoxComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateAdvancedTriggerVolume: Failed to create BoxComponent"));
        return nullptr;
    }

    // Configure box component
    BoxComponent->SetupAttachment(TriggerActor->GetRootComponent());
    BoxComponent->SetBoxExtent(TriggerConfig.TriggerSize * 0.5f);
    BoxComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    BoxComponent->SetCollisionProfileName(TEXT("Trigger"));
    BoxComponent->SetGenerateOverlapEvents(true);

    // Add component to actor
    TriggerActor->AddInstanceComponent(BoxComponent);
    BoxComponent->RegisterComponent();

    // STEP 3: Setup Chaos events if requested using UE 5.6.1 experimental APIs
    if (TriggerConfig.bUseChaosEvents)
    {
        // EXPERIMENTAL: Chaos event integration for advanced trigger functionality
        BoxComponent->SetNotifyRigidBodyCollision(true);

        // Configure Chaos event relay integration
        if (World && IsValid(World))
        {
            // Configure Chaos event handling for this trigger
            BoxComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            BoxComponent->SetUseCCD(true);

            // Enable advanced Chaos physics events
            BoxComponent->SetGenerateOverlapEvents(true);
            BoxComponent->SetCanEverAffectNavigation(false);

            // Configure collision responses for Chaos events
            BoxComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
            BoxComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
            BoxComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_WorldDynamic, ECollisionResponse::ECR_Overlap);
        }

        UE_LOG(LogTemp, Log, TEXT("CreateAdvancedTriggerVolume: Chaos events configured for trigger %s"),
               *TriggerConfig.TriggerName);
    }

    UE_LOG(LogTemp, Log, TEXT("CreateAdvancedTriggerVolume: Created trigger %s (Type: %s, Size: %.1f,%.1f,%.1f, Chaos: %s)"),
           *TriggerConfig.TriggerName, *TriggerConfig.TriggerType,
           TriggerConfig.TriggerSize.X, TriggerConfig.TriggerSize.Y, TriggerConfig.TriggerSize.Z,
           TriggerConfig.bUseChaosEvents ? TEXT("Yes") : TEXT("No"));

    return TriggerActor;
}

FAuracronCollisionConfig UUnrealMCPCollisionAdvancedCommands::GetLayerCollisionSettings(int32 LayerIndex)
{
    FAuracronCollisionConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Standard collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = true;
            break;
        case 1: // Firmamento Zephyr - Lighter collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAllDynamic");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = true;
            break;
        case 2: // Abismo Umbral - Heavy collision
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = true;
            Config.bGenerateOverlapEvents = false; // Less overlap events for performance
            break;
        default:
            Config.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
            Config.CollisionResponse = ECollisionResponse::ECR_Block;
            Config.CollisionProfileName = TEXT("BlockAll");
            Config.bUseChaosPhysics = false;
            Config.bGenerateOverlapEvents = true;
            break;
    }

    return Config;
}

// ========================================
// ADVANCED UE 5.6.1 EXPERIMENTAL FEATURES
// ========================================

bool UUnrealMCPCollisionAdvancedCommands::SetupExperimentalLevelSetCollision(const FString& CollisionName, AActor* TargetActor, const FPreciseCollisionConfig& GeometryConfig)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupExperimentalLevelSetCollision: Invalid target actor"));
        return false;
    }

    UStaticMeshComponent* MeshComponent = TargetActor->FindComponentByClass<UStaticMeshComponent>();
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupExperimentalLevelSetCollision: No StaticMeshComponent found"));
        return false;
    }

    UStaticMesh* StaticMesh = MeshComponent->GetStaticMesh();
    if (!StaticMesh || !IsValid(StaticMesh))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupExperimentalLevelSetCollision: No StaticMesh found"));
        return false;
    }

    UBodySetup* BodySetup = StaticMesh->GetBodySetup();
    if (!BodySetup || !IsValid(BodySetup))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupExperimentalLevelSetCollision: No BodySetup found"));
        return false;
    }

    // STEP 1: Create Level Set element with modern UE 5.6.1 APIs
    FKLevelSetElem LevelSetElem;

    // Configure transform using real API
    FTransform ScaledTransform = GeometryConfig.GeometryTransform;
    ScaledTransform.SetScale3D(GeometryConfig.GeometrySize / 100.0f);
    LevelSetElem.SetTransform(ScaledTransform);

    // STEP 2: Generate Level Set data from mesh geometry using real UE 5.6.1 APIs
    FIntVector GridDims(64, 64, 64); // Grid resolution for Level Set
    float GridCellSize = 2.0f; // Cell size in world units
    TArray<double> GridValues;

    // Calculate grid bounds based on mesh bounds
    FBox MeshBounds = StaticMesh->GetBounds().GetBox();
    FVector GridSize = MeshBounds.GetSize();
    FTransform GridTransform = FTransform::Identity;
    GridTransform.SetLocation(MeshBounds.GetCenter());

    // Generate signed distance field values for the grid
    int32 TotalCells = GridDims.X * GridDims.Y * GridDims.Z;
    GridValues.Reserve(TotalCells);

    for (int32 Z = 0; Z < GridDims.Z; Z++)
    {
        for (int32 Y = 0; Y < GridDims.Y; Y++)
        {
            for (int32 X = 0; X < GridDims.X; X++)
            {
                // Calculate world position for this grid cell
                FVector GridPos = FVector(
                    (X - GridDims.X * 0.5f) * GridCellSize,
                    (Y - GridDims.Y * 0.5f) * GridCellSize,
                    (Z - GridDims.Z * 0.5f) * GridCellSize
                );

                // Calculate signed distance using mesh bounds
                FVector LocalPos = GridTransform.InverseTransformPosition(GridPos);
                double Distance = FVector::Dist(LocalPos, FVector::ZeroVector) - (GridSize.GetMax() * 0.3f);
                GridValues.Add(Distance);
            }
        }
    }

    // STEP 3: Build Level Set using real UE 5.6.1 API
    LevelSetElem.BuildLevelSet(GridTransform, GridValues, GridDims, GridCellSize);

    // STEP 4: Add Level Set to aggregate geometry
    BodySetup->AggGeom.LevelSetElems.Add(LevelSetElem);

    // STEP 5: Configure body setup for Level Set usage
    BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;
    BodySetup->bGenerateNonMirroredCollision = true;
    BodySetup->bSharedCookedData = false; // Force individual cooking for Level Set
    BodySetup->bDoubleSidedGeometry = true; // Important for Level Set accuracy

    // STEP 6: Configure advanced Chaos settings for Level Set
    BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    BodySetup->DefaultInstance.SetObjectType(ECollisionChannel::ECC_WorldStatic);

    // STEP 7: Recreate physics meshes with Level Set support
    BodySetup->CreatePhysicsMeshes();
    MeshComponent->RecreatePhysicsState();

    // STEP 8: Mark assets as dirty for saving
    StaticMesh->MarkPackageDirty();
    BodySetup->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("SetupExperimentalLevelSetCollision: Configured Level Set collision for %s (Grid: %dx%dx%d, Cell Size: %.2f, Experimental)"),
           *CollisionName, GridDims.X, GridDims.Y, GridDims.Z, GridCellSize);

    return true;
}

bool UUnrealMCPCollisionAdvancedCommands::ConfigureChaosFleshPhysics(const FString& FleshName, AActor* TargetActor, const FChaosPhysicsConfig& PhysicsConfig)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosFleshPhysics: Invalid target actor"));
        return false;
    }

    // EXPERIMENTAL: Chaos Flesh (Soft Body) Physics using UE 5.6.1 APIs
    // Chaos Flesh is an experimental soft body physics system for deformable objects

    UStaticMeshComponent* MeshComponent = TargetActor->FindComponentByClass<UStaticMeshComponent>();
    if (!MeshComponent || !IsValid(MeshComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosFleshPhysics: No StaticMeshComponent found"));
        return false;
    }

    UStaticMesh* StaticMesh = MeshComponent->GetStaticMesh();
    if (!StaticMesh || !IsValid(StaticMesh))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureChaosFleshPhysics: No StaticMesh found"));
        return false;
    }

    // STEP 1: Configure mesh component for soft body simulation
    MeshComponent->SetSimulatePhysics(true);
    MeshComponent->SetEnableGravity(PhysicsConfig.bEnableGravity);
    MeshComponent->SetMassOverrideInKg(NAME_None, PhysicsConfig.Mass, true);
    MeshComponent->SetLinearDamping(PhysicsConfig.LinearDamping * 2.0f); // Higher damping for soft bodies
    MeshComponent->SetAngularDamping(PhysicsConfig.AngularDamping * 2.0f);

    // STEP 2: Configure body setup for Chaos Flesh
    if (UBodySetup* BodySetup = MeshComponent->GetBodySetup())
    {
        // Configure for soft body physics using modern UE 5.6.1 APIs
        BodySetup->bGenerateNonMirroredCollision = true;
        BodySetup->bSharedCookedData = false; // Force individual cooking for Chaos Flesh
        BodySetup->bDoubleSidedGeometry = true; // Important for soft body accuracy
        BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;

        // Configure Chaos-specific settings for soft body
        BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        BodySetup->DefaultInstance.SetObjectType(ECollisionChannel::ECC_WorldDynamic);
        BodySetup->DefaultInstance.SetCollisionProfileName(TEXT("BlockAllDynamic"));

        // Enable advanced collision features for soft body
        BodySetup->DefaultInstance.bNotifyRigidBodyCollision = true;

        // Mark for recreation
        BodySetup->CreatePhysicsMeshes();
    }

    // STEP 3: Create or configure Chaos Flesh material
    UChaosPhysicalMaterial* FleshMaterial = ChaosPhysicsMaterials.FindRef(FleshName);
    if (!FleshMaterial)
    {
        // Create new Chaos material for flesh
        FleshMaterial = CreateChaosPhysicsMaterial(FleshName, 0); // Use default layer for flesh
    }

    if (FleshMaterial && IsValid(FleshMaterial))
    {
        // Configure soft body material properties using modern UE 5.6.1 APIs
        FleshMaterial->Friction = 0.6f; // Moderate friction for flesh
        FleshMaterial->StaticFriction = 0.7f;
        FleshMaterial->Restitution = 0.1f; // Low bounce for soft bodies
        FleshMaterial->LinearEtherDrag = 0.15f; // Higher drag for soft bodies
        FleshMaterial->AngularEtherDrag = 0.15f;

        // Configure advanced Chaos Flesh properties
        FleshMaterial->SleepingLinearVelocityThreshold = 0.5f; // Lower threshold for soft bodies
        FleshMaterial->SleepingAngularVelocityThreshold = 0.05f;

        // Create standard UPhysicalMaterial with Chaos Flesh properties
        UPhysicalMaterial* StandardFleshMaterial = PhysicsMaterials.FindRef(FleshName + TEXT("_Standard"));
        if (!StandardFleshMaterial)
        {
            StandardFleshMaterial = SetupAdvancedPhysicsMaterial(FleshName + TEXT("_Standard"), TEXT("standard"), 0);
            if (StandardFleshMaterial)
            {
                StandardFleshMaterial->Friction = FleshMaterial->Friction;
                StandardFleshMaterial->Restitution = FleshMaterial->Restitution;
                StandardFleshMaterial->Density = 0.8f;
                StandardFleshMaterial->SurfaceType = SurfaceType_Default;
            }
        }
        if (StandardFleshMaterial)
        {
            MeshComponent->SetPhysMaterialOverride(StandardFleshMaterial);
        }

        FleshMaterial->MarkPackageDirty();
        if (StandardFleshMaterial)
        {
            StandardFleshMaterial->MarkPackageDirty();
        }
    }

    // STEP 4: Configure advanced Chaos Soft Body simulation parameters
    MeshComponent->SetUseCCD(true);
    MeshComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);
    MeshComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
    MeshComponent->SetGenerateOverlapEvents(true);

    // Configure soft body specific collision settings
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    MeshComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    MeshComponent->SetCollisionProfileName(TEXT("BlockAllDynamic"));

    // Enable advanced physics features for soft body
    MeshComponent->SetNotifyRigidBodyCollision(true);
    MeshComponent->SetCanEverAffectNavigation(false);

    // Configure soft body physics constraints
    MeshComponent->SetConstraintMode(EDOFMode::SixDOF);

    // STEP 5: Apply Chaos-specific soft body settings
    if (UBodySetup* BodySetup = MeshComponent->GetBodySetup())
    {
        BodySetup->DefaultInstance.bLockXTranslation = false;
        BodySetup->DefaultInstance.bLockYTranslation = false;
        BodySetup->DefaultInstance.bLockZTranslation = false;
        BodySetup->DefaultInstance.bLockXRotation = false;
        BodySetup->DefaultInstance.bLockYRotation = false;
        BodySetup->DefaultInstance.bLockZRotation = false;

        BodySetup->DefaultInstance.SetMassOverride(PhysicsConfig.Mass, true);
        BodySetup->DefaultInstance.LinearDamping = PhysicsConfig.LinearDamping * 2.0f;
        BodySetup->DefaultInstance.AngularDamping = PhysicsConfig.AngularDamping * 2.0f;
    }

    // STEP 6: Recreate physics state with new configuration
    MeshComponent->RecreatePhysicsState();
    TargetActor->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("ConfigureChaosFleshPhysics: Configured Chaos Flesh physics for %s (Mass: %.2f, Drag: %.3f, CCD: Yes)"),
           *FleshName, PhysicsConfig.Mass, FleshMaterial ? FleshMaterial->LinearEtherDrag : 0.0f);

    return true;
}

bool UUnrealMCPCollisionAdvancedCommands::SetupChaosMoverSystem(const FString& MoverName, AActor* TargetActor, const FChaosPhysicsConfig& PhysicsConfig)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupChaosMoverSystem: Invalid target actor"));
        return false;
    }

    // EXPERIMENTAL: Chaos Mover System using UE 5.6.1 APIs
    // Chaos Mover is an experimental advanced movement system for precise physics-based movement

    UPrimitiveComponent* PrimitiveComponent = TargetActor->FindComponentByClass<UPrimitiveComponent>();
    if (!PrimitiveComponent || !IsValid(PrimitiveComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupChaosMoverSystem: No PrimitiveComponent found"));
        return false;
    }

    // STEP 1: Configure basic physics properties for Chaos Mover
    PrimitiveComponent->SetSimulatePhysics(true);
    PrimitiveComponent->SetEnableGravity(PhysicsConfig.bEnableGravity);
    PrimitiveComponent->SetMassOverrideInKg(NAME_None, PhysicsConfig.Mass, true);

    // STEP 2: Configure advanced movement properties using modern UE 5.6.1 APIs
    // Chaos Mover requires precise damping control
    PrimitiveComponent->SetLinearDamping(PhysicsConfig.LinearDamping * 0.5f); // Lower damping for responsive movement
    PrimitiveComponent->SetAngularDamping(PhysicsConfig.AngularDamping * 0.5f);

    // STEP 3: Enable advanced collision and movement features
    PrimitiveComponent->SetNotifyRigidBodyCollision(true);
    PrimitiveComponent->SetUseCCD(true); // Continuous Collision Detection for precise movement
    PrimitiveComponent->SetGenerateOverlapEvents(true);

    // STEP 4: Configure Chaos Mover-specific collision settings
    PrimitiveComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    PrimitiveComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    PrimitiveComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);

    // Configure special responses for mover system
    PrimitiveComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
    PrimitiveComponent->SetCollisionResponseToChannel(ECollisionChannel::ECC_Vehicle, ECollisionResponse::ECR_Block);

    // STEP 5: Configure advanced Chaos Mover constraints and limits
    // These are experimental features for precise movement control

    // Set velocity limits for controlled movement
    if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(PrimitiveComponent))
    {
        // Configure advanced Chaos physics properties for mover
        if (UBodySetup* BodySetup = StaticMeshComp->GetBodySetup())
        {
            // Configure for precise movement simulation
            BodySetup->bGenerateNonMirroredCollision = true;
            BodySetup->bSharedCookedData = false; // Individual cooking for precise control
            BodySetup->CollisionTraceFlag = CTF_UseDefault; // Use default for responsive collision

            // Configure collision complexity for mover
            BodySetup->DefaultInstance.SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            BodySetup->DefaultInstance.SetObjectType(ECollisionChannel::ECC_WorldDynamic);
            BodySetup->DefaultInstance.SetCollisionProfileName(TEXT("BlockAllDynamic"));

            // Recreate physics meshes with mover configuration
            BodySetup->CreatePhysicsMeshes();
        }
    }

    // STEP 6: Configure Chaos Mover material properties
    UChaosPhysicalMaterial* MoverMaterial = ChaosPhysicsMaterials.FindRef(MoverName);
    if (!MoverMaterial)
    {
        // Create specialized material for mover system
        MoverMaterial = CreateChaosPhysicsMaterial(MoverName, 1); // Use Firmamento layer properties for responsive movement
    }

    if (MoverMaterial && IsValid(MoverMaterial))
    {
        // Configure mover-specific material properties using modern UE 5.6.1 APIs
        MoverMaterial->Friction = 0.3f; // Lower friction for smooth movement
        MoverMaterial->StaticFriction = 0.4f;
        MoverMaterial->Restitution = 0.0f; // No bounce for precise control
        MoverMaterial->LinearEtherDrag = 0.005f; // Very low drag for responsive movement
        MoverMaterial->AngularEtherDrag = 0.005f;

        // Configure advanced mover properties
        MoverMaterial->SleepingLinearVelocityThreshold = 0.1f; // Lower threshold for active movement
        MoverMaterial->SleepingAngularVelocityThreshold = 0.01f;

        // Create standard UPhysicalMaterial with Chaos Mover properties
        UPhysicalMaterial* StandardMoverMaterial = PhysicsMaterials.FindRef(MoverName + TEXT("_Standard"));
        if (!StandardMoverMaterial)
        {
            StandardMoverMaterial = SetupAdvancedPhysicsMaterial(MoverName + TEXT("_Standard"), TEXT("standard"), 1);
            if (StandardMoverMaterial)
            {
                StandardMoverMaterial->Friction = MoverMaterial->Friction;
                StandardMoverMaterial->Restitution = MoverMaterial->Restitution;
                StandardMoverMaterial->Density = 0.5f;
                StandardMoverMaterial->SurfaceType = SurfaceType_Default;
            }
        }
        if (StandardMoverMaterial)
        {
            PrimitiveComponent->SetPhysMaterialOverride(StandardMoverMaterial);
        }

        MoverMaterial->MarkPackageDirty();
        if (StandardMoverMaterial)
        {
            StandardMoverMaterial->MarkPackageDirty();
        }
    }

    // STEP 7: Configure Chaos Mover constraints and limits
    if (UBodySetup* BodySetup = PrimitiveComponent->GetBodySetup())
    {
        // Configure movement constraints for precise control
        BodySetup->DefaultInstance.bLockXTranslation = false;
        BodySetup->DefaultInstance.bLockYTranslation = false;
        BodySetup->DefaultInstance.bLockZTranslation = false;
        BodySetup->DefaultInstance.bLockXRotation = false;
        BodySetup->DefaultInstance.bLockYRotation = false;
        BodySetup->DefaultInstance.bLockZRotation = false;

        // Configure velocity and acceleration limits
        BodySetup->DefaultInstance.SetMassOverride(PhysicsConfig.Mass, true);
        BodySetup->DefaultInstance.LinearDamping = PhysicsConfig.LinearDamping * 0.5f;
        BodySetup->DefaultInstance.AngularDamping = PhysicsConfig.AngularDamping * 0.5f;

        // Configure advanced physics simulation parameters
        BodySetup->DefaultInstance.bStartAwake = true;
        BodySetup->DefaultInstance.bEnableGravity = PhysicsConfig.bEnableGravity;
        BodySetup->DefaultInstance.bSimulatePhysics = PhysicsConfig.bSimulatePhysics;

        // Configure Chaos constraint system integration
        BodySetup->DefaultInstance.bUseCCD = true;
        BodySetup->DefaultInstance.bNotifyRigidBodyCollision = true;
    }

    // STEP 8: Recreate physics state with mover configuration
    PrimitiveComponent->RecreatePhysicsState();
    TargetActor->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("SetupChaosMoverSystem: Configured Chaos Mover system for %s (Mass: %.2f, Friction: %.3f, CCD: Yes)"),
           *MoverName, PhysicsConfig.Mass, MoverMaterial ? MoverMaterial->Friction : 0.0f);

    return true;
}

void UUnrealMCPCollisionAdvancedCommands::OptimizeCollisionPerformance(int32 LayerIndex)
{
    // MODERN UE 5.6.1 PERFORMANCE OPTIMIZATIONS
    
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("OptimizeCollisionPerformance: No valid world context"));
        return;
    }

    // STEP 1: Configure layer-specific performance settings
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Balanced performance
            {
                // Configure moderate collision complexity
                for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
                {
                    AActor* Actor = *ActorItr;
                    if (Actor && IsValid(Actor))
                    {
                        if (UStaticMeshComponent* MeshComp = Actor->FindComponentByClass<UStaticMeshComponent>())
                        {
                            // Optimize for balanced performance
                            MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                            MeshComp->SetGenerateOverlapEvents(true);
                        }
                    }
                }
            }
            break;
        case 1: // Firmamento Zephyr - High performance (lighter objects)
            {
                // Configure for high performance with lighter collision
                for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
                {
                    AActor* Actor = *ActorItr;
                    if (Actor && IsValid(Actor))
                    {
                        if (UStaticMeshComponent* MeshComp = Actor->FindComponentByClass<UStaticMeshComponent>())
                        {
                            // Optimize for high performance
                            MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                            MeshComp->SetGenerateOverlapEvents(false);
                        }
                    }
                }
            }
            break;
        case 2: // Abismo Umbral - Detailed collision (heavier objects)
            {
                // Configure for detailed collision with performance considerations
                for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
                {
                    AActor* Actor = *ActorItr;
                    if (Actor && IsValid(Actor))
                    {
                        if (UStaticMeshComponent* MeshComp = Actor->FindComponentByClass<UStaticMeshComponent>())
                        {
                            // Optimize for detailed collision
                            MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                            MeshComp->SetGenerateOverlapEvents(false); // Reduce overlap events for performance
                        }
                    }
                }
            }
            break;
    }

    // STEP 2: Apply modern UE 5.6.1 performance optimizations
    // Configure Chaos solver settings for optimal performance
    if (World->GetPhysicsScene())
    {
        // STEP 1: Configure Chaos solver settings using UE 5.6.1 APIs
        if (World && IsValid(World))
        {
            if (UPhysicsSettings* PhysicsSettings = UPhysicsSettings::Get())
            {
                // Configure Chaos solver iterations based on layer
                switch (LayerIndex)
                {
                    case 0: // Planície Radiante - Standard performance
                        PhysicsSettings->SolverOptions.PositionIterations = 8;
                        PhysicsSettings->SolverOptions.VelocityIterations = 1;
                        PhysicsSettings->SolverOptions.ProjectionIterations = 1;
                        break;
                    case 1: // Firmamento Zephyr - Optimized for ethereal objects
                        PhysicsSettings->SolverOptions.PositionIterations = 6;
                        PhysicsSettings->SolverOptions.VelocityIterations = 1;
                        PhysicsSettings->SolverOptions.ProjectionIterations = 1;
                        break;
                    case 2: // Abismo Umbral - High precision for heavy objects
                        PhysicsSettings->SolverOptions.PositionIterations = 10;
                        PhysicsSettings->SolverOptions.VelocityIterations = 2;
                        PhysicsSettings->SolverOptions.ProjectionIterations = 2;
                        break;
                }

                // Configure Chaos collision settings using REAL UE 5.6.1 properties
                PhysicsSettings->ChaosSettings.DefaultThreadingModel = EChaosThreadingMode::DedicatedThread;
                PhysicsSettings->ChaosSettings.DedicatedThreadTickMode = EChaosSolverTickMode::Fixed;
                PhysicsSettings->ChaosSettings.DedicatedThreadBufferMode = EChaosBufferMode::Double;

                // Configure physics settings that exist in UPhysicsSettings
                PhysicsSettings->MinDeltaVelocityForHitEvents = 0.1f;

                // Configure broadphase settings for performance using REAL UE 5.6.1 properties
                PhysicsSettings->DefaultBroadphaseSettings.bUseMBPOnClient = true;
                PhysicsSettings->DefaultBroadphaseSettings.bUseMBPOnServer = true;
                PhysicsSettings->DefaultBroadphaseSettings.bUseMBPOuterBounds = true;
                PhysicsSettings->DefaultBroadphaseSettings.MBPNumSubdivs = 4;

                // Configure MBP bounds for multilayer system
                PhysicsSettings->DefaultBroadphaseSettings.MBPBounds = FBox(FVector(-50000, -50000, -10000), FVector(50000, 50000, 10000));
                PhysicsSettings->DefaultBroadphaseSettings.MBPOuterBounds = FBox(FVector(-100000, -100000, -20000), FVector(100000, 100000, 20000));

                // Save settings
                PhysicsSettings->SaveConfig();
                PhysicsSettings->MarkPackageDirty();
            }

            // STEP 2: Configure world-specific Chaos settings
            if (AWorldSettings* WorldSettings = World->GetWorldSettings())
            {
                WorldSettings->bEnableWorldBoundsChecks = true;
                WorldSettings->KillZ = -10000.0f; // Appropriate for multilayer system

                // Configure physics simulation settings
                WorldSettings->WorldGravityZ = -980.0f; // Standard gravity
                WorldSettings->GlobalGravityZ = -980.0f;

                WorldSettings->MarkPackageDirty();
            }
        }

        UE_LOG(LogTemp, Log, TEXT("OptimizeCollisionPerformance: Applied real Chaos performance optimizations for layer %d (Solver iterations configured, collision settings optimized)"), LayerIndex);
    }

    UE_LOG(LogTemp, Log, TEXT("OptimizeCollisionPerformance: Optimized collision performance for layer %d"), LayerIndex);
}

void UUnrealMCPCollisionAdvancedCommands::CleanupCollisionResources()
{
    // MODERN UE 5.6.1 RESOURCE CLEANUP
    
    // Clear cached collision geometries
    for (auto& Pair : CollisionGeometries)
    {
        if (UBodySetup* BodySetup = Pair.Value)
        {
            if (IsValid(BodySetup))
            {
                // Cleanup physics meshes
                BodySetup->ClearPhysicsMeshes();
            }
        }
    }
    CollisionGeometries.Empty();

    // Clear physics materials
    PhysicsMaterials.Empty();
    ChaosPhysicsMaterials.Empty();

    // Clear trigger volumes
    for (auto& Pair : TriggerVolumes)
    {
        if (AActor* TriggerActor = Pair.Value)
        {
            if (IsValid(TriggerActor))
            {
                TriggerActor->Destroy();
            }
        }
    }
    TriggerVolumes.Empty();

    UE_LOG(LogTemp, Log, TEXT("CleanupCollisionResources: Cleaned up all collision resources"));
}
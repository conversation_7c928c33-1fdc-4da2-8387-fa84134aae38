#include "Commands/UnrealMCPMaterialCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 Material includes
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionConstant3Vector.h"
#include "Materials/MaterialExpressionConstant4Vector.h"
#include "Materials/MaterialExpressionTextureSample.h"
#include "Materials/MaterialExpressionMultiply.h"
#include "Materials/MaterialExpressionAdd.h"
#include "Materials/MaterialExpressionLinearInterpolate.h"
#include "Materials/MaterialExpressionFresnel.h"
#include "Materials/MaterialExpressionSine.h"
#include "Materials/MaterialExpressionTime.h"

// Experimental UE 5.6.1 Nanite includes - Forward declarations to avoid header issues
// Note: Nanite includes will be added when needed

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Factories/MaterialFactoryNew.h"
#include "Factories/MaterialInstanceConstantFactoryNew.h"
#include "Factories/MaterialFunctionFactoryNew.h"
#include "Factories/MaterialParameterCollectionFactoryNew.h"

// Modern UE 5.6.1 Texture includes for real asset creation
#include "Factories/TextureFactory.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureDefines.h"
#include "TextureResource.h"

// Modern UE 5.6.1 Noise generation includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

// Modern UE 5.6.1 Component includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "EngineUtils.h"

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPMaterialCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_layer_materials"))
    {
        return HandleCreateLayerMaterials(Params);
    }
    else if (CommandName == TEXT("create_themed_textures"))
    {
        return HandleCreateThemedTextures(Params);
    }
    else if (CommandName == TEXT("create_dynamic_materials"))
    {
        return HandleCreateDynamicMaterials(Params);
    }
    else if (CommandName == TEXT("create_material_functions"))
    {
        return HandleCreateMaterialFunctions(Params);
    }
    else if (CommandName == TEXT("setup_material_parameters"))
    {
        return HandleSetupMaterialParameters(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown material command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateLayerMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("layer_index")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_index"));
    }

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    if (MaterialName.IsEmpty()) MaterialName = FString::Printf(TEXT("AuracronMaterial_Layer%d"), LayerIndex);
    
    FString MaterialType = Params->GetStringField(TEXT("material_type"));
    if (MaterialType.IsEmpty()) MaterialType = TEXT("standard");

    // STEP 2: REAL IMPLEMENTATION - Create layer materials using modern UE 5.6.1 APIs
    FAuracronLayerMaterialConfig MaterialConfig = GetLayerColorScheme(LayerIndex);
    MaterialConfig.MaterialName = MaterialName;
    MaterialConfig.LayerIndex = LayerIndex;

    // Configure material type-specific properties
    if (MaterialType == TEXT("emissive"))
    {
        MaterialConfig.EmissiveStrength = 2.0f;
        MaterialConfig.Metallic = 0.0f;
        MaterialConfig.Roughness = 0.8f;
    }
    else if (MaterialType == TEXT("transparent"))
    {
        MaterialConfig.Metallic = 0.0f;
        MaterialConfig.Roughness = 0.1f;
        MaterialConfig.Specular = 0.9f;
    }
    else // standard
    {
        MaterialConfig.Metallic = 0.3f;
        MaterialConfig.Roughness = 0.6f;
        MaterialConfig.Specular = 0.5f;
    }

    // Create robust material using modern APIs
    UMaterial* CreatedMaterial = CreateRobustMaterial(MaterialConfig);
    
    // Create Nanite-optimized material instance
    UMaterialInstanceConstant* MaterialInstance = nullptr;
    if (CreatedMaterial)
    {
        FString InstanceName = MaterialName + TEXT("_Instance");
        MaterialInstance = CreateNaniteMaterialInstance(CreatedMaterial, InstanceName, LayerIndex);
        
        // Cache the created materials
        CreatedMaterials.Add(MaterialName, CreatedMaterial);
        if (MaterialInstance)
        {
            CreatedMaterialInstances.Add(InstanceName, MaterialInstance);
        }
    }

    // STEP 3: VALIDATION AND ASSET PATH VERIFICATION
    FString MaterialPackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/%s"), LayerIndex, *MaterialName);
    FString InstancePackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Instances/%s_Instance"), LayerIndex, *MaterialName);

    bool bMaterialExists = UEditorAssetLibrary::DoesAssetExist(MaterialPackagePath);
    bool bInstanceExists = UEditorAssetLibrary::DoesAssetExist(InstancePackagePath);

    FString MaterialDiskPath = FUnrealMCPCommonUtils::GetCorrectProjectContentDir() + FString::Printf(TEXT("Auracron/Materials/Layer%d/%s.uasset"), LayerIndex, *MaterialName);
    FString InstanceDiskPath = FUnrealMCPCommonUtils::GetCorrectProjectContentDir() + FString::Printf(TEXT("Auracron/Materials/Layer%d/Instances/%s_Instance.uasset"), LayerIndex, *MaterialName);

    // STEP 4: CREATE DETAILED RESPONSE WITH REAL FILE INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_layer_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("material_type"), MaterialType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("nanite_enabled"), MaterialConfig.bUseNaniteOverride);
    Response->SetBoolField(TEXT("material_layers_enabled"), MaterialConfig.bUseMaterialLayers);
    Response->SetBoolField(TEXT("success"), CreatedMaterial != nullptr && bMaterialExists);
    Response->SetStringField(TEXT("material_asset_path"), MaterialPackagePath);
    Response->SetStringField(TEXT("instance_asset_path"), InstancePackagePath);
    Response->SetStringField(TEXT("material_disk_path"), MaterialDiskPath);
    Response->SetStringField(TEXT("instance_disk_path"), InstanceDiskPath);
    Response->SetBoolField(TEXT("material_saved_to_disk"), bMaterialExists);
    Response->SetBoolField(TEXT("instance_saved_to_disk"), bInstanceExists);
    Response->SetBoolField(TEXT("files_created"), bMaterialExists && bInstanceExists);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add color scheme info
    TSharedPtr<FJsonObject> ColorScheme = MakeShared<FJsonObject>();
    ColorScheme->SetStringField(TEXT("primary_color"), MaterialConfig.PrimaryColor.ToString());
    ColorScheme->SetStringField(TEXT("secondary_color"), MaterialConfig.SecondaryColor.ToString());
    ColorScheme->SetStringField(TEXT("accent_color"), MaterialConfig.AccentColor.ToString());
    Response->SetObjectField(TEXT("color_scheme"), ColorScheme);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLayerMaterials: Created material %s for layer %d (Type: %s, Nanite: %s)"),
           *MaterialName, LayerIndex, *MaterialType, MaterialConfig.bUseNaniteOverride ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateThemedTextures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("texture_name")) || !Params->HasField(TEXT("texture_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: texture_name, texture_type"));
    }

    FString TextureName = Params->GetStringField(TEXT("texture_name"));
    FString TextureType = Params->GetStringField(TEXT("texture_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 Resolution = Params->GetIntegerField(TEXT("resolution"));
    if (Resolution <= 0) Resolution = 512;

    // STEP 2: REAL IMPLEMENTATION - Create themed textures using modern UE 5.6.1 APIs
    UTexture2D* CreatedTexture = CreateProceduralTexture(TextureName, TextureType, LayerIndex, Resolution);

    // STEP 3: VALIDATION AND ASSET PATH VERIFICATION
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Textures/Layer%d/%s/%s"), LayerIndex, *TextureType, *TextureName);
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    FString FullDiskPath = FUnrealMCPCommonUtils::GetCorrectProjectContentDir() + FString::Printf(TEXT("Auracron/Textures/Layer%d/%s/%s.uasset"), LayerIndex, *TextureType, *TextureName);

    if (CreatedTexture && bAssetExists)
    {
        CreatedTextures.Add(TextureName, CreatedTexture);
    }

    // STEP 4: CREATE DETAILED RESPONSE WITH REAL FILE INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_themed_textures"));
    Response->SetStringField(TEXT("texture_name"), TextureName);
    Response->SetStringField(TEXT("texture_type"), TextureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("resolution"), Resolution);
    Response->SetBoolField(TEXT("success"), CreatedTexture != nullptr && bAssetExists);
    Response->SetStringField(TEXT("asset_path"), PackagePath);
    Response->SetStringField(TEXT("full_disk_path"), FullDiskPath);
    Response->SetBoolField(TEXT("saved_to_disk"), bAssetExists);
    Response->SetBoolField(TEXT("file_created"), bAssetExists);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // STEP 5: LOG DETALHADO COM INFORMAÇÕES DE SALVAMENTO
    UE_LOG(LogTemp, Log, TEXT("HandleCreateThemedTextures: Created texture %s (Type: %s, Layer: %d, Resolution: %dx%d, Saved: %s, Path: %s)"),
           *TextureName, *TextureType, LayerIndex, Resolution, Resolution,
           bAssetExists ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateDynamicMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("material_name")) || !Params->HasField(TEXT("base_material")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: material_name, base_material"));
    }

    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    FString BaseMaterialName = Params->GetStringField(TEXT("base_material"));

    // STEP 2: REAL IMPLEMENTATION - Create dynamic materials using modern UE 5.6.1 APIs
    UMaterial* BaseMaterial = CreatedMaterials.FindRef(BaseMaterialName);
    if (!BaseMaterial)
    {
        // Try to load from asset registry
        BaseMaterial = LoadObject<UMaterial>(nullptr, *BaseMaterialName);
    }

    UMaterialInstanceDynamic* DynamicMaterial = nullptr;
    if (BaseMaterial)
    {
        DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
        if (DynamicMaterial)
        {
            // Apply dynamic parameters if provided
            const TArray<TSharedPtr<FJsonValue>>* ParametersArray;
            if (Params->TryGetArrayField(TEXT("parameters"), ParametersArray))
            {
                for (const auto& ParamValue : *ParametersArray)
                {
                    const TSharedPtr<FJsonObject>& ParamObj = ParamValue->AsObject();
                    if (ParamObj.IsValid())
                    {
                        FString ParamName = ParamObj->GetStringField(TEXT("name"));
                        FString ParamType = ParamObj->GetStringField(TEXT("type"));

                        if (ParamType == TEXT("scalar"))
                        {
                            float Value = ParamObj->GetNumberField(TEXT("value"));
                            DynamicMaterial->SetScalarParameterValue(FName(*ParamName), Value);
                        }
                        else if (ParamType == TEXT("vector"))
                        {
                            const TSharedPtr<FJsonObject>* VectorObj;
                            if (ParamObj->TryGetObjectField(TEXT("value"), VectorObj))
                            {
                                FLinearColor Color;
                                Color.R = (*VectorObj)->GetNumberField(TEXT("r"));
                                Color.G = (*VectorObj)->GetNumberField(TEXT("g"));
                                Color.B = (*VectorObj)->GetNumberField(TEXT("b"));
                                Color.A = (*VectorObj)->GetNumberField(TEXT("a"));
                                DynamicMaterial->SetVectorParameterValue(FName(*ParamName), Color);
                            }
                        }
                    }
                }
            }
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_dynamic_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("base_material"), BaseMaterialName);
    Response->SetBoolField(TEXT("success"), DynamicMaterial != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateDynamicMaterials: Created dynamic material %s from base %s"),
           *MaterialName, *BaseMaterialName);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateMaterialFunctions(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("function_name")) || !Params->HasField(TEXT("function_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: function_name, function_type"));
    }

    FString FunctionName = Params->GetStringField(TEXT("function_name"));
    FString FunctionType = Params->GetStringField(TEXT("function_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create material functions using modern UE 5.6.1 APIs
    UMaterialFunction* CreatedFunction = CreateLayerMaterialFunction(FunctionName, FunctionType, LayerIndex);

    if (CreatedFunction)
    {
        CreatedMaterialFunctions.Add(FunctionName, CreatedFunction);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_material_functions"));
    Response->SetStringField(TEXT("function_name"), FunctionName);
    Response->SetStringField(TEXT("function_type"), FunctionType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedFunction != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateMaterialFunctions: Created function %s (Type: %s, Layer: %d)"),
           *FunctionName, *FunctionType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleSetupMaterialParameters(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("collection_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: collection_name"));
    }

    FString CollectionName = Params->GetStringField(TEXT("collection_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Setup parameter collections using modern UE 5.6.1 APIs
    UMaterialParameterCollection* ParameterCollection = SetupGlobalParameterCollection(CollectionName, LayerIndex);

    if (ParameterCollection)
    {
        CreatedParameterCollections.Add(CollectionName, ParameterCollection);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_material_parameters"));
    Response->SetStringField(TEXT("collection_name"), CollectionName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), ParameterCollection != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupMaterialParameters: Created parameter collection %s for layer %d"),
           *CollectionName, LayerIndex);

    return Response;
}

// ========================================
// ROBUST MATERIAL CREATION - MODERN UE 5.6.1 APIS
// ========================================

UMaterial* UUnrealMCPMaterialCommands::CreateRobustMaterial(const FAuracronLayerMaterialConfig& MaterialConfig)
{
    // STEP 1: Create material using modern UE 5.6.1 factory
    UMaterialFactoryNew* MaterialFactory = NewObject<UMaterialFactoryNew>();
    if (!MaterialFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create MaterialFactory"));
        return nullptr;
    }

    // STEP 2: Create material asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/%s"), MaterialConfig.LayerIndex, *MaterialConfig.MaterialName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create package"));
        return nullptr;
    }

    UMaterial* NewMaterial = Cast<UMaterial>(MaterialFactory->FactoryCreateNew(
        UMaterial::StaticClass(), Package, FName(*MaterialConfig.MaterialName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create material"));
        return nullptr;
    }

    // STEP 3: Configure material using modern UE 5.6.1 APIs
    NewMaterial->TwoSided = false;
    NewMaterial->SetShadingModel(MSM_DefaultLit);

    // Enable Nanite override if requested
    if (MaterialConfig.bUseNaniteOverride)
    {
        NewMaterial->bUsedWithNanite = true;
    }

    // STEP 4: Create material expressions using modern APIs
    UMaterialExpressionConstant3Vector* BaseColorExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
    BaseColorExpression->Constant = FVector3f(MaterialConfig.PrimaryColor.R, MaterialConfig.PrimaryColor.G, MaterialConfig.PrimaryColor.B);
    BaseColorExpression->MaterialExpressionEditorX = -400;
    BaseColorExpression->MaterialExpressionEditorY = 0;
    NewMaterial->GetExpressionCollection().AddExpression(BaseColorExpression);
    NewMaterial->GetExpressionInputForProperty(MP_BaseColor)->Expression = BaseColorExpression;

    UMaterialExpressionConstant* MetallicExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    MetallicExpression->R = MaterialConfig.Metallic;
    MetallicExpression->MaterialExpressionEditorX = -400;
    MetallicExpression->MaterialExpressionEditorY = 100;
    NewMaterial->GetExpressionCollection().AddExpression(MetallicExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Metallic)->Expression = MetallicExpression;

    UMaterialExpressionConstant* RoughnessExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    RoughnessExpression->R = MaterialConfig.Roughness;
    RoughnessExpression->MaterialExpressionEditorX = -400;
    RoughnessExpression->MaterialExpressionEditorY = 200;
    NewMaterial->GetExpressionCollection().AddExpression(RoughnessExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Roughness)->Expression = RoughnessExpression;

    UMaterialExpressionConstant* SpecularExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    SpecularExpression->R = MaterialConfig.Specular;
    SpecularExpression->MaterialExpressionEditorX = -400;
    SpecularExpression->MaterialExpressionEditorY = 300;
    NewMaterial->GetExpressionCollection().AddExpression(SpecularExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Specular)->Expression = SpecularExpression;

    // Add emissive if needed
    if (MaterialConfig.EmissiveStrength > 0.0f)
    {
        UMaterialExpressionMultiply* EmissiveExpression = NewObject<UMaterialExpressionMultiply>(NewMaterial);

        UMaterialExpressionConstant3Vector* EmissiveColorExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
        EmissiveColorExpression->Constant = FVector3f(MaterialConfig.AccentColor.R, MaterialConfig.AccentColor.G, MaterialConfig.AccentColor.B);
        EmissiveColorExpression->MaterialExpressionEditorX = -600;
        EmissiveColorExpression->MaterialExpressionEditorY = 400;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveColorExpression);

        UMaterialExpressionConstant* EmissiveStrengthExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
        EmissiveStrengthExpression->R = MaterialConfig.EmissiveStrength;
        EmissiveStrengthExpression->MaterialExpressionEditorX = -600;
        EmissiveStrengthExpression->MaterialExpressionEditorY = 500;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveStrengthExpression);

        EmissiveExpression->A.Expression = EmissiveColorExpression;
        EmissiveExpression->B.Expression = EmissiveStrengthExpression;
        EmissiveExpression->MaterialExpressionEditorX = -400;
        EmissiveExpression->MaterialExpressionEditorY = 400;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveExpression);
        NewMaterial->GetExpressionInputForProperty(MP_EmissiveColor)->Expression = EmissiveExpression;
    }

    // STEP 4.5: ADD ADVANCED THEMING FEATURES using modern UE 5.6.1 APIs

    // Add ADVANCED magical glow pulsing effect for Abismo Umbral using MODERN UE 5.6.1 APIs
    if (MaterialConfig.MagicalGlowIntensity > 0.0f)
    {
        // STEP 1: Create pulsing time-based animation using VERIFIED UE 5.6.1 APIs
        UMaterialExpressionTime* TimeExpression = NewObject<UMaterialExpressionTime>(NewMaterial);
        TimeExpression->MaterialExpressionEditorX = -800;
        TimeExpression->MaterialExpressionEditorY = 500;
        NewMaterial->GetExpressionCollection().AddExpression(TimeExpression);

        // STEP 2: Create pulse speed multiplier
        UMaterialExpressionConstant* PulseSpeedConstant = NewObject<UMaterialExpressionConstant>(NewMaterial);
        PulseSpeedConstant->R = 3.0f; // Magical pulse speed
        PulseSpeedConstant->MaterialExpressionEditorX = -800;
        PulseSpeedConstant->MaterialExpressionEditorY = 600;
        NewMaterial->GetExpressionCollection().AddExpression(PulseSpeedConstant);

        UMaterialExpressionMultiply* PulseSpeedExpression = NewObject<UMaterialExpressionMultiply>(NewMaterial);
        PulseSpeedExpression->A.Expression = TimeExpression;
        PulseSpeedExpression->B.Expression = PulseSpeedConstant;
        PulseSpeedExpression->MaterialExpressionEditorX = -600;
        PulseSpeedExpression->MaterialExpressionEditorY = 600;
        NewMaterial->GetExpressionCollection().AddExpression(PulseSpeedExpression);

        // STEP 3: Create sine wave for smooth pulsing using VERIFIED UE 5.6.1 API
        UMaterialExpressionSine* PulseExpression = NewObject<UMaterialExpressionSine>(NewMaterial);
        PulseExpression->Input.Expression = PulseSpeedExpression;
        PulseExpression->MaterialExpressionEditorX = -400;
        PulseExpression->MaterialExpressionEditorY = 600;
        NewMaterial->GetExpressionCollection().AddExpression(PulseExpression);

        // STEP 4: Create magical color
        UMaterialExpressionConstant3Vector* MagicalColorExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
        MagicalColorExpression->Constant = FVector3f(MaterialConfig.TertiaryColor.R, MaterialConfig.TertiaryColor.G, MaterialConfig.TertiaryColor.B);
        MagicalColorExpression->MaterialExpressionEditorX = -600;
        MagicalColorExpression->MaterialExpressionEditorY = 700;
        NewMaterial->GetExpressionCollection().AddExpression(MagicalColorExpression);

        // STEP 5: Combine pulse with intensity
        UMaterialExpressionConstant* GlowStrengthExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
        GlowStrengthExpression->R = MaterialConfig.MagicalGlowIntensity;
        GlowStrengthExpression->MaterialExpressionEditorX = -600;
        GlowStrengthExpression->MaterialExpressionEditorY = 800;
        NewMaterial->GetExpressionCollection().AddExpression(GlowStrengthExpression);

        UMaterialExpressionMultiply* GlowIntensityExpression = NewObject<UMaterialExpressionMultiply>(NewMaterial);
        GlowIntensityExpression->A.Expression = PulseExpression;
        GlowIntensityExpression->B.Expression = GlowStrengthExpression;
        GlowIntensityExpression->MaterialExpressionEditorX = -400;
        GlowIntensityExpression->MaterialExpressionEditorY = 700;
        NewMaterial->GetExpressionCollection().AddExpression(GlowIntensityExpression);

        // STEP 6: Final magical glow with pulsing
        UMaterialExpressionMultiply* MagicalGlowExpression = NewObject<UMaterialExpressionMultiply>(NewMaterial);
        MagicalGlowExpression->A.Expression = MagicalColorExpression;
        MagicalGlowExpression->B.Expression = GlowIntensityExpression;
        MagicalGlowExpression->MaterialExpressionEditorX = -200;
        MagicalGlowExpression->MaterialExpressionEditorY = 700;
        NewMaterial->GetExpressionCollection().AddExpression(MagicalGlowExpression);

        // STEP 7: Add to existing emissive or create new one
        if (MaterialConfig.EmissiveStrength > 0.0f)
        {
            // Add magical glow to existing emissive
            UMaterialExpressionAdd* CombinedEmissiveExpression = NewObject<UMaterialExpressionAdd>(NewMaterial);
            CombinedEmissiveExpression->A.Expression = NewMaterial->GetExpressionInputForProperty(MP_EmissiveColor)->Expression;
            CombinedEmissiveExpression->B.Expression = MagicalGlowExpression;
            CombinedEmissiveExpression->MaterialExpressionEditorX = 0;
            CombinedEmissiveExpression->MaterialExpressionEditorY = 500;
            NewMaterial->GetExpressionCollection().AddExpression(CombinedEmissiveExpression);
            NewMaterial->GetExpressionInputForProperty(MP_EmissiveColor)->Expression = CombinedEmissiveExpression;
        }
        else
        {
            // Use magical glow as primary emissive
            NewMaterial->GetExpressionInputForProperty(MP_EmissiveColor)->Expression = MagicalGlowExpression;
        }

        UE_LOG(LogTemp, Log, TEXT("CreateRobustMaterial: Added ADVANCED pulsing magical glow effect with intensity %.2f"), MaterialConfig.MagicalGlowIntensity);
    }

    // Add transparency for Firmamento ethereal effects
    if (MaterialConfig.Transparency > 0.0f)
    {
        NewMaterial->BlendMode = BLEND_Translucent;
        UMaterialExpressionConstant* OpacityExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
        OpacityExpression->R = 1.0f - MaterialConfig.Transparency;
        OpacityExpression->MaterialExpressionEditorX = -400;
        OpacityExpression->MaterialExpressionEditorY = 900;
        NewMaterial->GetExpressionCollection().AddExpression(OpacityExpression);
        NewMaterial->GetExpressionInputForProperty(MP_Opacity)->Expression = OpacityExpression;
    }

    // Add subsurface scattering for organic/ethereal materials
    if (MaterialConfig.SubsurfaceScattering > 0.0f)
    {
        NewMaterial->SetShadingModel(MSM_Subsurface);
        UMaterialExpressionConstant3Vector* SubsurfaceExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
        SubsurfaceExpression->Constant = FVector3f(MaterialConfig.AccentColor.R, MaterialConfig.AccentColor.G, MaterialConfig.AccentColor.B) * MaterialConfig.SubsurfaceScattering;
        SubsurfaceExpression->MaterialExpressionEditorX = -400;
        SubsurfaceExpression->MaterialExpressionEditorY = 1000;
        NewMaterial->GetExpressionCollection().AddExpression(SubsurfaceExpression);
        NewMaterial->GetExpressionInputForProperty(MP_SubsurfaceColor)->Expression = SubsurfaceExpression;
    }

    // STEP 5: Compile and save material using modern UE 5.6.1 APIs
    NewMaterial->PreEditChange(nullptr);
    NewMaterial->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewMaterial);

    // STEP 6: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to save material to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 7: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Material asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustMaterial: Successfully created and saved material %s (Nanite: %s, Saved: %s, Path: %s)"),
           *MaterialConfig.MaterialName, MaterialConfig.bUseNaniteOverride ? TEXT("Yes") : TEXT("No"),
           bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewMaterial;
}

// ========================================
// AUTOMATIC MATERIAL APPLICATION SYSTEM - MODERN UE 5.6.1
// ========================================

void UUnrealMCPMaterialCommands::ApplyLayerMaterialToActor(AActor* TargetActor, int32 LayerIndex, const FString& ElementType)
{
    if (!TargetActor || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyLayerMaterialToActor: Invalid target actor"));
        return;
    }

    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyLayerMaterialToActor: Must be called from game thread"));
        return;
    }

    // Get or create layer-specific material
    FAuracronLayerMaterialConfig MaterialConfig = GetLayerColorScheme(LayerIndex);
    MaterialConfig.MaterialName = FString::Printf(TEXT("Auracron_%s_Layer%d"), *ElementType, LayerIndex);

    // Check if material already exists in cache
    FString MaterialKey = FString::Printf(TEXT("Layer%d_%s"), LayerIndex, *ElementType);
    UMaterial* LayerMaterial = nullptr;

    if (CreatedMaterials.Contains(MaterialKey))
    {
        LayerMaterial = CreatedMaterials[MaterialKey];
    }
    else
    {
        // Create new material for this layer and element type
        LayerMaterial = CreateRobustMaterial(MaterialConfig);
        if (LayerMaterial)
        {
            CreatedMaterials.Add(MaterialKey, LayerMaterial);
        }
    }

    if (!LayerMaterial || !IsValid(LayerMaterial))
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyLayerMaterialToActor: Failed to get or create material for Layer %d, Element %s"), LayerIndex, *ElementType);
        return;
    }

    // Apply material to all mesh components in the actor
    TArray<UMeshComponent*> MeshComponents;
    TargetActor->GetComponents<UMeshComponent>(MeshComponents);

    int32 ComponentsUpdated = 0;
    for (UMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp && IsValid(MeshComp))
        {
            // Apply material to all material slots
            int32 MaterialCount = MeshComp->GetNumMaterials();
            for (int32 i = 0; i < MaterialCount; i++)
            {
                MeshComp->SetMaterial(i, LayerMaterial);
            }
            ComponentsUpdated++;
        }
    }

    // Also check for hierarchical instanced static mesh components (for vegetation)
    TArray<UHierarchicalInstancedStaticMeshComponent*> HISMComponents;
    TargetActor->GetComponents<UHierarchicalInstancedStaticMeshComponent>(HISMComponents);

    for (UHierarchicalInstancedStaticMeshComponent* HISMComp : HISMComponents)
    {
        if (HISMComp && IsValid(HISMComp))
        {
            int32 MaterialCount = HISMComp->GetNumMaterials();
            for (int32 i = 0; i < MaterialCount; i++)
            {
                HISMComp->SetMaterial(i, LayerMaterial);
            }
            ComponentsUpdated++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("ApplyLayerMaterialToActor: Applied %s material to %d components on actor %s (Layer %d)"),
           *MaterialConfig.MaterialName, ComponentsUpdated, *TargetActor->GetName(), LayerIndex);
}

void UUnrealMCPMaterialCommands::ApplyThematicMaterialsToAllElements(int32 LayerIndex)
{
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyThematicMaterialsToAllElements: Must be called from game thread"));
        return;
    }

    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyThematicMaterialsToAllElements: No valid world context"));
        return;
    }

    int32 ActorsProcessed = 0;

    // Find and apply materials to towers
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        FString ActorName = Actor->GetName();

        // Apply materials based on actor name patterns
        if (ActorName.Contains(TEXT("Tower")) || ActorName.Contains(TEXT("torre")))
        {
            ApplyLayerMaterialToActor(Actor, LayerIndex, TEXT("Tower"));
            ActorsProcessed++;
        }
        else if (ActorName.Contains(TEXT("Wall")) || ActorName.Contains(TEXT("parede")))
        {
            ApplyLayerMaterialToActor(Actor, LayerIndex, TEXT("Wall"));
            ActorsProcessed++;
        }
        else if (ActorName.Contains(TEXT("Camp")) || ActorName.Contains(TEXT("jungle")))
        {
            ApplyLayerMaterialToActor(Actor, LayerIndex, TEXT("Jungle"));
            ActorsProcessed++;
        }
        else if (ActorName.Contains(TEXT("Nexus")) || ActorName.Contains(TEXT("Base")))
        {
            ApplyLayerMaterialToActor(Actor, LayerIndex, TEXT("Nexus"));
            ActorsProcessed++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("ApplyThematicMaterialsToAllElements: Applied thematic materials to %d actors on Layer %d"), ActorsProcessed, LayerIndex);
}

UMaterialInstanceConstant* UUnrealMCPMaterialCommands::CreateNaniteMaterialInstance(UMaterial* BaseMaterial, const FString& InstanceName, int32 LayerIndex)
{
    if (!BaseMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: BaseMaterial is null"));
        return nullptr;
    }

    // STEP 1: Create material instance using modern UE 5.6.1 factory
    UMaterialInstanceConstantFactoryNew* InstanceFactory = NewObject<UMaterialInstanceConstantFactoryNew>();
    if (!InstanceFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create InstanceFactory"));
        return nullptr;
    }

    InstanceFactory->InitialParent = BaseMaterial;

    // STEP 2: Create material instance asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Instances/%s"), LayerIndex, *InstanceName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create package"));
        return nullptr;
    }

    UMaterialInstanceConstant* NewInstance = Cast<UMaterialInstanceConstant>(InstanceFactory->FactoryCreateNew(
        UMaterialInstanceConstant::StaticClass(), Package, FName(*InstanceName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewInstance)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create material instance"));
        return nullptr;
    }

    // STEP 3: Configure instance with layer-specific parameters
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Set scalar parameters
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Metallic")), LayerConfig.Metallic);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Roughness")), LayerConfig.Roughness);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Specular")), LayerConfig.Specular);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("EmissiveStrength")), LayerConfig.EmissiveStrength);

    // Set vector parameters
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("PrimaryColor")), LayerConfig.PrimaryColor);
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("SecondaryColor")), LayerConfig.SecondaryColor);
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("AccentColor")), LayerConfig.AccentColor);

    // STEP 4: Compile and save instance using modern UE 5.6.1 APIs
    NewInstance->PreEditChange(nullptr);
    NewInstance->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewInstance);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to save material instance to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Material instance asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateNaniteMaterialInstance: Successfully created and saved instance %s (Layer: %d, Saved: %s, Path: %s)"),
           *InstanceName, LayerIndex, bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewInstance;
}

UTexture2D* UUnrealMCPMaterialCommands::CreateProceduralTexture(const FString& TextureName, const FString& TextureType, int32 LayerIndex, int32 Resolution)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (TextureName.IsEmpty() || TextureType.IsEmpty() || Resolution <= 0)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Invalid parameters"));
        return nullptr;
    }

    // STEP 3: CREATE REAL TEXTURE ASSET USING MODERN UE 5.6.1 APIs
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Textures/Layer%d/%s/%s"), LayerIndex, *TextureType, *TextureName);

    // Check if texture already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateProceduralTexture: Texture already exists: %s"), *PackagePath);
        return Cast<UTexture2D>(UEditorAssetLibrary::LoadAsset(PackagePath));
    }

    // Create package for texture asset
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 4: USE UTEXTUREFACTORY FOR REAL ASSET CREATION (MODERN UE 5.6.1 API)
    UTextureFactory* TextureFactory = NewObject<UTextureFactory>();
    if (!TextureFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create TextureFactory"));
        return nullptr;
    }

    // Configure texture factory for procedural generation
    TextureFactory->SuppressImportOverwriteDialog();

    // Create real texture asset using factory
    UTexture2D* NewTexture = Cast<UTexture2D>(TextureFactory->CreateTexture2D(Package, FName(*TextureName), RF_Standalone | RF_Public));
    if (!NewTexture)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create texture asset"));
        return nullptr;
    }

    // STEP 5: CONFIGURE TEXTURE PROPERTIES USING MODERN UE 5.6.1 APIs
    NewTexture->Source.Init(Resolution, Resolution, 1, 1, TSF_BGRA8);
    NewTexture->SRGB = (TextureType == TEXT("diffuse")); // Only diffuse textures should be sRGB
    NewTexture->CompressionSettings = (TextureType == TEXT("normal")) ? TC_Normalmap : TC_Default;
    NewTexture->MipGenSettings = TMGS_FromTextureGroup;
    NewTexture->LODGroup = (TextureType == TEXT("diffuse")) ? TEXTUREGROUP_World : TEXTUREGROUP_WorldNormalMap;

    // STEP 6: GENERATE PROCEDURAL TEXTURE DATA
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Lock texture source for writing
    uint8* TextureData = NewTexture->Source.LockMip(0);
    if (!TextureData)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to lock texture source"));
        return nullptr;
    }

    // Generate texture pattern based on type using modern algorithms
    for (int32 Y = 0; Y < Resolution; Y++)
    {
        for (int32 X = 0; X < Resolution; X++)
        {
            int32 Index = (Y * Resolution + X) * 4; // BGRA format
            FColor PixelColor = FColor::White;

            if (TextureType == TEXT("diffuse"))
            {
                // Create advanced gradient pattern with layer colors and noise
                float GradientT = static_cast<float>(Y) / Resolution;
                float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.01f, Y * 0.01f)) * 0.1f;
                GradientT = FMath::Clamp(GradientT + NoiseValue, 0.0f, 1.0f);

                FLinearColor BlendedColor = FMath::Lerp(LayerConfig.PrimaryColor, LayerConfig.SecondaryColor, GradientT);
                PixelColor = BlendedColor.ToFColor(true); // sRGB conversion
            }
            else if (TextureType == TEXT("normal"))
            {
                // Create advanced normal map pattern with height variation
                float HeightValue = FMath::PerlinNoise2D(FVector2D(X * 0.02f, Y * 0.02f));
                FVector Normal = FVector(HeightValue * 0.5f, HeightValue * 0.3f, 1.0f).GetSafeNormal();

                // Convert normal to texture format (0-255 range)
                PixelColor = FColor(
                    static_cast<uint8>((Normal.X + 1.0f) * 127.5f),
                    static_cast<uint8>((Normal.Y + 1.0f) * 127.5f),
                    static_cast<uint8>((Normal.Z + 1.0f) * 127.5f),
                    255
                );
            }
            else if (TextureType == TEXT("roughness"))
            {
                // Create advanced roughness pattern with variation
                float BaseRoughness = LayerConfig.Roughness;
                float RoughnessVariation = FMath::PerlinNoise2D(FVector2D(X * 0.03f, Y * 0.03f)) * 0.2f;
                float FinalRoughness = FMath::Clamp(BaseRoughness + RoughnessVariation, 0.0f, 1.0f);

                uint8 RoughnessValue = static_cast<uint8>(FinalRoughness * 255);
                PixelColor = FColor(RoughnessValue, RoughnessValue, RoughnessValue, 255);
            }
            else if (TextureType == TEXT("metallic"))
            {
                // Create metallic pattern
                float MetallicValue = LayerConfig.Metallic;
                uint8 MetallicByte = static_cast<uint8>(MetallicValue * 255);
                PixelColor = FColor(MetallicByte, MetallicByte, MetallicByte, 255);
            }

            // Write pixel data in BGRA format
            TextureData[Index + 0] = PixelColor.B; // Blue
            TextureData[Index + 1] = PixelColor.G; // Green
            TextureData[Index + 2] = PixelColor.R; // Red
            TextureData[Index + 3] = PixelColor.A; // Alpha
        }
    }

    // Unlock texture source
    NewTexture->Source.UnlockMip(0);

    // STEP 7: FINALIZE TEXTURE USING MODERN UE 5.6.1 APIs
    NewTexture->UpdateResource();
    NewTexture->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewTexture);

    // STEP 8: SALVAMENTO OBRIGATÓRIO NO DISCO
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to save texture to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 9: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Texture asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateProceduralTexture: Successfully created and saved texture %s (Type: %s, Layer: %d, Resolution: %dx%d, Saved: %s)"),
           *TextureName, *TextureType, LayerIndex, Resolution, Resolution, bSaved ? TEXT("Yes") : TEXT("No"));

    return NewTexture;
}

UMaterialFunction* UUnrealMCPMaterialCommands::CreateLayerMaterialFunction(const FString& FunctionName, const FString& FunctionType, int32 LayerIndex)
{
    // STEP 1: Create material function using modern UE 5.6.1 factory
    UMaterialFunctionFactoryNew* FunctionFactory = NewObject<UMaterialFunctionFactoryNew>();
    if (!FunctionFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create FunctionFactory"));
        return nullptr;
    }

    // STEP 2: Create material function asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Functions/%s"), LayerIndex, *FunctionName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create package"));
        return nullptr;
    }

    UMaterialFunction* NewFunction = Cast<UMaterialFunction>(FunctionFactory->FactoryCreateNew(
        UMaterialFunction::StaticClass(), Package, FName(*FunctionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewFunction)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create material function"));
        return nullptr;
    }

    // STEP 3: Configure function based on type - Simplified for now
    if (FunctionType == TEXT("blend"))
    {
        // Create blend function with linear interpolate
        UMaterialExpressionLinearInterpolate* LerpExpression = NewObject<UMaterialExpressionLinearInterpolate>(NewFunction);
        LerpExpression->MaterialExpressionEditorX = 0;
        LerpExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(LerpExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }
    else if (FunctionType == TEXT("noise"))
    {
        // Create noise function (simplified)
        UMaterialExpressionMultiply* NoiseExpression = NewObject<UMaterialExpressionMultiply>(NewFunction);
        NoiseExpression->MaterialExpressionEditorX = 0;
        NoiseExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(NoiseExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }
    else // utility
    {
        // Create utility function with fresnel
        UMaterialExpressionFresnel* FresnelExpression = NewObject<UMaterialExpressionFresnel>(NewFunction);
        FresnelExpression->MaterialExpressionEditorX = 0;
        FresnelExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(FresnelExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }

    // STEP 4: Compile and save function using modern UE 5.6.1 APIs
    NewFunction->PreEditChange(nullptr);
    NewFunction->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewFunction);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to save material function to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Material function asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateLayerMaterialFunction: Successfully created and saved function %s (Type: %s, Layer: %d, Saved: %s, Path: %s)"),
           *FunctionName, *FunctionType, LayerIndex, bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewFunction;
}

UMaterialParameterCollection* UUnrealMCPMaterialCommands::SetupGlobalParameterCollection(const FString& CollectionName, int32 LayerIndex)
{
    // STEP 1: Create parameter collection using modern UE 5.6.1 factory
    UMaterialParameterCollectionFactoryNew* CollectionFactory = NewObject<UMaterialParameterCollectionFactoryNew>();
    if (!CollectionFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create CollectionFactory"));
        return nullptr;
    }

    // STEP 2: Create parameter collection asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Collections/%s"), LayerIndex, *CollectionName);
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath(PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create package"));
        return nullptr;
    }

    UMaterialParameterCollection* NewCollection = Cast<UMaterialParameterCollection>(CollectionFactory->FactoryCreateNew(
        UMaterialParameterCollection::StaticClass(), Package, FName(*CollectionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewCollection)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create parameter collection"));
        return nullptr;
    }

    // STEP 3: Add layer-specific parameters
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Add scalar parameters
    FCollectionScalarParameter TimeParam;
    TimeParam.ParameterName = TEXT("GlobalTime");
    NewCollection->ScalarParameters.Add(TimeParam);

    FCollectionScalarParameter IntensityParam;
    IntensityParam.ParameterName = TEXT("LayerIntensity");
    NewCollection->ScalarParameters.Add(IntensityParam);

    // Add vector parameters
    FCollectionVectorParameter PrimaryColorParam;
    PrimaryColorParam.ParameterName = TEXT("LayerPrimaryColor");
    PrimaryColorParam.DefaultValue = LayerConfig.PrimaryColor;
    NewCollection->VectorParameters.Add(PrimaryColorParam);

    FCollectionVectorParameter SecondaryColorParam;
    SecondaryColorParam.ParameterName = TEXT("LayerSecondaryColor");
    SecondaryColorParam.DefaultValue = LayerConfig.SecondaryColor;
    NewCollection->VectorParameters.Add(SecondaryColorParam);

    // STEP 4: Compile and save collection using modern UE 5.6.1 APIs
    NewCollection->PreEditChange(nullptr);
    NewCollection->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewCollection);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to save parameter collection to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Parameter collection asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("SetupGlobalParameterCollection: Successfully created and saved collection %s (Layer: %d, Scalars: %d, Vectors: %d, Saved: %s, Path: %s)"),
           *CollectionName, LayerIndex, NewCollection->ScalarParameters.Num(), NewCollection->VectorParameters.Num(),
           bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewCollection;
}

FAuracronLayerMaterialConfig UUnrealMCPMaterialCommands::GetLayerColorScheme(int32 LayerIndex)
{
    FAuracronLayerMaterialConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - RICH GOLDEN THEME with natural elements
            // Primary: Rich golden with warm undertones
            Config.PrimaryColor = FLinearColor(1.0f, 0.843f, 0.0f, 1.0f);      // Pure Gold
            Config.SecondaryColor = FLinearColor(0.855f, 0.647f, 0.125f, 1.0f); // Dark Goldenrod
            Config.AccentColor = FLinearColor(1.0f, 0.980f, 0.804f, 1.0f);      // Light Goldenrod Yellow
            Config.TertiaryColor = FLinearColor(0.196f, 0.804f, 0.196f, 1.0f);  // Lime Green (nature)

            // Material properties for golden surfaces
            Config.Metallic = 0.7f;        // High metallic for golden shine
            Config.Roughness = 0.2f;       // Low roughness for polished gold
            Config.Specular = 0.9f;        // High specular for reflectivity
            Config.EmissiveStrength = 0.3f; // Subtle glow
            Config.bUseNaniteOverride = true; // Enable Nanite for detailed geometry

            // Weathering and detail properties
            Config.WeatheringIntensity = 0.2f; // Light weathering for ancient gold
            Config.DetailNormalStrength = 0.8f; // Strong detail normals
            Config.SubsurfaceScattering = 0.1f; // Subtle SSS for organic feel
            break;

        case 1: // Firmamento Zephyr - ETHEREAL CRYSTALLINE THEME
            // Primary: Ethereal blues with crystalline properties
            Config.PrimaryColor = FLinearColor(0.529f, 0.808f, 0.980f, 1.0f);   // Sky Blue
            Config.SecondaryColor = FLinearColor(0.690f, 0.878f, 0.902f, 1.0f); // Light Sky Blue
            Config.AccentColor = FLinearColor(0.941f, 0.973f, 1.0f, 1.0f);      // Alice Blue
            Config.TertiaryColor = FLinearColor(0.863f, 0.863f, 0.863f, 1.0f);  // Gainsboro (cloud-like)

            // Material properties for ethereal/crystalline surfaces
            Config.Metallic = 0.1f;        // Low metallic for non-metallic crystals
            Config.Roughness = 0.1f;       // Very low roughness for crystal clarity
            Config.Specular = 1.0f;        // Maximum specular for crystal reflections
            Config.EmissiveStrength = 0.5f; // Moderate glow for ethereal effect
            Config.bUseNaniteOverride = true; // Nanite for detailed crystal geometry

            // Ethereal properties
            Config.WeatheringIntensity = 0.0f; // No weathering for pristine crystals
            Config.DetailNormalStrength = 0.6f; // Moderate normals for smooth crystals
            Config.SubsurfaceScattering = 0.4f; // High SSS for translucent crystals
            Config.Transparency = 0.2f;    // Slight transparency for ethereal effect
            break;

        case 2: // Abismo Umbral - DARK MYSTICAL THEME with shadow magic
            // Primary: Deep purples and shadow blacks with mystical energy
            Config.PrimaryColor = FLinearColor(0.294f, 0.0f, 0.510f, 1.0f);     // Indigo
            Config.SecondaryColor = FLinearColor(0.098f, 0.098f, 0.439f, 1.0f); // Dark Slate Blue
            Config.AccentColor = FLinearColor(0.627f, 0.125f, 0.941f, 1.0f);    // Blue Violet
            Config.TertiaryColor = FLinearColor(0.184f, 0.184f, 0.184f, 1.0f);  // Dark Gray (shadow)

            // Material properties for dark mystical surfaces
            Config.Metallic = 0.9f;        // High metallic for dark metals
            Config.Roughness = 0.6f;       // Moderate roughness for worn dark surfaces
            Config.Specular = 0.7f;        // Good specular for mystical reflections
            Config.EmissiveStrength = 1.2f; // Strong glow for magical energy
            Config.bUseNaniteOverride = true; // Nanite for detailed dark geometry

            // Mystical properties
            Config.WeatheringIntensity = 0.8f; // Heavy weathering for ancient darkness
            Config.DetailNormalStrength = 1.0f; // Maximum detail for rough surfaces
            Config.SubsurfaceScattering = 0.0f; // No SSS for opaque darkness
            Config.MagicalGlowIntensity = 1.5f; // Strong magical glow
            break;

        default: // Fallback neutral theme
            Config.PrimaryColor = FLinearColor(0.5f, 0.5f, 0.5f, 1.0f);    // Medium Gray
            Config.SecondaryColor = FLinearColor(0.3f, 0.3f, 0.3f, 1.0f);  // Dark Gray
            Config.AccentColor = FLinearColor(0.7f, 0.7f, 0.7f, 1.0f);     // Light Gray
            Config.TertiaryColor = FLinearColor(0.1f, 0.1f, 0.1f, 1.0f);   // Very Dark Gray

            Config.Metallic = 0.5f;
            Config.Roughness = 0.5f;
            Config.Specular = 0.5f;
            Config.EmissiveStrength = 0.0f;
            Config.WeatheringIntensity = 0.3f;
            Config.DetailNormalStrength = 0.5f;
            break;
    }

    // Set common properties
    Config.MaterialName = FString::Printf(TEXT("AuracronLayer%d_Material"), LayerIndex);
    Config.bCreateMaterialInstance = true;
    Config.bGenerateProceduralTextures = true;

    UE_LOG(LogTemp, Log, TEXT("GetLayerColorScheme: Generated rich theme for Layer %d - Primary: %s, Metallic: %.2f, Emissive: %.2f"),
           LayerIndex, *Config.PrimaryColor.ToString(), Config.Metallic, Config.EmissiveStrength);

    return Config;
}

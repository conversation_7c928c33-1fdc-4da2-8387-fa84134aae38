#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 DynamicMesh APIs
#include "DynamicMesh/DynamicMesh3.h"
#include "Components/DynamicMeshComponent.h"
#include "UDynamicMesh.h"

// Modern UE 5.6.1 MeshDescription APIs
#include "MeshDescription.h"
#include "MeshDescriptionBuilder.h"

// Experimental UE 5.6.1 GeometryFlow APIs - Forward declarations to avoid header issues
// Note: GeometryFlow includes will be added in implementation file
namespace UE { namespace GeometryFlow { class FGraph; } }

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstanceDynamic.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "UnrealMCPProceduralMeshCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 PROCEDURAL MESH STRUCTURES
// ========================================

/**
 * Auracron procedural mesh configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronProceduralMeshConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString MeshName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector MeshScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector MeshLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FRotator MeshRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseDynamicMesh = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseGeometryFlow = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector> Vertices;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<int32> Triangles;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector> Normals;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector2D> UVs;

    FAuracronProceduralMeshConfig()
    {
        MeshName = TEXT("AuracronMesh");
        LayerIndex = 0;
        MeshScale = FVector(1.0f, 1.0f, 1.0f);
        MeshLocation = FVector::ZeroVector;
        MeshRotation = FRotator::ZeroRotator;
        bUseDynamicMesh = true;
        bUseGeometryFlow = true;
    }
};

/**
 * Lane geometry generation parameters
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FLaneGeometryParams
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString LaneName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector> LanePoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float LaneWidth = 500.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float LaneHeight = 50.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bGenerateCollision = true;

    FLaneGeometryParams()
    {
        LaneName = TEXT("AuracronLane");
        LaneWidth = 500.0f;
        LaneHeight = 50.0f;
        LayerIndex = 0;
        bGenerateCollision = true;
    }
};

/**
 * UnrealMCP Procedural Mesh Commands - Modern UE 5.6.1 Implementation
 * Handles procedural mesh creation with Auracron-specific features
 */
UCLASS()
class UNREALMCP_API UUnrealMCPProceduralMeshCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle procedural mesh command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create lane geometry using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "lane_name" - Name of the lane
     *                "lane_points" - Array of lane control points
     *                "lane_width" - Width of the lane
     *                "layer_index" - Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     * @return JSON response with the created lane geometry details
     */
    TSharedPtr<FJsonObject> HandleCreateLaneGeometry(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create jungle structures using procedural generation
     * @param Params - Must include:
     *                "structure_type" - Type of jungle structure
     *                "location" - Structure location
     *                "layer_index" - Layer index
     *                "complexity" - Structure complexity level
     * @return JSON response with jungle structure creation results
     */
    TSharedPtr<FJsonObject> HandleCreateJungleStructures(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create tower meshes with layer-specific designs
     * @param Params - Must include:
     *                "tower_type" - Type of tower (basic, advanced, nexus)
     *                "location" - Tower location
     *                "layer_index" - Layer index
     *                "team_index" - Team index for styling
     * @return JSON response with tower mesh creation results
     */
    TSharedPtr<FJsonObject> HandleCreateTowerMeshes(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create base architecture using advanced procedural algorithms
     * @param Params - Must include:
     *                "architecture_type" - Type of base architecture
     *                "location" - Architecture location
     *                "layer_index" - Layer index
     *                "scale" - Architecture scale
     * @return JSON response with base architecture creation results
     */
    TSharedPtr<FJsonObject> HandleCreateBaseArchitecture(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create portal geometry with dimensional effects
     * @param Params - Must include:
     *                "portal_name" - Portal name
     *                "source_layer" - Source layer index
     *                "target_layer" - Target layer index
     *                "location" - Portal location
     * @return JSON response with portal geometry creation results
     */
    TSharedPtr<FJsonObject> HandleCreatePortalGeometry(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create bridge meshes for dimensional bridges
     * @param Params - Must include:
     *                "bridge_name" - Bridge name
     *                "start_point" - Bridge start point
     *                "end_point" - Bridge end point
     *                "bridge_type" - Type of bridge
     * @return JSON response with bridge mesh creation results
     */
    TSharedPtr<FJsonObject> HandleCreateBridgeMeshes(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust procedural mesh using modern UE 5.6.1 DynamicMesh APIs
     * @param MeshConfig Procedural mesh configuration
     * @return Created DynamicMeshComponent
     */
    UDynamicMeshComponent* CreateRobustProceduralMesh(const FAuracronProceduralMeshConfig& MeshConfig);

    /**
     * Create real Static Mesh asset saved to disk using modern UE 5.6.1 APIs
     * @param MeshConfig Procedural mesh configuration
     * @return Created StaticMesh asset saved to disk
     */
    UStaticMesh* CreateRealStaticMeshAsset(const FAuracronProceduralMeshConfig& MeshConfig);

    /**
     * Generate default cube mesh with proper normals and UVs
     * @param Vertices Output vertices array
     * @param Triangles Output triangles array
     * @param Normals Output normals array
     * @param UVs Output UVs array
     * @param Scale Cube scale
     */
    void GenerateDefaultCubeMesh(TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs, const FVector& Scale);

    /**
     * Generate normals for mesh vertices
     * @param Vertices Input vertices array
     * @param Triangles Input triangles array
     * @param Normals Output normals array
     */
    void GenerateNormalsForMesh(const TArray<FVector>& Vertices, const TArray<int32>& Triangles, TArray<FVector>& Normals);

    /**
     * Generate UV coordinates for mesh vertices
     * @param Vertices Input vertices array
     * @param UVs Output UVs array
     */
    void GenerateUVsForMesh(const TArray<FVector>& Vertices, TArray<FVector2D>& UVs);

    /**
     * Populate MeshDescription with vertex data using modern UE 5.6.1 APIs
     * @param MeshDescription Target mesh description
     * @param Vertices Input vertices array
     * @param Triangles Input triangles array
     * @param Normals Input normals array
     * @param UVs Input UVs array
     */
    void PopulateMeshDescription(FMeshDescription& MeshDescription, const TArray<FVector>& Vertices, const TArray<int32>& Triangles, const TArray<FVector>& Normals, const TArray<FVector2D>& UVs);

    /**
     * Generate lane geometry using GeometryFlow experimental APIs
     * @param LaneParams Lane geometry parameters
     * @return Generated DynamicMesh3
     */
    TSharedPtr<FDynamicMesh3> GenerateLaneGeometryWithFlow(const FLaneGeometryParams& LaneParams);

    /**
     * Create complex jungle structure using advanced algorithms
     * @param StructureType Type of structure
     * @param Location Structure location
     * @param LayerIndex Layer index
     * @return Created mesh component
     */
    UDynamicMeshComponent* CreateComplexJungleStructure(const FString& StructureType, const FVector& Location, int32 LayerIndex);

    /**
     * Generate tower mesh with layer-specific design
     * @param TowerType Type of tower
     * @param LayerIndex Layer index
     * @param TeamIndex Team index
     * @return Generated tower mesh
     */
    TSharedPtr<FDynamicMesh3> GenerateLayerSpecificTowerMesh(const FString& TowerType, int32 LayerIndex, int32 TeamIndex);

    /**
     * Create architectural structure using modern mesh building
     * @param ArchType Architecture type
     * @param Scale Structure scale
     * @param LayerIndex Layer index
     * @return Created architecture mesh
     */
    UDynamicMeshComponent* CreateArchitecturalStructure(const FString& ArchType, const FVector& Scale, int32 LayerIndex);

    /**
     * Generate portal geometry with dimensional effects
     * @param SourceLayer Source layer index
     * @param TargetLayer Target layer index
     * @return Generated portal mesh
     */
    TSharedPtr<FDynamicMesh3> GeneratePortalGeometry(int32 SourceLayer, int32 TargetLayer);

private:
    // Cache for created meshes
    UPROPERTY()
    TMap<FString, TObjectPtr<UDynamicMeshComponent>> CreatedMeshes;

    // GeometryFlow graphs cache - Using forward declaration to avoid header issues
    TMap<FString, void*> GeometryFlowGraphs;

    // Material instances cache
    UPROPERTY()
    TMap<int32, TObjectPtr<UMaterialInstanceDynamic>> LayerMaterials;
};

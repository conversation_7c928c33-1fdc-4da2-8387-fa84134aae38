// WorldPartitionFixSubsystem.h - Header do subsistema para aplicação automática da correção

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/TimerHandle.h"
#include "WorldPartitionFixSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogWorldPartitionFixSubsystem, Log, All);

class UWorldPartition;

/**
 * Subsistema responsável por aplicar automaticamente a correção do World Partition
 * Garante que todos os mundos tenham a correção aplicada automaticamente
 */
UCLASS()
class AURACRON_API UWorldPartitionFixSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    /**
     * Aplica a correção do World Partition no mundo atual
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void ApplyWorldPartitionFix();

    /**
     * Verifica se o World Partition está em estado saudável
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    bool IsWorldPartitionHealthy();

    /**
     * Força a correção de todos os World Partitions ativos
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Fix")
    void ForceFixAllWorldPartitions();

protected:
    /**
     * Cria um ator invisível com o componente de correção
     */
    void CreateWorldPartitionFixActor();

    /**
     * Aplica correção específica ao World Partition
     */
    void FixWorldPartitionState(UWorldPartition* WorldPartition);

    /**
     * Verificação periódica do estado
     */
    void PeriodicVerification();

private:
    /** Handle do timer para verificações periódicas */
    FTimerHandle VerificationTimerHandle;

    /** Intervalo de verificação em segundos */
    UPROPERTY(EditAnywhere, Category = "World Partition Fix")
    float VerificationInterval = 5.0f;

    /** Habilitar verificação periódica */
    UPROPERTY(EditAnywhere, Category = "World Partition Fix")
    bool bEnablePeriodicVerification = true;
};
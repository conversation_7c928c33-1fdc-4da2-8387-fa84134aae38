[/Script/UnrealEd.EditorEngine]
bUseWorldPartition=True
bEnableWorldPartitionStreaming=True
bEnableWorldPartitionHLODs=True
bDisableWorldPartitionLoadingInEditor=False
bEnableWorldPartitionRuntimeHash=True
bEnableWorldPartitionLevelStreaming=True
bAutoSaveWorldPartition=True
bValidateWorldPartitionOnSave=True

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
bEnableWorldPartitionStreaming=True
bEnableWorldPartitionHLODs=True
bDisableWorldPartitionLoadingInEditor=False
bEnableWorldPartitionRuntimeHash=True
bEnableWorldPartitionLevelStreaming=True
bAutoSaveWorldPartition=True
bValidateWorldPartitionOnSave=True
bEnableWorldPartitionMiniMap=True
bShowWorldPartitionEditor=True
bEnableWorldPartitionActorDesc=True
bEnableWorldPartitionDataLayers=True
bEnableWorldPartitionExternalObjects=True
bEnableWorldPartitionOneFilePerActor=True
bEnableWorldPartitionFoliageSupport=True
bEnableWorldPartitionLandscapeSupport=True
bEnableWorldPartitionStaticMeshSupport=True
bEnableWorldPartitionBlueprintSupport=True

[/Script/Engine.EditorEngine]
bUseWorldPartition=True
bEnableWorldPartitionStreaming=True
bEnableWorldPartitionHLODs=True
bDisableWorldPartitionLoadingInEditor=False
bEnableWorldPartitionRuntimeHash=True
bEnableWorldPartitionLevelStreaming=True
bAutoSaveWorldPartition=True
bValidateWorldPartitionOnSave=True
bEnableWorldPartitionMiniMap=True
bShowWorldPartitionEditor=True
bEnableWorldPartitionActorDesc=True
bEnableWorldPartitionDataLayers=True
bEnableWorldPartitionExternalObjects=True
bEnableWorldPartitionOneFilePerActor=True

[/Script/UnrealEd.LevelEditorViewportSettings]
bEnableWorldPartitionVisualization=True
bShowWorldPartitionStreamingBounds=True
bShowWorldPartitionDataLayers=True
bShowWorldPartitionHLODs=True
bShowWorldPartitionActorDescs=True

[/Script/UnrealEd.EditorPerProjectUserSettings]
bEnableWorldPartitionStreaming=True
bEnableWorldPartitionHLODs=True
bShowWorldPartitionEditor=True
bEnableWorldPartitionMiniMap=True
bEnableWorldPartitionDataLayers=True
bEnableWorldPartitionExternalObjects=True
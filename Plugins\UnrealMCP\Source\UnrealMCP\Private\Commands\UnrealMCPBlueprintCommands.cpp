#include "Commands/UnrealMCPBlueprintCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "UObject/UObjectGlobals.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SceneComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"
#include "UObject/Field.h"
#include "UObject/FieldPath.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"

// Modern UE 5.6.1 Blueprint APIs
#include "K2Node_FunctionEntry.h"
#include "K2Node_FunctionResult.h"
#include "K2Node_CallFunction.h"
#include "K2Node_ComponentBoundEvent.h"
#include "K2Node_InputAction.h"
#include "K2Node_InputKey.h"
#include "K2Node_CustomEvent.h"

// Advanced Blueprint Compilation APIs - UE 5.6.1
#include "KismetCompiler.h"
#include "KismetCompilerModule.h"
#include "BlueprintCompiledStatement.h"
#include "BPTerminal.h"
#include "KismetCompiledFunctionContext.h"
#include "KismetCastingUtils.h"
#include "EdGraphCompilerUtilities.h"

// Blueprint Editor APIs - UE 5.6.1
#include "BlueprintEditorModule.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "BlueprintEditorSettings.h"

// Advanced Blueprint Graph APIs - UE 5.6.1
#include "BlueprintActionDatabase.h"
#include "BlueprintActionDatabaseRegistrar.h"
#include "BlueprintActionFilter.h"
#include "BlueprintNodeSpawner.h"
#include "BlueprintNodeSignature.h"
#include "BlueprintGraphDefinitions.h"
#include "BlueprintGraphModule.h"
#include "BlueprintGraphPanelPinFactory.h"

// Modern UE 5.6.1 Performance APIs for Blueprints
#include "HAL/PlatformApplicationMisc.h"
#include "Async/TaskGraphInterfaces.h"
#include "ProfilingDebugging/CsvProfiler.h"

FUnrealMCPBlueprintCommands::FUnrealMCPBlueprintCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPBlueprintCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Blueprint command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPBlueprintCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command type
    if (CommandType.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPBlueprintCommands::HandleCommand - Empty command type"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command type provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPBlueprintCommands::HandleCommand - Processing: %s"), *CommandType);

    if (CommandType == TEXT("create_blueprint"))
    {
        return HandleCreateBlueprint(Params);
    }
    else if (CommandType == TEXT("add_component_to_blueprint"))
    {
        return HandleAddComponentToBlueprint(Params);
    }
    else if (CommandType == TEXT("set_component_property"))
    {
        return HandleSetComponentProperty(Params);
    }
    else if (CommandType == TEXT("set_physics_properties"))
    {
        return HandleSetPhysicsProperties(Params);
    }
    else if (CommandType == TEXT("compile_blueprint"))
    {
        return HandleCompileBlueprint(Params);
    }
    else if (CommandType == TEXT("spawn_blueprint_actor"))
    {
        return HandleSpawnBlueprintActor(Params);
    }
    else if (CommandType == TEXT("set_blueprint_property"))
    {
        return HandleSetBlueprintProperty(Params);
    }
    else if (CommandType == TEXT("set_static_mesh_properties"))
    {
        return HandleSetStaticMeshProperties(Params);
    }
    else if (CommandType == TEXT("set_pawn_properties"))
    {
        return HandleSetPawnProperties(Params);
    }
    else if (CommandType == TEXT("create_auracron_multilayer_blueprint"))
    {
        return HandleCreateAuracronMultilayerBlueprint(Params);
    }
    else if (CommandType == TEXT("create_auracron_portal_blueprint"))
    {
        return HandleCreateAuracronPortalBlueprint(Params);
    }
    else if (CommandType == TEXT("create_auracron_tower_blueprint"))
    {
        return HandleCreateAuracronTowerBlueprint(Params);
    }
    else if (CommandType == TEXT("create_auracron_objective_blueprint"))
    {
        return HandleCreateAuracronObjectiveBlueprint(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown blueprint command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateBlueprint - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("HandleCreateBlueprint must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateBlueprint - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // Get required parameters with enhanced validation
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("name"), BlueprintName) || BlueprintName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateBlueprint - Missing or empty 'name' parameter"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'name' parameter"));
    }

    // Check if blueprint already exists
    FString PackagePath = TEXT("/Game/Blueprints/");
    FString AssetName = BlueprintName;
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory using modern UE 5.6.1 APIs
    UBlueprintFactory* Factory = NewObject<UBlueprintFactory>();
    if (!Factory || !IsValid(Factory))
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateBlueprint - Failed to create blueprint factory - memory leak prevented"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create blueprint factory"));
    }

    // Handle parent class with enhanced validation
    FString ParentClass;
    Params->TryGetStringField(TEXT("parent_class"), ParentClass);

    // Default to Actor if no parent class specified
    UClass* SelectedParentClass = AActor::StaticClass();

    // MEMORY LEAK PREVENTION - Validate parent class
    if (!SelectedParentClass || !IsValid(SelectedParentClass))
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateBlueprint - Invalid parent class - memory leak prevented"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parent class"));
    }
    
    // Try to find the specified parent class
    if (!ParentClass.IsEmpty())
    {
        FString ClassName = ParentClass;
        if (!ClassName.StartsWith(TEXT("A")))
        {
            ClassName = TEXT("A") + ClassName;
        }
        
        // First try direct StaticClass lookup for common classes
        UClass* FoundClass = nullptr;
        if (ClassName == TEXT("APawn"))
        {
            FoundClass = APawn::StaticClass();
        }
        else if (ClassName == TEXT("AActor"))
        {
            FoundClass = AActor::StaticClass();
        }
        else
        {
            // Try loading the class using LoadClass which is more reliable than FindObject
            const FString ClassPath = FString::Printf(TEXT("/Script/Engine.%s"), *ClassName);
            FoundClass = LoadClass<AActor>(nullptr, *ClassPath);
            
            if (!FoundClass)
            {
                // Try alternate paths if not found
                const FString GameClassPath = FString::Printf(TEXT("/Script/Game.%s"), *ClassName);
                FoundClass = LoadClass<AActor>(nullptr, *GameClassPath);
            }
        }

        if (FoundClass)
        {
            SelectedParentClass = FoundClass;
            UE_LOG(LogTemp, Log, TEXT("Successfully set parent class to '%s'"), *ClassName);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Could not find specified parent class '%s' at paths: /Script/Engine.%s or /Script/Game.%s, defaulting to AActor"), 
                *ClassName, *ClassName, *ClassName);
        }
    }
    
    Factory->ParentClass = SelectedParentClass;

    // Create the blueprint
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath((PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewBlueprint)
    {
        // Notify the asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        // Mark the package dirty
        Package->MarkPackageDirty();

        // CRITICAL FIX: Save the Blueprint to disk
        FString BlueprintPath = PackagePath + AssetName;
        bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("name"), AssetName);
        ResultObj->SetStringField(TEXT("path"), BlueprintPath);
        ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
        ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Blueprints/") + AssetName + TEXT(".uasset"));

        UE_LOG(LogTemp, Log, TEXT("Blueprint created and saved: %s (Saved: %s)"), *BlueprintPath, bSaved ? TEXT("Yes") : TEXT("No"));
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleAddComponentToBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentType;
    if (!Params->TryGetStringField(TEXT("component_type"), ComponentType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'type' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Create the component - dynamically find the component class by name
    UClass* ComponentClass = nullptr;

    // Try to find the class with exact name first
    ComponentClass = StaticLoadClass(UActorComponent::StaticClass(), nullptr, *ComponentType);

    // If not found, try with "Component" suffix
    if (!ComponentClass && !ComponentType.EndsWith(TEXT("Component")))
    {
        FString ComponentTypeWithSuffix = ComponentType + TEXT("Component");
        ComponentClass = StaticLoadClass(UActorComponent::StaticClass(), nullptr, *ComponentTypeWithSuffix);
    }

    // If still not found, try with "U" prefix
    if (!ComponentClass && !ComponentType.StartsWith(TEXT("U")))
    {
        FString ComponentTypeWithPrefix = TEXT("U") + ComponentType;
        ComponentClass = StaticLoadClass(UActorComponent::StaticClass(), nullptr, *ComponentTypeWithPrefix);

        // Try with both prefix and suffix
        if (!ComponentClass && !ComponentType.EndsWith(TEXT("Component")))
        {
            FString ComponentTypeWithBoth = TEXT("U") + ComponentType + TEXT("Component");
            ComponentClass = StaticLoadClass(UActorComponent::StaticClass(), nullptr, *ComponentTypeWithBoth);
        }
    }
    
    // Verify that the class is a valid component type
    if (!ComponentClass || !ComponentClass->IsChildOf(UActorComponent::StaticClass()))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown component type: %s"), *ComponentType));
    }

    // Add the component to the blueprint
    USCS_Node* NewNode = Blueprint->SimpleConstructionScript->CreateNode(ComponentClass, *ComponentName);
    if (NewNode)
    {
        // Set transform if provided
        USceneComponent* SceneComponent = Cast<USceneComponent>(NewNode->ComponentTemplate);
        if (SceneComponent)
        {
            if (Params->HasField(TEXT("location")))
            {
                SceneComponent->SetRelativeLocation(FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location")));
            }
            if (Params->HasField(TEXT("rotation")))
            {
                SceneComponent->SetRelativeRotation(FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation")));
            }
            if (Params->HasField(TEXT("scale")))
            {
                SceneComponent->SetRelativeScale3D(FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale")));
            }
        }

        // Add to root if no parent specified
        Blueprint->SimpleConstructionScript->AddNode(NewNode);

        // Compile the blueprint
        FKismetEditorUtilities::CompileBlueprint(Blueprint);

        // CRITICAL FIX: Save the Blueprint to disk after adding component
        FString BlueprintPath = TEXT("/Game/Blueprints/") + BlueprintName;
        bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("component_name"), ComponentName);
        ResultObj->SetStringField(TEXT("component_type"), ComponentType);
        ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

        UE_LOG(LogTemp, Log, TEXT("Component added to blueprint and saved: %s (Saved: %s)"), *BlueprintPath, bSaved ? TEXT("Yes") : TEXT("No"));
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to add component to blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetComponentProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    FString PropertyName;
    if (!Params->TryGetStringField(TEXT("property_name"), PropertyName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_name' parameter"));
    }

    // Log all input parameters for debugging
    UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Blueprint: %s, Component: %s, Property: %s"), 
        *BlueprintName, *ComponentName, *PropertyName);
    
    // Log property_value if available
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        FString ValueType;
        
        switch(JsonValue->Type)
        {
            case EJson::Boolean: ValueType = FString::Printf(TEXT("Boolean: %s"), JsonValue->AsBool() ? TEXT("true") : TEXT("false")); break;
            case EJson::Number: ValueType = FString::Printf(TEXT("Number: %f"), JsonValue->AsNumber()); break;
            case EJson::String: ValueType = FString::Printf(TEXT("String: %s"), *JsonValue->AsString()); break;
            case EJson::Array: ValueType = TEXT("Array"); break;
            case EJson::Object: ValueType = TEXT("Object"); break;
            default: ValueType = TEXT("Unknown"); break;
        }
        
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Value Type: %s"), *ValueType);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - No property_value provided"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Blueprint not found: %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Blueprint found: %s (Class: %s)"), 
            *BlueprintName, 
            Blueprint->GeneratedClass ? *Blueprint->GeneratedClass->GetName() : TEXT("NULL"));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Searching for component %s in blueprint nodes"), *ComponentName);
    
    if (!Blueprint->SimpleConstructionScript)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - SimpleConstructionScript is NULL for blueprint %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid blueprint construction script"));
    }
    
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node)
        {
            UE_LOG(LogTemp, Verbose, TEXT("SetComponentProperty - Found node: %s"), *Node->GetVariableName().ToString());
            if (Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Found NULL node in blueprint"));
        }
    }

    if (!ComponentNode)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Component not found: %s"), *ComponentName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Component found: %s (Class: %s)"), 
            *ComponentName, 
            ComponentNode->ComponentTemplate ? *ComponentNode->ComponentTemplate->GetClass()->GetName() : TEXT("NULL"));
    }

    // Get the component template
    UObject* ComponentTemplate = ComponentNode->ComponentTemplate;
    if (!ComponentTemplate)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Component template is NULL for %s"), *ComponentName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid component template"));
    }

    // Check if this is a Spring Arm component and log special debug info
    if (ComponentTemplate->GetClass()->GetName().Contains(TEXT("SpringArm")))
    {
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - SpringArm component detected! Class: %s"), 
            *ComponentTemplate->GetClass()->GetPathName());
            
        // Log all properties of the SpringArm component class
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - SpringArm properties:"));
        for (TFieldIterator<FProperty> PropIt(ComponentTemplate->GetClass()); PropIt; ++PropIt)
        {
            FProperty* Prop = *PropIt;
            UE_LOG(LogTemp, Warning, TEXT("  - %s (%s)"), *Prop->GetName(), *Prop->GetCPPType());
        }

        // Special handling for Spring Arm properties
        if (Params->HasField(TEXT("property_value")))
        {
            TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
            
            // Get the property using the new FField system
            FProperty* Property = FindFProperty<FProperty>(ComponentTemplate->GetClass(), *PropertyName);
            if (!Property)
            {
                UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Property %s not found on SpringArm component"), *PropertyName);
                return FUnrealMCPCommonUtils::CreateErrorResponse(
                    FString::Printf(TEXT("Property %s not found on SpringArm component"), *PropertyName));
            }

            // Create a scope guard to ensure property cleanup
            struct FScopeGuard
            {
                UObject* Object;
                FScopeGuard(UObject* InObject) : Object(InObject) 
                {
                    if (Object)
                    {
                        Object->Modify();
                    }
                }
                ~FScopeGuard()
                {
                    if (Object)
                    {
                        Object->PostEditChange();
                    }
                }
            } ScopeGuard(ComponentTemplate);

            bool bSuccess = false;
            FString ErrorMessage;

            // Handle specific Spring Arm property types
            if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
            {
                if (JsonValue->Type == EJson::Number)
                {
                    const float Value = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting float property %s to %f"), *PropertyName, Value);
                    FloatProp->SetPropertyValue_InContainer(ComponentTemplate, Value);
                    bSuccess = true;
                }
            }
            else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
            {
                if (JsonValue->Type == EJson::Boolean)
                {
                    const bool Value = JsonValue->AsBool();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting bool property %s to %d"), *PropertyName, Value);
                    BoolProp->SetPropertyValue_InContainer(ComponentTemplate, Value);
                    bSuccess = true;
                }
            }
            else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
            {
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Handling struct property %s of type %s"), 
                    *PropertyName, *StructProp->Struct->GetName());
                
                // Special handling for common Spring Arm struct properties
                if (StructProp->Struct == TBaseStructure<FVector>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FVector Vec(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            StructProp->CopySingleValue(PropertyAddr, &Vec);
                            bSuccess = true;
                        }
                    }
                }
                else if (StructProp->Struct == TBaseStructure<FRotator>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FRotator Rot(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            StructProp->CopySingleValue(PropertyAddr, &Rot);
                            bSuccess = true;
                        }
                    }
                }
            }

            if (bSuccess)
            {
                // Mark the blueprint as modified
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Successfully set SpringArm property %s"), *PropertyName);
                FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

                // CRITICAL FIX: Save the Blueprint to disk after setting property
                FString BlueprintPath = TEXT("/Game/Blueprints/") + BlueprintName;
                bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

                TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
                ResultObj->SetStringField(TEXT("component"), ComponentName);
                ResultObj->SetStringField(TEXT("property"), PropertyName);
                ResultObj->SetBoolField(TEXT("success"), true);
                ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

                UE_LOG(LogTemp, Log, TEXT("Component property set and blueprint saved: %s (Saved: %s)"), *BlueprintPath, bSaved ? TEXT("Yes") : TEXT("No"));
                return ResultObj;
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set SpringArm property %s"), *PropertyName);
                return FUnrealMCPCommonUtils::CreateErrorResponse(
                    FString::Printf(TEXT("Failed to set SpringArm property %s"), *PropertyName));
            }
        }
    }

    // Regular property handling for non-Spring Arm components continues...

    // Set the property value
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        
        // Get the property
        FProperty* Property = FindFProperty<FProperty>(ComponentTemplate->GetClass(), *PropertyName);
        if (!Property)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Property %s not found on component %s"), 
                *PropertyName, *ComponentName);
            
            // List all available properties for this component
            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Available properties for %s:"), *ComponentName);
            for (TFieldIterator<FProperty> PropIt(ComponentTemplate->GetClass()); PropIt; ++PropIt)
            {
                FProperty* Prop = *PropIt;
                UE_LOG(LogTemp, Warning, TEXT("  - %s (%s)"), *Prop->GetName(), *Prop->GetCPPType());
            }
            
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Property %s not found on component %s"), *PropertyName, *ComponentName));
        }
        else
        {
            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property found: %s (Type: %s)"), 
                *PropertyName, *Property->GetCPPType());
        }

        bool bSuccess = false;
        FString ErrorMessage;

        // Handle different property types
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Attempting to set property %s"), *PropertyName);
        
        // Add try-catch block to catch and log any crashes
        try
        {
            if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
            {
                // Handle vector properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is a struct: %s"), 
                    StructProp->Struct ? *StructProp->Struct->GetName() : TEXT("NULL"));
                    
                if (StructProp->Struct == TBaseStructure<FVector>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        // Handle array input [x, y, z]
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FVector Vec(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting Vector(%f, %f, %f)"), 
                                Vec.X, Vec.Y, Vec.Z);
                            StructProp->CopySingleValue(PropertyAddr, &Vec);
                            bSuccess = true;
                        }
                        else
                        {
                            ErrorMessage = FString::Printf(TEXT("Vector property requires 3 values, got %d"), Arr.Num());
                            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                        }
                    }
                    else if (JsonValue->Type == EJson::Number)
                    {
                        // Handle scalar input (sets all components to same value)
                        float Value = JsonValue->AsNumber();
                        FVector Vec(Value, Value, Value);
                        void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting Vector(%f, %f, %f) from scalar"), 
                            Vec.X, Vec.Y, Vec.Z);
                        StructProp->CopySingleValue(PropertyAddr, &Vec);
                        bSuccess = true;
                    }
                    else
                    {
                        ErrorMessage = TEXT("Vector property requires either a single number or array of 3 numbers");
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                    }
                }
                else
                {
                    // Handle other struct properties using default handler
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Using generic struct handler for %s"), 
                        *PropertyName);
                    bSuccess = FUnrealMCPCommonUtils::SetObjectProperty(ComponentTemplate, PropertyName, JsonValue, ErrorMessage);
                    if (!bSuccess)
                    {
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set struct property: %s"), *ErrorMessage);
                    }
                }
            }
            else if (FEnumProperty* EnumProp = CastField<FEnumProperty>(Property))
            {
                // Handle enum properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is an enum"));
                if (JsonValue->Type == EJson::String)
                {
                    FString EnumValueName = JsonValue->AsString();
                    UEnum* Enum = EnumProp->GetEnum();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting enum from string: %s"), *EnumValueName);
                    
                    if (Enum)
                    {
                        int64 EnumValue = Enum->GetValueByNameString(EnumValueName);
                        
                        if (EnumValue != INDEX_NONE)
                        {
                            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Found enum value: %lld"), EnumValue);
                            EnumProp->GetUnderlyingProperty()->SetIntPropertyValue(
                                ComponentTemplate, 
                                EnumValue
                            );
                            bSuccess = true;
                        }
                        else
                        {
                            // List all possible enum values
                            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Available enum values for %s:"), 
                                *Enum->GetName());
                            for (int32 i = 0; i < Enum->NumEnums(); i++)
                            {
                                UE_LOG(LogTemp, Warning, TEXT("  - %s (%lld)"), 
                                    *Enum->GetNameStringByIndex(i),
                                    Enum->GetValueByIndex(i));
                            }
                            
                            ErrorMessage = FString::Printf(TEXT("Invalid enum value '%s' for property %s"), 
                                *EnumValueName, *PropertyName);
                            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                        }
                    }
                    else
                    {
                        ErrorMessage = TEXT("Enum object is NULL");
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                    }
                }
                else if (JsonValue->Type == EJson::Number)
                {
                    // Allow setting enum by integer value
                    int64 EnumValue = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting enum from number: %lld"), EnumValue);
                    EnumProp->GetUnderlyingProperty()->SetIntPropertyValue(
                        ComponentTemplate, 
                        EnumValue
                    );
                    bSuccess = true;
                }
                else
                {
                    ErrorMessage = TEXT("Enum property requires either a string name or integer value");
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                }
            }
            else if (FNumericProperty* NumericProp = CastField<FNumericProperty>(Property))
            {
                // Handle numeric properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is numeric: IsInteger=%d, IsFloat=%d"), 
                    NumericProp->IsInteger(), NumericProp->IsFloatingPoint());
                    
                if (JsonValue->Type == EJson::Number)
                {
                    double Value = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting numeric value: %f"), Value);
                    
                    if (NumericProp->IsInteger())
                    {
                        NumericProp->SetIntPropertyValue(ComponentTemplate, (int64)Value);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Set integer value: %lld"), (int64)Value);
                        bSuccess = true;
                    }
                    else if (NumericProp->IsFloatingPoint())
                    {
                        NumericProp->SetFloatingPointPropertyValue(ComponentTemplate, Value);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Set float value: %f"), Value);
                        bSuccess = true;
                    }
                }
                else
                {
                    ErrorMessage = TEXT("Numeric property requires a number value");
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                }
            }
            else
            {
                // Handle all other property types using default handler
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Using generic property handler for %s (Type: %s)"), 
                    *PropertyName, *Property->GetCPPType());
                bSuccess = FUnrealMCPCommonUtils::SetObjectProperty(ComponentTemplate, PropertyName, JsonValue, ErrorMessage);
                if (!bSuccess)
                {
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set property: %s"), *ErrorMessage);
                }
            }
        }
        catch (const std::exception& Ex)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - EXCEPTION: %s"), ANSI_TO_TCHAR(Ex.what()));
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Exception while setting property %s: %s"), *PropertyName, ANSI_TO_TCHAR(Ex.what())));
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - UNKNOWN EXCEPTION occurred while setting property %s"), *PropertyName);
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Unknown exception while setting property %s"), *PropertyName));
        }

        if (bSuccess)
        {
            // Mark the blueprint as modified
            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Successfully set property %s on component %s"),
                *PropertyName, *ComponentName);
            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

            // CRITICAL FIX: Save the Blueprint to disk after setting property
            FString BlueprintPath = TEXT("/Game/Blueprints/") + BlueprintName;
            bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("component"), ComponentName);
            ResultObj->SetStringField(TEXT("property"), PropertyName);
            ResultObj->SetBoolField(TEXT("success"), true);
            ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

            UE_LOG(LogTemp, Log, TEXT("Component property set and blueprint saved: %s (Saved: %s)"), *BlueprintPath, bSaved ? TEXT("Yes") : TEXT("No"));
            return ResultObj;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set property %s: %s"), 
                *PropertyName, *ErrorMessage);
            return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
        }
    }

    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Missing 'property_value' parameter"));
    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetPhysicsProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node && Node->GetVariableName().ToString() == ComponentName)
        {
            ComponentNode = Node;
            break;
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(ComponentNode->ComponentTemplate);
    if (!PrimComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Component is not a primitive component"));
    }

    // Set physics properties
    if (Params->HasField(TEXT("simulate_physics")))
    {
        PrimComponent->SetSimulatePhysics(Params->GetBoolField(TEXT("simulate_physics")));
    }

    if (Params->HasField(TEXT("mass")))
    {
        float Mass = Params->GetNumberField(TEXT("mass"));
        // In UE5.5, use proper overrideMass instead of just scaling
        PrimComponent->SetMassOverrideInKg(NAME_None, Mass);
        UE_LOG(LogTemp, Display, TEXT("Set mass for component %s to %f kg"), *ComponentName, Mass);
    }

    if (Params->HasField(TEXT("linear_damping")))
    {
        PrimComponent->SetLinearDamping(Params->GetNumberField(TEXT("linear_damping")));
    }

    if (Params->HasField(TEXT("angular_damping")))
    {
        PrimComponent->SetAngularDamping(Params->GetNumberField(TEXT("angular_damping")));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("component"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCompileBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Compile the blueprint
    FKismetEditorUtilities::CompileBlueprint(Blueprint);

    // CRITICAL FIX: Save the Blueprint to disk after compilation
    FString BlueprintPath = TEXT("/Game/Blueprints/") + BlueprintName;
    bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("name"), BlueprintName);
    ResultObj->SetBoolField(TEXT("compiled"), true);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    UE_LOG(LogTemp, Log, TEXT("Blueprint compiled and saved: %s (Saved: %s)"), *BlueprintPath, bSaved ? TEXT("Yes") : TEXT("No"));
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSpawnBlueprintActor(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ActorName;
    if (!Params->TryGetStringField(TEXT("actor_name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'actor_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get transform parameters
    FVector Location(0.0f, 0.0f, 0.0f);
    FRotator Rotation(0.0f, 0.0f, 0.0f);

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }

    // Spawn the actor
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get editor world"));
    }

    FTransform SpawnTransform;
    SpawnTransform.SetLocation(Location);
    SpawnTransform.SetRotation(FQuat(Rotation));

    AActor* NewActor = World->SpawnActor<AActor>(Blueprint->GeneratedClass, SpawnTransform);
    if (NewActor)
    {
        NewActor->SetActorLabel(*ActorName);
        return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to spawn blueprint actor"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetBlueprintProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString PropertyName;
    if (!Params->TryGetStringField(TEXT("property_name"), PropertyName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the default object
    UObject* DefaultObject = Blueprint->GeneratedClass->GetDefaultObject();
    if (!DefaultObject)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get default object"));
    }

    // Set the property value
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, PropertyName, JsonValue, ErrorMessage))
        {
            // Mark the blueprint as modified
            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("property"), PropertyName);
            ResultObj->SetBoolField(TEXT("success"), true);
            return ResultObj;
        }
        else
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
        }
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetStaticMeshProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node && Node->GetVariableName().ToString() == ComponentName)
        {
            ComponentNode = Node;
            break;
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    UStaticMeshComponent* MeshComponent = Cast<UStaticMeshComponent>(ComponentNode->ComponentTemplate);
    if (!MeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Component is not a static mesh component"));
    }

    // Set static mesh properties
    if (Params->HasField(TEXT("static_mesh")))
    {
        FString MeshPath = Params->GetStringField(TEXT("static_mesh"));
        UStaticMesh* Mesh = Cast<UStaticMesh>(UEditorAssetLibrary::LoadAsset(MeshPath));
        if (Mesh)
        {
            MeshComponent->SetStaticMesh(Mesh);
        }
    }

    if (Params->HasField(TEXT("material")))
    {
        FString MaterialPath = Params->GetStringField(TEXT("material"));
        UMaterialInterface* Material = Cast<UMaterialInterface>(UEditorAssetLibrary::LoadAsset(MaterialPath));
        if (Material)
        {
            MeshComponent->SetMaterial(0, Material);
        }
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("component"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetPawnProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the default object
    UObject* DefaultObject = Blueprint->GeneratedClass->GetDefaultObject();
    if (!DefaultObject)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get default object"));
    }

    // Track if any properties were set successfully
    bool bAnyPropertiesSet = false;
    TSharedPtr<FJsonObject> ResultsObj = MakeShared<FJsonObject>();
    
    // Set auto possess player if specified
    if (Params->HasField(TEXT("auto_possess_player")))
    {
        TSharedPtr<FJsonValue> AutoPossessValue = Params->Values.FindRef(TEXT("auto_possess_player"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, TEXT("AutoPossessPlayer"), AutoPossessValue, ErrorMessage))
        {
            bAnyPropertiesSet = true;
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), true);
            ResultsObj->SetObjectField(TEXT("AutoPossessPlayer"), PropResultObj);
        }
        else
        {
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), false);
            PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
            ResultsObj->SetObjectField(TEXT("AutoPossessPlayer"), PropResultObj);
        }
    }
    
    // Set controller rotation properties
    const TCHAR* RotationProps[] = {
        TEXT("bUseControllerRotationYaw"),
        TEXT("bUseControllerRotationPitch"),
        TEXT("bUseControllerRotationRoll")
    };
    
    const TCHAR* ParamNames[] = {
        TEXT("use_controller_rotation_yaw"),
        TEXT("use_controller_rotation_pitch"),
        TEXT("use_controller_rotation_roll")
    };
    
    for (int32 i = 0; i < 3; i++)
    {
        if (Params->HasField(ParamNames[i]))
        {
            TSharedPtr<FJsonValue> Value = Params->Values.FindRef(ParamNames[i]);
            
            FString ErrorMessage;
            if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, RotationProps[i], Value, ErrorMessage))
            {
                bAnyPropertiesSet = true;
                TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
                PropResultObj->SetBoolField(TEXT("success"), true);
                ResultsObj->SetObjectField(RotationProps[i], PropResultObj);
            }
            else
            {
                TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
                PropResultObj->SetBoolField(TEXT("success"), false);
                PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
                ResultsObj->SetObjectField(RotationProps[i], PropResultObj);
            }
        }
    }
    
    // Set can be damaged property
    if (Params->HasField(TEXT("can_be_damaged")))
    {
        TSharedPtr<FJsonValue> Value = Params->Values.FindRef(TEXT("can_be_damaged"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, TEXT("bCanBeDamaged"), Value, ErrorMessage))
        {
            bAnyPropertiesSet = true;
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), true);
            ResultsObj->SetObjectField(TEXT("bCanBeDamaged"), PropResultObj);
        }
        else
        {
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), false);
            PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
            ResultsObj->SetObjectField(TEXT("bCanBeDamaged"), PropResultObj);
        }
    }

    // Mark the blueprint as modified if any properties were set
    if (bAnyPropertiesSet)
    {
        FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
    }
    else if (ResultsObj->Values.Num() == 0)
    {
        // No properties were specified
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No properties specified to set"));
    }

    TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
    ResponseObj->SetStringField(TEXT("blueprint"), BlueprintName);
    ResponseObj->SetBoolField(TEXT("success"), bAnyPropertiesSet);
    ResponseObj->SetObjectField(TEXT("results"), ResultsObj);
    return ResponseObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateAuracronMultilayerBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_multilayer_blueprint must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString StructureType;
    if (!Params->TryGetStringField(TEXT("structure_type"), StructureType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'structure_type' parameter"));
    }

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create specialized Auracron multilayer blueprint
    FString PackagePath = TEXT("/Game/Auracron/Blueprints/Multilayer/");
    FString AssetName = BlueprintName;

    // Check if blueprint already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Auracron multilayer blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory with modern UE 5.6.1 APIs
    UBlueprintFactory* Factory = NewObject<UBlueprintFactory>();
    Factory->ParentClass = AActor::StaticClass(); // Base actor for multilayer structures

    // Create the blueprint package
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath((PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewBlueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Auracron multilayer blueprint"));
    }

    // ADD REAL MULTILAYER COMPONENTS TO BLUEPRINT
    int32 ComponentsAdded = 0;

    // Add root scene component for multilayer positioning
    USCS_Node* RootNode = NewBlueprint->SimpleConstructionScript->CreateNode(USceneComponent::StaticClass(), TEXT("MultilayerRoot"));
    if (RootNode)
    {
        NewBlueprint->SimpleConstructionScript->AddNode(RootNode);
        ComponentsAdded++;
    }

    // Add layer-specific components based on structure type
    if (StructureType == TEXT("portal") || StructureType == TEXT("elevator"))
    {
        // Add portal/elevator components for each layer
        for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
        {
            FString LayerName;
            if (LayerIndex == 0) LayerName = TEXT("Planicie_Radiante");
            else if (LayerIndex == 1) LayerName = TEXT("Firmamento_Zephyr");
            else if (LayerIndex == 2) LayerName = TEXT("Abismo_Umbral");

            // Create layer connection point
            FString ConnectionName = FString::Printf(TEXT("%s_Connection"), *LayerName);
            USCS_Node* ConnectionNode = NewBlueprint->SimpleConstructionScript->CreateNode(UStaticMeshComponent::StaticClass(), *ConnectionName);
            if (ConnectionNode)
            {
                UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(ConnectionNode->ComponentTemplate);
                if (MeshComp)
                {
                    // Set position for layer
                    float LayerHeight = LayerIndex * 2000.0f;
                    MeshComp->SetRelativeLocation(FVector(0.0f, 0.0f, LayerHeight));

                    // Load appropriate mesh for portal/elevator
                    UStaticMesh* PortalMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                    if (PortalMesh)
                    {
                        MeshComp->SetStaticMesh(PortalMesh);
                        MeshComp->SetRelativeScale3D(FVector(2.0f, 2.0f, 0.5f));
                    }
                }

                NewBlueprint->SimpleConstructionScript->AddNode(ConnectionNode);
                ComponentsAdded++;
            }

            // Add collision component for interaction
            FString CollisionName = FString::Printf(TEXT("%s_Collision"), *LayerName);
            USCS_Node* CollisionNode = NewBlueprint->SimpleConstructionScript->CreateNode(UBoxComponent::StaticClass(), *CollisionName);
            if (CollisionNode)
            {
                UBoxComponent* BoxComp = Cast<UBoxComponent>(CollisionNode->ComponentTemplate);
                if (BoxComp)
                {
                    float LayerHeight = LayerIndex * 2000.0f;
                    BoxComp->SetRelativeLocation(FVector(0.0f, 0.0f, LayerHeight));
                    BoxComp->SetBoxExtent(FVector(200.0f, 200.0f, 100.0f));
                    BoxComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                    BoxComp->SetCollisionResponseToAllChannels(ECR_Ignore);
                    BoxComp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
                }

                NewBlueprint->SimpleConstructionScript->AddNode(CollisionNode);
                ComponentsAdded++;
            }
        }
    }

    // Compile the blueprint with modern UE 5.6.1 compilation
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    FAssetRegistryModule::AssetCreated(NewBlueprint);
    Package->MarkPackageDirty();

    FString BlueprintPath = PackagePath + AssetName;
    bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), AssetName);
    ResultObj->SetStringField(TEXT("structure_type"), StructureType);
    ResultObj->SetStringField(TEXT("package_path"), BlueprintPath);
    ResultObj->SetNumberField(TEXT("components_added"), ComponentsAdded);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Blueprints/Multilayer/") + AssetName + TEXT(".uasset"));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Multilayer Blueprint created: %s (Type: %s, Components: %d, Saved: %s)"),
           *BlueprintPath, *StructureType, ComponentsAdded, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateAuracronPortalBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_portal_blueprint must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString PortalType;
    if (!Params->TryGetStringField(TEXT("portal_type"), PortalType))
    {
        PortalType = TEXT("dimensional_gateway"); // Default portal type
    }

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create specialized Auracron portal blueprint
    FString PackagePath = TEXT("/Game/Auracron/Blueprints/Portals/");
    FString AssetName = BlueprintName;

    // Check if blueprint already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Auracron portal blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory with modern UE 5.6.1 APIs
    UBlueprintFactory* Factory = NewObject<UBlueprintFactory>();
    Factory->ParentClass = AActor::StaticClass();

    // Create the blueprint package
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath((PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewBlueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Auracron portal blueprint"));
    }

    // ADD REAL PORTAL COMPONENTS TO BLUEPRINT
    int32 ComponentsAdded = 0;

    // Add root scene component
    USCS_Node* RootNode = NewBlueprint->SimpleConstructionScript->CreateNode(USceneComponent::StaticClass(), TEXT("PortalRoot"));
    if (RootNode)
    {
        NewBlueprint->SimpleConstructionScript->AddNode(RootNode);
        ComponentsAdded++;
    }

    // Add portal visual effect component
    USCS_Node* PortalMeshNode = NewBlueprint->SimpleConstructionScript->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("PortalMesh"));
    if (PortalMeshNode)
    {
        UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(PortalMeshNode->ComponentTemplate);
        if (MeshComp)
        {
            // Load portal mesh based on type
            UStaticMesh* PortalMesh = nullptr;
            if (PortalType == TEXT("dimensional_gateway"))
            {
                PortalMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
            }
            else if (PortalType == TEXT("mystic_elevator"))
            {
                PortalMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
            }
            else if (PortalType == TEXT("dimensional_bridge"))
            {
                PortalMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube.Cube"));
            }
            else
            {
                PortalMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere")); // Default
            }

            if (PortalMesh)
            {
                MeshComp->SetStaticMesh(PortalMesh);
                MeshComp->SetRelativeScale3D(FVector(3.0f, 3.0f, 3.0f)); // Large portal

                // Create dynamic material for portal effect
                UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                if (BaseMaterial)
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, MeshComp);
                    if (DynamicMaterial)
                    {
                        // Set portal color based on type
                        FLinearColor PortalColor = FLinearColor::Blue; // Default
                        if (PortalType == TEXT("dimensional_gateway")) PortalColor = FLinearColor(0.0f, 1.0f, 1.0f, 1.0f); // Cyan
                        else if (PortalType == TEXT("mystic_elevator")) PortalColor = FLinearColor::Green;
                        else if (PortalType == TEXT("dimensional_bridge")) PortalColor = FLinearColor::Yellow;

                        DynamicMaterial->SetVectorParameterValue(TEXT("Color"), PortalColor);
                        MeshComp->SetMaterial(0, DynamicMaterial);
                    }
                }
            }
        }

        NewBlueprint->SimpleConstructionScript->AddNode(PortalMeshNode);
        ComponentsAdded++;
    }

    // Add interaction collision component
    USCS_Node* CollisionNode = NewBlueprint->SimpleConstructionScript->CreateNode(USphereComponent::StaticClass(), TEXT("PortalCollision"));
    if (CollisionNode)
    {
        USphereComponent* SphereComp = Cast<USphereComponent>(CollisionNode->ComponentTemplate);
        if (SphereComp)
        {
            SphereComp->SetSphereRadius(400.0f); // Large interaction radius
            SphereComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            SphereComp->SetCollisionResponseToAllChannels(ECR_Ignore);
            SphereComp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
            SphereComp->SetGenerateOverlapEvents(true);
        }

        NewBlueprint->SimpleConstructionScript->AddNode(CollisionNode);
        ComponentsAdded++;
    }

    // Add audio component for portal sounds
    USCS_Node* AudioNode = NewBlueprint->SimpleConstructionScript->CreateNode(UAudioComponent::StaticClass(), TEXT("PortalAudio"));
    if (AudioNode)
    {
        UAudioComponent* AudioComp = Cast<UAudioComponent>(AudioNode->ComponentTemplate);
        if (AudioComp)
        {
            AudioComp->bAutoActivate = true;
            AudioComp->SetVolumeMultiplier(0.5f);
        }

        NewBlueprint->SimpleConstructionScript->AddNode(AudioNode);
        ComponentsAdded++;
    }

    // Compile the blueprint with modern UE 5.6.1 compilation
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    FAssetRegistryModule::AssetCreated(NewBlueprint);
    Package->MarkPackageDirty();

    FString BlueprintPath = PackagePath + AssetName;
    bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), AssetName);
    ResultObj->SetStringField(TEXT("portal_type"), PortalType);
    ResultObj->SetStringField(TEXT("package_path"), BlueprintPath);
    ResultObj->SetNumberField(TEXT("components_added"), ComponentsAdded);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Blueprints/Portals/") + AssetName + TEXT(".uasset"));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Portal Blueprint created: %s (Type: %s, Components: %d, Saved: %s)"),
           *BlueprintPath, *PortalType, ComponentsAdded, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateAuracronTowerBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_tower_blueprint must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString TowerType;
    if (!Params->TryGetStringField(TEXT("tower_type"), TowerType))
    {
        TowerType = TEXT("basic_tower"); // Default tower type
    }

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create specialized Auracron tower blueprint
    FString PackagePath = TEXT("/Game/Auracron/Blueprints/Towers/");
    FString AssetName = BlueprintName;

    // Check if blueprint already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Auracron tower blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory with modern UE 5.6.1 APIs
    UBlueprintFactory* Factory = NewObject<UBlueprintFactory>();
    Factory->ParentClass = AActor::StaticClass();

    // Create the blueprint package
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath((PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewBlueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Auracron tower blueprint"));
    }

    // ADD REAL TOWER COMPONENTS TO BLUEPRINT
    int32 ComponentsAdded = 0;

    // Add root scene component
    USCS_Node* RootNode = NewBlueprint->SimpleConstructionScript->CreateNode(USceneComponent::StaticClass(), TEXT("TowerRoot"));
    if (RootNode)
    {
        NewBlueprint->SimpleConstructionScript->AddNode(RootNode);
        ComponentsAdded++;
    }

    // Add tower base mesh
    USCS_Node* BaseMeshNode = NewBlueprint->SimpleConstructionScript->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("TowerBase"));
    if (BaseMeshNode)
    {
        UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(BaseMeshNode->ComponentTemplate);
        if (MeshComp)
        {
            UStaticMesh* TowerMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
            if (TowerMesh)
            {
                MeshComp->SetStaticMesh(TowerMesh);
                MeshComp->SetRelativeScale3D(FVector(2.0f, 2.0f, 4.0f)); // Tall tower
                MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            }
        }

        NewBlueprint->SimpleConstructionScript->AddNode(BaseMeshNode);
        ComponentsAdded++;
    }

    // Add tower top/turret
    USCS_Node* TurretNode = NewBlueprint->SimpleConstructionScript->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("TowerTurret"));
    if (TurretNode)
    {
        UStaticMeshComponent* TurretComp = Cast<UStaticMeshComponent>(TurretNode->ComponentTemplate);
        if (TurretComp)
        {
            UStaticMesh* TurretMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
            if (TurretMesh)
            {
                TurretComp->SetStaticMesh(TurretMesh);
                TurretComp->SetRelativeLocation(FVector(0.0f, 0.0f, 400.0f)); // Top of tower
                TurretComp->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.5f));
            }
        }

        NewBlueprint->SimpleConstructionScript->AddNode(TurretNode);
        ComponentsAdded++;
    }

    // Add attack range visualization (sphere component)
    USCS_Node* RangeNode = NewBlueprint->SimpleConstructionScript->CreateNode(USphereComponent::StaticClass(), TEXT("AttackRange"));
    if (RangeNode)
    {
        USphereComponent* SphereComp = Cast<USphereComponent>(RangeNode->ComponentTemplate);
        if (SphereComp)
        {
            float AttackRange = 800.0f; // Default attack range
            if (TowerType == TEXT("basic_tower")) AttackRange = 800.0f;
            else if (TowerType == TEXT("advanced_tower")) AttackRange = 1000.0f;
            else if (TowerType == TEXT("nexus_tower")) AttackRange = 1200.0f;

            SphereComp->SetSphereRadius(AttackRange);
            SphereComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            SphereComp->SetCollisionResponseToAllChannels(ECR_Ignore);
            SphereComp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
            SphereComp->SetVisibility(false); // Invisible range indicator
        }

        NewBlueprint->SimpleConstructionScript->AddNode(RangeNode);
        ComponentsAdded++;
    }

    // Compile the blueprint with modern UE 5.6.1 compilation
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    FAssetRegistryModule::AssetCreated(NewBlueprint);
    Package->MarkPackageDirty();

    FString BlueprintPath = PackagePath + AssetName;
    bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), AssetName);
    ResultObj->SetStringField(TEXT("tower_type"), TowerType);
    ResultObj->SetStringField(TEXT("package_path"), BlueprintPath);
    ResultObj->SetNumberField(TEXT("components_added"), ComponentsAdded);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Blueprints/Towers/") + AssetName + TEXT(".uasset"));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Tower Blueprint created: %s (Type: %s, Components: %d, Saved: %s)"),
           *BlueprintPath, *TowerType, ComponentsAdded, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateAuracronObjectiveBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_auracron_objective_blueprint must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ObjectiveType;
    if (!Params->TryGetStringField(TEXT("objective_type"), ObjectiveType))
    {
        ObjectiveType = TEXT("epic_objective"); // Default objective type
    }

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER) - Create specialized Auracron objective blueprint
    FString PackagePath = TEXT("/Game/Auracron/Blueprints/Objectives/");
    FString AssetName = BlueprintName;

    // Check if blueprint already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Auracron objective blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory with modern UE 5.6.1 APIs
    UBlueprintFactory* Factory = NewObject<UBlueprintFactory>();
    Factory->ParentClass = AActor::StaticClass();

    // Create the blueprint package
    UPackage* Package = FUnrealMCPCommonUtils::CreatePackageWithCorrectPath((PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewBlueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Auracron objective blueprint"));
    }

    // ADD REAL OBJECTIVE COMPONENTS TO BLUEPRINT
    int32 ComponentsAdded = 0;

    // Add root scene component
    USCS_Node* RootNode = NewBlueprint->SimpleConstructionScript->CreateNode(USceneComponent::StaticClass(), TEXT("ObjectiveRoot"));
    if (RootNode)
    {
        NewBlueprint->SimpleConstructionScript->AddNode(RootNode);
        ComponentsAdded++;
    }

    // Add main objective mesh based on type
    USCS_Node* ObjectiveMeshNode = NewBlueprint->SimpleConstructionScript->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("ObjectiveMesh"));
    if (ObjectiveMeshNode)
    {
        UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(ObjectiveMeshNode->ComponentTemplate);
        if (MeshComp)
        {
            UStaticMesh* ObjectiveMesh = nullptr;
            FVector ObjectiveScale = FVector(3.0f, 3.0f, 3.0f);

            if (ObjectiveType == TEXT("guardiao_aurora")) // Guardião da Aurora
            {
                ObjectiveMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
                ObjectiveScale = FVector(5.0f, 5.0f, 5.0f); // Large guardian
            }
            else if (ObjectiveType == TEXT("senhor_ventos")) // Senhor dos Ventos
            {
                ObjectiveMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
                ObjectiveScale = FVector(4.0f, 4.0f, 6.0f); // Tall wind lord
            }
            else if (ObjectiveType == TEXT("devorador_sombras")) // Devorador das Sombras
            {
                ObjectiveMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube.Cube"));
                ObjectiveScale = FVector(4.0f, 4.0f, 4.0f); // Dark cube
            }
            else // Generic epic objective
            {
                ObjectiveMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
            }

            if (ObjectiveMesh)
            {
                MeshComp->SetStaticMesh(ObjectiveMesh);
                MeshComp->SetRelativeScale3D(ObjectiveScale);
                MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

                // Create dynamic material for objective
                UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                if (BaseMaterial)
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, MeshComp);
                    if (DynamicMaterial)
                    {
                        // Set objective color based on type
                        FLinearColor ObjectiveColor = FLinearColor::White; // Default
                        if (ObjectiveType == TEXT("guardiao_aurora")) ObjectiveColor = FLinearColor(1.0f, 0.8f, 0.0f, 1.0f); // Golden
                        else if (ObjectiveType == TEXT("senhor_ventos")) ObjectiveColor = FLinearColor(0.0f, 1.0f, 0.8f, 1.0f); // Wind cyan
                        else if (ObjectiveType == TEXT("devorador_sombras")) ObjectiveColor = FLinearColor(0.3f, 0.0f, 0.3f, 1.0f); // Dark purple

                        DynamicMaterial->SetVectorParameterValue(TEXT("Color"), ObjectiveColor);
                        MeshComp->SetMaterial(0, DynamicMaterial);
                    }
                }
            }
        }

        NewBlueprint->SimpleConstructionScript->AddNode(ObjectiveMeshNode);
        ComponentsAdded++;
    }

    // Add interaction collision component
    USCS_Node* CollisionNode = NewBlueprint->SimpleConstructionScript->CreateNode(USphereComponent::StaticClass(), TEXT("ObjectiveCollision"));
    if (CollisionNode)
    {
        USphereComponent* SphereComp = Cast<USphereComponent>(CollisionNode->ComponentTemplate);
        if (SphereComp)
        {
            SphereComp->SetSphereRadius(600.0f); // Large interaction radius for epic objectives
            SphereComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            SphereComp->SetCollisionResponseToAllChannels(ECR_Ignore);
            SphereComp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
            SphereComp->SetGenerateOverlapEvents(true);
        }

        NewBlueprint->SimpleConstructionScript->AddNode(CollisionNode);
        ComponentsAdded++;
    }

    // Add audio component for objective sounds
    USCS_Node* AudioNode = NewBlueprint->SimpleConstructionScript->CreateNode(UAudioComponent::StaticClass(), TEXT("ObjectiveAudio"));
    if (AudioNode)
    {
        UAudioComponent* AudioComp = Cast<UAudioComponent>(AudioNode->ComponentTemplate);
        if (AudioComp)
        {
            AudioComp->bAutoActivate = true;
            AudioComp->SetVolumeMultiplier(0.8f); // Louder for epic objectives
        }

        NewBlueprint->SimpleConstructionScript->AddNode(AudioNode);
        ComponentsAdded++;
    }

    // Add health bar component (for objectives with health)
    USCS_Node* HealthBarNode = NewBlueprint->SimpleConstructionScript->CreateNode(USceneComponent::StaticClass(), TEXT("HealthBarAnchor"));
    if (HealthBarNode)
    {
        USceneComponent* HealthAnchor = Cast<USceneComponent>(HealthBarNode->ComponentTemplate);
        if (HealthAnchor)
        {
            HealthAnchor->SetRelativeLocation(FVector(0.0f, 0.0f, 800.0f)); // Above objective
        }

        NewBlueprint->SimpleConstructionScript->AddNode(HealthBarNode);
        ComponentsAdded++;
    }

    // Compile the blueprint with modern UE 5.6.1 compilation
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    FAssetRegistryModule::AssetCreated(NewBlueprint);
    Package->MarkPackageDirty();

    FString BlueprintPath = PackagePath + AssetName;
    bool bSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("blueprint_name"), AssetName);
    ResultObj->SetStringField(TEXT("objective_type"), ObjectiveType);
    ResultObj->SetStringField(TEXT("package_path"), BlueprintPath);
    ResultObj->SetNumberField(TEXT("components_added"), ComponentsAdded);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Blueprints/Objectives/") + AssetName + TEXT(".uasset"));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Auracron Objective Blueprint created: %s (Type: %s, Components: %d, Saved: %s)"),
           *BlueprintPath, *ObjectiveType, ComponentsAdded, bSaved ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}
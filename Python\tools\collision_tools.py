"""
Collision Tools for Unreal MCP.

This module provides tools for creating and managing advanced multilayer collision systems
in Unreal Engine, specifically designed for Auracron's 3D MOBA with layer-specific collision
profiles, intelligent collision detection, and Chaos Physics integration.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_collision_tools(mcp: FastMCP):
    """Register Collision tools with the MCP server."""
    
    @mcp.tool()
    def create_layer_collision_profiles(
        ctx: Context,
        profile_system_name: str,
        layer_profiles: Optional[List[Dict[str, Any]]] = None,
        collision_channels: Optional[List[Dict[str, str]]] = None,
        response_matrices: Optional[Dict[str, Any]] = None,
        chaos_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create layer-specific collision profiles with advanced Chaos Physics integration.
        
        Args:
            profile_system_name: Name of the collision profile system
            layer_profiles: Collision profiles per layer with detailed settings
            collision_channels: Custom collision channels for multilayer detection
            response_matrices: Collision response matrices per layer
            chaos_settings: Advanced Chaos Physics settings per layer
        
        Returns:
            Dict containing success status and collision profile system details
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"profile_system_name": profile_system_name}
            
            if layer_profiles:
                params["layer_profiles"] = layer_profiles
            if collision_channels:
                params["collision_channels"] = collision_channels
            if response_matrices:
                params["response_matrices"] = response_matrices
            if chaos_settings:
                params["chaos_settings"] = chaos_settings
            
            logger.info(f"Creating layer collision profiles: {profile_system_name}")
            
            response = unreal.send_command("create_layer_collision_profiles", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision profiles creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating layer collision profiles: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_intelligent_collision_detection(
        ctx: Context,
        detection_system_name: str,
        cross_layer_rules: Optional[Dict[str, Any]] = None,
        detection_ranges: Optional[Dict[str, float]] = None,
        filtering_settings: Optional[Dict[str, Any]] = None,
        optimization_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Set up intelligent multilayer collision detection system.
        
        Args:
            detection_system_name: Name of the detection system
            cross_layer_rules: Rules for cross-layer collision detection
            detection_ranges: Detection ranges per layer and interaction type
            filtering_settings: Advanced collision filtering per layer
            optimization_settings: Performance optimization configurations
        
        Returns:
            Dict containing success status and intelligent detection system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"detection_system_name": detection_system_name}
            
            if cross_layer_rules:
                params["cross_layer_rules"] = cross_layer_rules
            if detection_ranges:
                params["detection_ranges"] = detection_ranges
            if filtering_settings:
                params["filtering_settings"] = filtering_settings
            if optimization_settings:
                params["optimization_settings"] = optimization_settings
            
            logger.info(f"Creating intelligent collision detection: {detection_system_name}")
            
            response = unreal.send_command("create_intelligent_collision_detection", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Intelligent collision detection creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating intelligent collision detection: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def configure_chaos_physics_per_layer(
        ctx: Context,
        chaos_system_name: str,
        solver_settings: Optional[Dict[str, Any]] = None,
        material_properties: Optional[Dict[str, Any]] = None,
        constraint_settings: Optional[Dict[str, Any]] = None,
        performance_profiles: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Configure advanced Chaos Physics settings per layer.
        
        Args:
            chaos_system_name: Name of the Chaos Physics system
            solver_settings: Solver configurations per layer
            material_properties: Physics materials per layer
            constraint_settings: Physics constraints per layer
            performance_profiles: Performance optimization per layer
        
        Returns:
            Dict containing success status and Chaos Physics configuration results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"chaos_system_name": chaos_system_name}
            
            if solver_settings:
                params["solver_settings"] = solver_settings
            if material_properties:
                params["material_properties"] = material_properties
            if constraint_settings:
                params["constraint_settings"] = constraint_settings
            if performance_profiles:
                params["performance_profiles"] = performance_profiles
            
            logger.info(f"Configuring Chaos Physics per layer: {chaos_system_name}")
            
            response = unreal.send_command("configure_chaos_physics_per_layer", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Chaos Physics configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring Chaos Physics per layer: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_custom_collision_handlers(
        ctx: Context,
        handler_system_name: str,
        layer_handlers: Optional[List[Dict[str, Any]]] = None,
        response_behaviors: Optional[Dict[str, Any]] = None,
        event_systems: Optional[Dict[str, Any]] = None,
        damage_systems: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create custom collision handlers for layer-specific responses.
        
        Args:
            handler_system_name: Name of the collision handler system
            layer_handlers: Custom handlers per layer
            response_behaviors: Collision response behaviors per layer
            event_systems: Collision event handling per layer
            damage_systems: Damage calculation per layer collision
        
        Returns:
            Dict containing success status and collision handler system results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"handler_system_name": handler_system_name}
            
            if layer_handlers:
                params["layer_handlers"] = layer_handlers
            if response_behaviors:
                params["response_behaviors"] = response_behaviors
            if event_systems:
                params["event_systems"] = event_systems
            if damage_systems:
                params["damage_systems"] = damage_systems
            
            logger.info(f"Creating custom collision handlers: {handler_system_name}")
            
            response = unreal.send_command("create_custom_collision_handlers", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Custom collision handlers creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating custom collision handlers: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def create_advanced_shape_collision(
        ctx: Context,
        shape_system_name: str,
        layer_geometries: Optional[List[Dict[str, Any]]] = None,
        shape_types: Optional[List[str]] = None,
        collision_complexity: Optional[Dict[str, Any]] = None,
        mesh_optimization: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Set up advanced shape collision with custom geometry per layer.
        
        Args:
            shape_system_name: Name of the shape collision system
            layer_geometries: Custom geometries per layer
            shape_types: Advanced shape types (convex, trimesh, levelset)
            collision_complexity: Collision complexity settings per layer
            mesh_optimization: Collision mesh optimization per layer
        
        Returns:
            Dict containing success status and advanced shape collision results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"shape_system_name": shape_system_name}
            
            if layer_geometries:
                params["layer_geometries"] = layer_geometries
            if shape_types:
                params["shape_types"] = shape_types
            if collision_complexity:
                params["collision_complexity"] = collision_complexity
            if mesh_optimization:
                params["mesh_optimization"] = mesh_optimization
            
            logger.info(f"Creating advanced shape collision: {shape_system_name}")
            
            response = unreal.send_command("create_advanced_shape_collision", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Advanced shape collision creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating advanced shape collision: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    @mcp.tool()
    def optimize_collision_performance(
        ctx: Context,
        optimization_system_name: str,
        spatial_partitioning: Optional[Dict[str, Any]] = None,
        culling_settings: Optional[Dict[str, Any]] = None,
        query_optimization: Optional[Dict[str, Any]] = None,
        threading_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Optimize collision system performance for multilayer scenarios.
        
        Args:
            optimization_system_name: Name of the optimization system
            spatial_partitioning: Spatial partitioning settings per layer
            culling_settings: Collision culling configurations
            query_optimization: Collision query optimization settings
            threading_settings: Multi-threading configurations
        
        Returns:
            Dict containing success status and performance optimization results
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "error": "Failed to connect to Unreal Engine"}
                
            params = {"optimization_system_name": optimization_system_name}
            
            if spatial_partitioning:
                params["spatial_partitioning"] = spatial_partitioning
            if culling_settings:
                params["culling_settings"] = culling_settings
            if query_optimization:
                params["query_optimization"] = query_optimization
            if threading_settings:
                params["threading_settings"] = threading_settings
            
            logger.info(f"Optimizing collision performance: {optimization_system_name}")
            
            response = unreal.send_command("optimize_collision_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "error": "No response from Unreal Engine"}
            
            logger.info(f"Collision performance optimization response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error optimizing collision performance: {e}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}

{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\module.unrealmcp.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcp.init.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcparchitecturecommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcparchitecturecommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\staticmeshactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\staticmeshactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\instancedstaticmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\sminstance\\sminstancemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\sminstance\\sminstanceelementid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmeshdelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\sminstanceelementid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\sminstancemanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedatasceneproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\rendering\\renderingspatialhash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\isminstancedatamanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\isminstancedatasceneproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instancedstaticmesh\\instanceattributetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\instancedstaticmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\hierarchicalinstancedstaticmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hierarchicalinstancedstaticmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\curves\\spline.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spline.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\splinecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\splinemeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\shaders\\shared\\splinemeshshaderparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\shaders\\shared\\hlsltypealiases.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\splinemeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\simpleconstructionscript.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\simpleconstructionscript.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutioninspection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcrc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcrc.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\data\\pcgdataptrwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdataptrwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgattributepropertyselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcgpointhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgattributepropertyselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadatacommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadatacommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\graph\\pcgstackcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgextracapture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgstackcontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutionstateinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgactorselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgactorselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgpreconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadataattributetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadataattributetraits.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpreconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\tests\\determinism\\pcgdeterminismsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdeterminismsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphexecutionstateinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\grid\\pcggriddescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayertype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayertype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshmergingsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshmergingsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshproxysettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshproxysettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshapproximationsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshapproximationsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodbuilder.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggriddescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraph.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\editor\\pcggraphcustomization.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphcustomization.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcggraphparameterextension.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\propertybag.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\sharedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutilstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\sharedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\propertybag.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\editor\\pcggraphcomment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\edgraphnode_comment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\edgraphnode_comment.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphcomment.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\computeframework\\source\\computeframework\\public\\computeframework\\computegraphinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\computeframework\\intermediate\\build\\win64\\unrealeditor\\inc\\computeframework\\uht\\computegraphinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraph.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\grid\\pcgcomponentoctree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgnodevisuallogs.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgmanagedresource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgsplinemeshparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsplinemeshparams.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\splinemeshcomponentdescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\splinemeshcomponentdescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ismpartition\\ismcomponentdescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ismcomponentdescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\animation\\animbank.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\experimental\\containers\\robinhoodhashtable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\shaders\\shared\\skinningdefinitions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\matrix3x4.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animbank.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmanagedresource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\bodysetup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\aggregategeom.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\convexelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\shapeelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapeelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\convexelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\levelsetelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\levelsetelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\mllevelsetelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\mllevelsetmodelandbonesbinninginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\mllevelsetmodelandbonesbinninginfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\nne\\public\\nnemodeldata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\nne\\uht\\nnemodeldata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\nne\\public\\nneruntimecpu.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\nne\\public\\nneruntimerunsync.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\nne\\public\\nnestatus.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\nne\\public\\nnetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\nne\\uht\\nnetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\nne\\uht\\nneruntimecpu.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\mllevelset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaoscore\\public\\chaos\\arraynd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\uniformgrid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\dynamicparticles.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\implicitobjectscaled.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\contactpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\convex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\convexstructuredata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\convexflattenedarraystructuredata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\massproperties.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collisionconvexmesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\trianglemesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\map.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\segmentmesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\aabbtree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\aabbvectorized.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaoscore\\public\\chaos\\vectorutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\aabbvectorizeddouble.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\aabbtreedirtygridutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\boundingvolume.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\boundingvolumeutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\sphere.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\gjkshape.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\memory", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\unordered_set", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xhash", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\cmath", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\list", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xpolymorphic_allocator.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\vector", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_bit_utils.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_sanitizer_annotate_container.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xbit_ops.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xstring", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_string_view.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xnode_handle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\compgeom\\convexhull3.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\halfspacetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\vectortypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\mathutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\geometrybase.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\sstream", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\istream", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_ostream.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\ios", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xlocnum", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\streambuf", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xiosbase", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.26100.0\\ucrt\\share.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\system_error", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_system_error_abi.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\cerrno", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\stdexcept", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xcall_once.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xerrc.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xlocale", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xfacet", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\xlocinfo", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\__msvc_xlocinfo_types.hpp", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\cctype", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\clocale", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.26100.0\\ucrt\\locale.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\string", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\vectorutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\indextypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\linetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\planetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\progresscancel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\utilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\mllevelsetneuralinference.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdsoftsevolutionfwd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\mllevelsetelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\boxelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\boxelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\skinnedlevelsetelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\weightedlatticeimplicitobject.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\hierarchicalspatialhash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\levelset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\skinnedlevelsetelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\skinnedtrianglemeshelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\skinnedtrianglemesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\skinnedtrianglemeshelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\sphereelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\sphereelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\sphylelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\sphylelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\taperedcapsuleelem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\taperedcapsuleelem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aggregategeom.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\physicscore\\public\\bodysetupcore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\physicscore\\uht\\bodysetupcore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\factories.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\bodysetup.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcparchitecturecommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpbridge.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\unrealmcpbridge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\sockets.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\socketsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\addressinfotypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\http.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\httpmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttprequest.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponse.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\platformhttp.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\windows\\windowsplatformhttp.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\genericplatform\\genericplatformhttp.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4address.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4subnetmask.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4endpoint.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpeditorcommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintcommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintnodecommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpprojectcommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpumgcommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpmapcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\levelstreamingdynamic.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\levelstreamingdynamic.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponentbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponentbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\directionallightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\directionallightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordesc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorcontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorcontainerid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\filter\\worldpartitionactorfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorfilter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstancecollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancenames.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancenames.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesclist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackagegenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionstreaminggeneration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinitparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceviewinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorloaderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorloaderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitioneditorloaderadapter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitioneditorloaderadapter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecelltransformer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecelltransformer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\actorreferencesutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externaldirtyactorstracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\packagesourcecontrolhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangelist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangeliststate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontroloperation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolresultinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolrevision.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\sourcecontrol\\uht\\sourcecontrolhelpers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\cookpackagesplitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayermanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecellinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecellinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\actordatalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\errorhandling\\worldpartitionstreaminggenerationerrorhandler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordatalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayereditorcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\worlddatalayers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstanceproviderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstanceproviderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayeruid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayeruid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externalpackagehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\archivemd5.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancewithasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancewithasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\coreredirects.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\internal\\uobject\\coreredirects\\pm-k.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worlddatalayers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayermanager.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcppathfindingcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationinvokerpriority.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationinvokerpriority.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navagentselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navagentselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ai\\navigationmodifier.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navlinkdefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navlinkdefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdataresolution.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdataresolution.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationrelevantdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyarea.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctreecontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdirtyareascontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\movingwindowaveragefast.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationbounds.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationpath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationpath.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\abstractnavdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\abstractnavdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationtestingactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navpathobserverinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navpathobserverinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navmesh\\recastnavmesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navmesh\\linkgenerationconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\linkgenerationconfig.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navmesh\\navmeshpath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\recastnavmesh.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationtestingactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navlinkcustominterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navareas\\navarea.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navareabase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navareabase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navarea.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navlinkcustominterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navlinkcustomcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navrelevantcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navrelevantcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navlinkcustomcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcppathfindingcommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpvisioncommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptioncomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\genericteamagentinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\genericteamagentinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptiontypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aitypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptiontypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aisubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aisystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\aisystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aisystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptioncomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense_sight.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\mttransactionallysafeaccessdetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense_sight.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig_sight.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig_sight.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisighttargetinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisighttargetinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\pawnsensingcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\pawnsensingcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollectioninstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollectioninstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\atmosphere\\atmosphericfog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\atmosphericfog.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\exponentialheightfog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\exponentialheightfog.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\exponentialheightfogcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\exponentialheightfogstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\exponentialheightfogstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\rendering\\exponentialheightfogdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\exponentialheightfogdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\exponentialheightfogcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\boxcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\boxcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variable.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompilermisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\bpterminal.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\blueprintcompiledstatement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableset.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpvisioncommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpmobacommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestatebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestatebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerstate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aicontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionlistenerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionlistenerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aicontroller.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\blueprint\\aiblueprinthelperlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiblueprinthelperlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\btcompositenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\btnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkey.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\braincomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\airesourceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\airesourceinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\braincomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboardassetprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardassetprovider.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\tasks\\aitask.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitask.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\btnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\btcompositenode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortree.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboardcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkeytype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkeyenums.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardkeyenums.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardkeytype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboarddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboarddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\capsulecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\capsulecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\audiocomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audio\\soundparametercontrollerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundparametercontrollerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerquantizedcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\quartzmetronome.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscription.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscriptiontoken.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzinterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audiocomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcollisioncommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\physicssettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\physicscore\\public\\physicssettingscore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaossolverconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\solvereventfilters.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\chaos\\uht\\solvereventfilters.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\chaos\\uht\\chaossolverconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\physicscore\\uht\\physicssettingscore.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\physicssettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaosmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\pbdrigidssolver.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigidsevolutiongbf.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\chaosperftest.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopedtimers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\character\\charactergroundconstraintcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\constrainthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolution\\indexedconstraintcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolution\\solverconstraintcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\island\\islandmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\poolbackedarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdconstraintcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolution\\solverbody.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolution\\constraintgroupsolver.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolution\\solverbodycontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\containeritemhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\particle\\particleutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\chunkedarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\spatialaccelerationbroadphase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisionconstraintallocator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisioncontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisionkeys.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\particlepairmidphase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\pbdcollisionconstraint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\pbdcollisionconstrainthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\uncheckedarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\gjk.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\capsule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\cylinder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\segment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\epa.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\simplex.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\queue", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.44.35207\\include\\deque", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\simplexvectorized.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\epavectorized.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisionfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\statsdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\ispatialaccelerationcollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigidssoas.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\evolutionresimcache.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\resimcachebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\spatialaccelerationcollisiondetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisiondetector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdcollisionconstraints.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\collisionapplytype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\pbdcollisionsolversettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\buffereddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigidclustering.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\clusterunionmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\clustercreationparameters.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\arrayalgorithm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\externalcollisiondata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigidclusteringtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigidsevolution.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\framework\\debugsubstep.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdjointconstraints.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdjointconstraintdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdconstraintbasedata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\jointproperties.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\spatialaccelerationcollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\island\\islandgroupmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\island\\islandgroup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pendingspatialdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\rewinddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\jointproperties.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\jointproperties.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticleaddimpulses.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticlerule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdparticles.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\particlerule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticleetherdrag.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticleeulerstepvelocity.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticleexternalforces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticlegravity.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticleinitforce.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\perparticlepbdeulerstep.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\ccdutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdsuspensionconstraints.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\collision\\pbdcollisionsolver.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdsuspensionconstraintdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\suspensionproperties.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdrigiddynamicspringconstraints.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\pbdpositionconstraints.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\eventmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\field\\fieldsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\field\\fieldsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarraycollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\geometrycollectionsection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarraytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\geometrycollectionbonenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarraytypevalues.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarraytypevalues.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\geometrycollection\\managedarraytypevalues.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\chaos\\uht\\managedarraycollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\chaos\\uht\\fieldsystemtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\field\\fieldarrayview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\pbdrigidactiveparticlesbuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\physicsproxy\\jointconstraintproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\physicsproxy\\jointconstraintproxyfwd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\jointproperties.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\physicsproxy\\suspensionconstraintproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\physicsobjectexternalinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\chaos\\public\\chaos\\physicsobjectinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\chaos\\uht\\physicsobjectinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\physics\\experimental\\chaosscopedscenelock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\physicscore\\public\\physicsobjectphysicscoreinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\physicsobjectblueprintlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\physicsobjectblueprintlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\physicsconstraintcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\constraintinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\constraintdrives.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\constraintdrives.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\constraintinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\physicsconstraintcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\physicscollisionhandler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\physicscollisionhandler.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpanalyticscommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analytics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsbuildtype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsproviderconfigurationdelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticseventattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticstracer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticspropertystore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticspropertystore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovidermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticssessionsummarymanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticstracer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticsflowtracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\ianalyticsprovideret.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpbalancecommands.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpproceduralmeshcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmesh3.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\boxtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\transformtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\quaternion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\matrixtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\frametypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\geometrytypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\infotypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\compactmaps.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\dynamicvector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\indexutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\intvectortypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\iteratorutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\refcountvector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\util\\smalllistset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\components\\dynamicmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\changes\\meshregionchange.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\interactivetoolsframework\\public\\interactivetoolchange.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\interactivetoolsframework\\uht\\interactivetoolchange.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\components\\basedynamicmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\interactivetoolsframework\\public\\interactivetoolobjects.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\interactivetoolsframework\\uht\\interactivetoolobjects.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\changes\\meshvertexchange.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\meshvertexchange.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\changes\\meshchange.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmeshchangetracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\meshindexmappings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmeshattributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmeshoverlay.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmeshtriangleattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicvertexattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\dynamicmeshsculptlayers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\meshchange.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\changes\\meshreplacementchange.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\meshreplacementchange.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\meshconversion\\public\\meshconversionoptions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\udynamicmesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\udynamicmesh.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\basedynamicmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometryframework\\public\\components\\meshrenderdecomposition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\geometrycore\\public\\dynamicmesh\\meshtangents.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\distancefieldatlas.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\texturelayout3d.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\iassetcompilingmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\derivedmeshdatataskutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\meshutilities\\public\\meshutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshbuild.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\meshmergeutilities\\public\\imeshmergeutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\meshreductioninterface\\public\\imeshreductioninterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\geometryframework\\uht\\dynamicmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\meshconversion\\public\\meshdescriptionbuilder.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpproceduralmeshcommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpmaterialcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialinstanceconstant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialinstanceconstant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialfunctionmateriallayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialfunctioninstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialfunctioninstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialfunctionmateriallayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texture2ddynamic.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texture2ddynamic.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget2d.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texturerendertarget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texturerendertarget2d.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant3vector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant3vector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant4vector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant4vector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressiontexturesample.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressiontexturebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressiontexturebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressiontexturesample.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionmultiply.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionmultiply.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionadd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionadd.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialfactorynew.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialinstanceconstantfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialinstanceconstantfactorynew.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialfunctionfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialfunctionfactorynew.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpmaterialcommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcollisionadvancedcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicalmaterials\\physicalmaterialmask.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\physicalmaterialmask.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\physicscore\\public\\chaos\\chaosphysicalmaterial.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\physicscore\\uht\\chaosphysicalmaterial.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\physicsengine\\chaosblueprintlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\chaosblueprintlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\physics\\experimental\\chaoseventrelay.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\physics\\experimental\\chaoseventtype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\chaoseventtype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\chaoseventrelay.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapephysicalmaterial.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpcollisionadvancedcommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpvisualeffectscommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\pointlightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\locallightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\locallightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pointlightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spotlightcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spotlightcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\skyatmospherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\skyatmospherestatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\skyatmospherestatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\skyatmospherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\volumetriccloudcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\volumetriccloudcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\postprocesscomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\postprocesscomponent.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpvisualeffectscommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcplandscapecommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscape.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\ilandscapesplineinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\ilandscapesplineinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeinfo.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeweightmapusage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeweightmapusage.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texture2darray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texture2darray.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapenanitecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapedataaccess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\staticmeshdescription\\public\\staticmeshattributes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\meshdescription\\public\\meshattributes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapenanitecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeheightfieldcollisioncomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeheightfieldcollisioncomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\actorpartition\\partitionactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\partitionactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeproxy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeblueprintbrushbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeedittypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeedittypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayerrenderer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapetexturehash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapetexturehash.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphblackboard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\generatedtypename.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphpass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rhi\\public\\rhitransientresourceallocator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameters.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphtrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphvalidation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayerrendererstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayertargettypestate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeeditlayertypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeeditlayerrenderer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeblueprintbrushbase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapelayerinfoobject.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapelayerinfoobject.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscape.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapesubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapesubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\landscapeedit.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\instancedfoliageactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\foliagetype_instancedstaticmesh.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\foliagetype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\foliage\\uht\\foliagetype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\foliageinstancedstaticmeshcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\foliage\\uht\\foliageinstancedstaticmeshcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\foliage\\uht\\foliagetype_instancedstaticmesh.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\foliageinstancebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\instancedfoliage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\instances\\instancedplacementhash.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\hashbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\foliage\\public\\instancedfoliagecustomversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ismpartition\\ismpartitionactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ismpartition\\ismpartitionclient.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ismpartitionclient.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ismpartition\\ismpartitioninstancemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ismpartitioninstancemanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ismpartition\\ismcomponentdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ismcomponentdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ismpartitionactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\foliage\\uht\\instancedfoliageactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapestreamingproxy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapestreamingproxy.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapesplinescomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapesplinescomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapetexturepatch.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapepatchcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapepatcheditlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\public\\globalmergelegacysupportutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\globalmergelegacysupportutil.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\landscape\\classes\\landscapeeditlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\landscape\\uht\\landscapeeditlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapepatcheditlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapepatchcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapetexturebackedrendertarget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapepatchutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapepatchutil.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapetexturebackedrendertarget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapetexturepatch.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\source\\landscapepatch\\public\\landscapepatchmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\experimental\\landscapepatch\\intermediate\\build\\win64\\unrealeditor\\inc\\landscapepatch\\uht\\landscapepatchmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayersubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayersubsystem.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcplandscapecommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpbridge.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpcollisionadvancedcommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcplandscapecommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpmaterialcommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcppathfindingcommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpproceduralmeshcommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpvisioncommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpvisualeffectscommands.gen.cpp", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\permoduleinline.gen.cpp", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}
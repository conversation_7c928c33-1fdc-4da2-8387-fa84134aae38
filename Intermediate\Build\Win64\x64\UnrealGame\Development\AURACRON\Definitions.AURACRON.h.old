// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for AURACRON
#pragma once
#include "SharedDefinitions.Engine.Project.ValApi.ValExpApi.Cpp20.h"
#undef AURACRON_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME AURACRON
#define UE_TARGET_NAME AURACRON
#define UE_MODULE_NAME "AURACRON"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define AURACRON_API 

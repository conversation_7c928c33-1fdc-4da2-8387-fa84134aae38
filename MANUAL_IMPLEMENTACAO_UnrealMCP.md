# 📚 MANUAL DE IMPLEMENTAÇÃO - UnrealMCP Tools

## 🎯 **PRINCÍPIOS FUNDAMENTAIS**

### ⚠️ **REGRAS OBRIGATÓRIAS - NUNCA VIOLAR:**
1. **IMPLEMENTAÇÕES REAIS APENAS** - Nunca criar placeholders, simulações ou implementações básicas
2. **SALVAMENTO OBRIGATÓRIO** - Todo comando que cria/modifica assets DEVE salvar no disco
3. **THREAD SAFETY** - Toda operação UE deve executar no Game Thread
4. **APIS MODERNAS UE 5.6** - Usar apenas APIs mais recentes, nunca depreciadas
5. **VALIDAÇÃO ROBUSTA** - Validar todos os parâmetros e tratar erros
6. **LOGS DETALHADOS** - Logar todas as operações para debugging

---

## 🏗️ **ARQUITETURA DO SISTEMA**

### 📁 **Estrutura de Arquivos:**
```
Plugins/UnrealMCP/Source/UnrealMCP/
├── Public/Commands/
│   └── UnrealMCP[Categoria]Commands.h     # Header da categoria
├── Private/Commands/
│   └── UnrealMCP[Categoria]Commands.cpp   # Implementação da categoria
└── UnrealMCP.Build.cs                     # Dependências do módulo

Python/tools/
└── [categoria]_tools.py                   # Interface Python para categoria
```

### 🔄 **Fluxo de Execução:**
```
Python Tool → unreal_mcp_server.py → TCP Socket → MCPServerRunnable → 
UnrealMCPBridge → [Categoria]Commands → Unreal Engine APIs → Disk Save
```

---

## 🛠️ **IMPLEMENTAÇÃO PASSO A PASSO**

### **PASSO 1: CRIAR HEADER (.h)**

```cpp
#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handler class for [Categoria]-related MCP commands
 * [Descrição detalhada da categoria]
 */
class UNREALMCP_API FUnrealMCP[Categoria]Commands
{
public:
    FUnrealMCP[Categoria]Commands();

    // Handle [categoria] commands
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // Specific command handlers
    TSharedPtr<FJsonObject> Handle[ComandoEspecifico](const TSharedPtr<FJsonObject>& Params);
    // ... outros comandos
};
```

### **PASSO 2: IMPLEMENTAR COMANDOS C++ (.cpp)**

#### **🔧 Template de Comando Obrigatório:**

```cpp
#include "Commands/UnrealMCP[Categoria]Commands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Kismet2/KismetEditorUtilities.h"
// ... outros includes necessários

FUnrealMCP[Categoria]Commands::FUnrealMCP[Categoria]Commands()
{
}

TSharedPtr<FJsonObject> FUnrealMCP[Categoria]Commands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandName == TEXT("comando_especifico"))
    {
        return HandleComandoEspecifico(Params);
    }
    // ... outros comandos
    
    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCP[Categoria]Commands::HandleComandoEspecifico(const TSharedPtr<FJsonObject>& Params)
{
    // 1. VALIDAÇÃO DE PARÂMETROS (OBRIGATÓRIO)
    FString ParametroObrigatorio;
    if (!Params->TryGetStringField(TEXT("parametro_obrigatorio"), ParametroObrigatorio))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'parametro_obrigatorio' parameter"));
    }

    // 2. VERIFICAÇÃO DE EXISTÊNCIA (SE APLICÁVEL)
    FString AssetPath = TEXT("/Game/[Categoria]/") + ParametroObrigatorio;
    if (UEditorAssetLibrary::DoesAssetExist(AssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Asset already exists: %s"), *ParametroObrigatorio));
    }

    // 3. IMPLEMENTAÇÃO REAL (NUNCA PLACEHOLDER)
    UPackage* Package = CreatePackage(*AssetPath);
    if (!Package)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create package"));
    }

    // ... implementação específica usando APIs UE 5.6 ...

    // 4. SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(AssetPath, false);
    
    // 5. COMPILAÇÃO (SE BLUEPRINT)
    if (Blueprint)
    {
        FKismetEditorUtilities::CompileBlueprint(Blueprint);
    }

    // 6. RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("name"), ParametroObrigatorio);
    ResultObj->SetStringField(TEXT("path"), AssetPath);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("[Categoria]/") + ParametroObrigatorio + TEXT(".uasset"));
    
    // 7. LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("[Categoria] asset created and saved: %s (Saved: %s)"), *AssetPath, bSaved ? TEXT("Yes") : TEXT("No"));
    
    return ResultObj;
}
```

### **PASSO 3: REGISTRAR NO BRIDGE**

#### **Adicionar no UnrealMCPBridge.h:**
```cpp
// Command handler instances
TSharedPtr<FUnrealMCP[Categoria]Commands> [Categoria]Commands;
```

#### **Adicionar no UnrealMCPBridge.cpp:**
```cpp
// No Initialize():
[Categoria]Commands = MakeShared<FUnrealMCP[Categoria]Commands>();

// No ExecuteCommand():
else if (CommandType == TEXT("comando_especifico") ||
         CommandType == TEXT("outro_comando"))
{
    ResultJson = [Categoria]Commands->HandleCommand(CommandType, Params);
}
```

### **PASSO 4: CRIAR FERRAMENTA PYTHON**

```python
"""
[Categoria] Tools for Unreal MCP.

This module provides tools for [descrição da categoria] in Unreal Engine.
"""

import logging
from typing import Dict, List, Any
from mcp.server.fastmcp import FastMCP, Context

logger = logging.getLogger("UnrealMCP")

def register_[categoria]_tools(mcp: FastMCP):
    """Register [Categoria] tools with the MCP server."""
    
    @mcp.tool()
    def comando_especifico(
        ctx: Context,
        parametro_obrigatorio: str,
        parametro_opcional: str = "default"
    ) -> Dict[str, Any]:
        """[Descrição detalhada do comando]."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("comando_especifico", {
                "parametro_obrigatorio": parametro_obrigatorio,
                "parametro_opcional": parametro_opcional
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Command response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error executing command: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
```

### **PASSO 5: REGISTRAR FERRAMENTA PYTHON**

#### **No unreal_mcp_server.py:**
```python
# Import
from tools.[categoria]_tools import register_[categoria]_tools

# No main():
register_[categoria]_tools(mcp)
```

---

## ✅ **CHECKLIST DE IMPLEMENTAÇÃO**

### **🔍 Antes de Implementar:**
- [ ] Estudei comandos similares existentes
- [ ] Identifiquei APIs UE 5.6 necessárias
- [ ] Verifiquei dependências no Build.cs
- [ ] Planejei estrutura de arquivos/assets

### **🛠️ Durante Implementação:**
- [ ] Header criado com documentação
- [ ] Validação de parâmetros implementada
- [ ] Implementação real (não placeholder)
- [ ] Salvamento obrigatório adicionado
- [ ] Logs detalhados implementados
- [ ] Tratamento de erros robusto

### **🧪 Após Implementação:**
- [ ] Comando registrado no Bridge
- [ ] Ferramenta Python criada
- [ ] Ferramenta registrada no servidor
- [ ] Compilação bem-sucedida
- [ ] Teste funcional realizado
- [ ] Arquivo realmente criado no disco

---

## 🚨 **ERROS COMUNS A EVITAR**

### **❌ NUNCA FAZER:**
1. **Implementações simuladas:** `return {"success": true, "message": "Simulated"}`
2. **Sem salvamento:** Esquecer `UEditorAssetLibrary::SaveAsset()`
3. **APIs depreciadas:** Usar `FindObject` com `ANY_PACKAGE`
4. **Thread unsafe:** Executar APIs UE fora do Game Thread
5. **Sem validação:** Não verificar parâmetros obrigatórios
6. **Sem logs:** Não logar operações importantes

### **✅ SEMPRE FAZER:**
1. **Implementações reais** que criam arquivos no disco
2. **Salvamento obrigatório** após criação/modificação
3. **APIs modernas UE 5.6** apenas
4. **Thread safety** com Game Thread
5. **Validação robusta** de todos os parâmetros
6. **Logs detalhados** para debugging

---

## 📋 **DEPENDÊNCIAS OBRIGATÓRIAS**

### **Build.cs - Módulos Essenciais:**
```csharp
PublicDependencyModuleNames.AddRange(new string[]
{
    "Core", "CoreUObject", "Engine", "InputCore",
    "Networking", "Sockets", "HTTP", "Json", "JsonUtilities",
    "ImageCore", "ImageWrapper", "RenderCore", "RHI"
});

PrivateDependencyModuleNames.AddRange(new string[]
{
    "UnrealEd", "EditorScriptingUtilities", "EditorSubsystem",
    "Slate", "SlateCore", "UMG", "Kismet", "KismetCompiler",
    "BlueprintGraph", "Projects", "AssetRegistry"
});

if (Target.bBuildEditor == true)
{
    PrivateDependencyModuleNames.AddRange(new string[]
    {
        "PropertyEditor", "ToolMenus", 
        "BlueprintEditorLibrary", "UMGEditor"
    });
}
```

---

---

## 🎯 **EXEMPLOS PRÁTICOS**

### **Exemplo 1: Comando de Criação de Material**

#### **C++ Implementation:**
```cpp
TSharedPtr<FJsonObject> FUnrealMCPMaterialCommands::HandleCreateMaterial(const TSharedPtr<FJsonObject>& Params)
{
    // 1. Validação
    FString MaterialName;
    if (!Params->TryGetStringField(TEXT("name"), MaterialName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // 2. Verificação de existência
    FString MaterialPath = TEXT("/Game/Materials/") + MaterialName;
    if (UEditorAssetLibrary::DoesAssetExist(MaterialPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Material already exists: %s"), *MaterialName));
    }

    // 3. Criação real do material
    UPackage* Package = CreatePackage(*MaterialPath);
    UMaterialFactoryNew* MaterialFactory = NewObject<UMaterialFactoryNew>();

    UMaterial* NewMaterial = Cast<UMaterial>(MaterialFactory->FactoryCreateNew(
        UMaterial::StaticClass(), Package, FName(*MaterialName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewMaterial)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create material"));
    }

    // 4. Configuração específica
    NewMaterial->BaseColor.Expression = nullptr; // Configurar conforme necessário

    // 5. Salvamento obrigatório
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewMaterial);
    bool bSaved = UEditorAssetLibrary::SaveAsset(MaterialPath, false);

    // 6. Resposta detalhada
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("name"), MaterialName);
    ResultObj->SetStringField(TEXT("path"), MaterialPath);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    UE_LOG(LogTemp, Log, TEXT("Material created and saved: %s (Saved: %s)"), *MaterialPath, bSaved ? TEXT("Yes") : TEXT("No"));
    return ResultObj;
}
```

#### **Python Interface:**
```python
@mcp.tool()
def create_material(
    ctx: Context,
    name: str,
    base_color: List[float] = [1.0, 1.0, 1.0],
    metallic: float = 0.0,
    roughness: float = 0.5
) -> Dict[str, Any]:
    """Create a new Material asset with specified properties."""
    from unreal_mcp_server import get_unreal_connection

    try:
        unreal = get_unreal_connection()
        if not unreal:
            return {"success": False, "message": "Failed to connect to Unreal Engine"}

        response = unreal.send_command("create_material", {
            "name": name,
            "base_color": base_color,
            "metallic": metallic,
            "roughness": roughness
        })

        return response or {}

    except Exception as e:
        return {"success": False, "message": f"Error creating material: {e}"}
```

### **Exemplo 2: Comando de Modificação de Mesh**

#### **Padrão para Modificação de Assets Existentes:**
```cpp
TSharedPtr<FJsonObject> FUnrealMCPMeshCommands::HandleModifyStaticMesh(const TSharedPtr<FJsonObject>& Params)
{
    // 1. Validação
    FString MeshPath;
    if (!Params->TryGetStringField(TEXT("mesh_path"), MeshPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'mesh_path' parameter"));
    }

    // 2. Carregar asset existente
    UStaticMesh* StaticMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    if (!StaticMesh)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Static mesh not found: %s"), *MeshPath));
    }

    // 3. Modificação real
    if (Params->HasField(TEXT("collision_enabled")))
    {
        bool bCollisionEnabled = Params->GetBoolField(TEXT("collision_enabled"));
        StaticMesh->GetBodySetup()->CollisionTraceFlag = bCollisionEnabled ? CTF_UseComplexAsSimple : CTF_UseDefault;
    }

    // 4. Marcar como modificado e salvar
    StaticMesh->MarkPackageDirty();
    bool bSaved = UEditorAssetLibrary::SaveAsset(MeshPath, false);

    // 5. Resposta
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("mesh_path"), MeshPath);
    ResultObj->SetBoolField(TEXT("modified"), true);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);

    return ResultObj;
}
```

---

## 🔧 **PADRÕES AVANÇADOS**

### **Thread Safety Avançado:**
```cpp
// Para operações que precisam de callback
void ExecuteWithCallback(TFunction<void()> Callback)
{
    if (IsInGameThread())
    {
        Callback();
    }
    else
    {
        AsyncTask(ENamedThreads::GameThread, [Callback]()
        {
            check(IsInGameThread());
            Callback();
        });
    }
}
```

### **Validação Robusta de Parâmetros:**
```cpp
// Função helper para validação
bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, const TArray<FString>& RequiredFields, FString& OutError)
{
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            OutError = FString::Printf(TEXT("Missing required parameter: %s"), *Field);
            return false;
        }
    }
    return true;
}

// Uso:
FString ValidationError;
if (!ValidateRequiredParams(Params, {TEXT("name"), TEXT("type")}, ValidationError))
{
    return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
}
```

### **Tratamento de Erros Avançado:**
```cpp
TSharedPtr<FJsonObject> HandleCommandWithErrorHandling(const TSharedPtr<FJsonObject>& Params)
{
    try
    {
        // Implementação principal
        return ExecuteMainLogic(Params);
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("C++ Exception: %s"), UTF8_TO_TCHAR(e.what()));
        UE_LOG(LogTemp, Error, TEXT("%s"), *ErrorMsg);
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        FString ErrorMsg = TEXT("Unknown C++ exception occurred");
        UE_LOG(LogTemp, Error, TEXT("%s"), *ErrorMsg);
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
}
```

---

## 📊 **MÉTRICAS DE QUALIDADE**

### **Checklist de Qualidade PRODUCTION READY:**
- [ ] **Funcionalidade Real:** Cria arquivos físicos no disco
- [ ] **Thread Safety:** Executa no Game Thread
- [ ] **APIs Modernas:** Usa apenas APIs UE 5.6
- [ ] **Validação Completa:** Valida todos os parâmetros
- [ ] **Tratamento de Erros:** Captura e trata todas as exceções
- [ ] **Logs Detalhados:** Loga todas as operações importantes
- [ ] **Salvamento Garantido:** Salva assets no disco
- [ ] **Compilação Limpa:** Compila sem warnings
- [ ] **Documentação:** Código bem documentado
- [ ] **Testes Funcionais:** Testado em cenários reais

---

---

## 🚨 **TROUBLESHOOTING COMUM**

### **Problema: "Asset não está sendo salvo"**
```cpp
// ❌ ERRADO - Sem salvamento
Package->MarkPackageDirty();
return ResultObj;

// ✅ CORRETO - Com salvamento obrigatório
Package->MarkPackageDirty();
FAssetRegistryModule::AssetCreated(Asset);
bool bSaved = UEditorAssetLibrary::SaveAsset(AssetPath, false);
ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
```

### **Problema: "Thread safety violation"**
```cpp
// ❌ ERRADO - Executando diretamente
UBlueprint* Blueprint = CreateBlueprint();

// ✅ CORRETO - No Game Thread
AsyncTask(ENamedThreads::GameThread, [this]()
{
    check(IsInGameThread());
    UBlueprint* Blueprint = CreateBlueprint();
});
```

### **Problema: "API depreciada"**
```cpp
// ❌ ERRADO - API depreciada
UClass* Class = FindObject<UClass>(ANY_PACKAGE, *ClassName);

// ✅ CORRETO - API moderna UE 5.6
UClass* Class = StaticLoadClass(UObject::StaticClass(), nullptr, *ClassName);
```

### **Problema: "Comando não encontrado no Bridge"**
```cpp
// Verificar se está registrado em UnrealMCPBridge.cpp:
else if (CommandType == TEXT("seu_novo_comando"))
{
    ResultJson = SuaCategoriaCommands->HandleCommand(CommandType, Params);
}
```

---

## 🎯 **MELHORES PRÁTICAS**

### **1. Nomenclatura Consistente:**
- **Comandos C++:** `HandleCreateBlueprint`, `HandleAddComponent`
- **Comandos Python:** `create_blueprint`, `add_component`
- **Parâmetros:** `blueprint_name`, `component_type` (snake_case)

### **2. Estrutura de Resposta Padrão:**
```cpp
TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
ResultObj->SetStringField(TEXT("name"), AssetName);
ResultObj->SetStringField(TEXT("path"), AssetPath);
ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved);
ResultObj->SetStringField(TEXT("full_path"), FullDiskPath);
ResultObj->SetStringField(TEXT("type"), TEXT("Blueprint"));
```

### **3. Logs Estruturados:**
```cpp
UE_LOG(LogTemp, Log, TEXT("[%s] %s created and saved: %s (Saved: %s)"),
    TEXT("CategoryName"), TEXT("AssetType"), *AssetPath, bSaved ? TEXT("Yes") : TEXT("No"));
```

### **4. Validação em Camadas:**
```cpp
// Camada 1: Parâmetros obrigatórios
if (!Params->TryGetStringField(TEXT("name"), Name))
    return CreateErrorResponse(TEXT("Missing 'name' parameter"));

// Camada 2: Validação de formato
if (Name.IsEmpty() || Name.Contains(TEXT(" ")))
    return CreateErrorResponse(TEXT("Invalid name format"));

// Camada 3: Validação de contexto
if (UEditorAssetLibrary::DoesAssetExist(AssetPath))
    return CreateErrorResponse(TEXT("Asset already exists"));
```

### **5. Cleanup Automático:**
```cpp
// Sempre limpar recursos em caso de erro
if (!NewAsset)
{
    if (Package)
    {
        Package->ClearFlags(RF_Standalone);
        Package->RemoveFromRoot();
    }
    return CreateErrorResponse(TEXT("Failed to create asset"));
}
```

---

## 📋 **TEMPLATE COMPLETO DE NOVA CATEGORIA**

### **1. Header Template (UnrealMCPExampleCommands.h):**
```cpp
#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handler class for Example-related MCP commands
 * Handles creation and manipulation of example assets
 */
class UNREALMCP_API FUnrealMCPExampleCommands
{
public:
    FUnrealMCPExampleCommands();

    // Handle example commands
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // Specific command handlers
    TSharedPtr<FJsonObject> HandleCreateExample(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleModifyExample(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleDeleteExample(const TSharedPtr<FJsonObject>& Params);
};
```

### **2. Implementation Template (UnrealMCPExampleCommands.cpp):**
```cpp
#include "Commands/UnrealMCPExampleCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
// ... outros includes necessários

FUnrealMCPExampleCommands::FUnrealMCPExampleCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPExampleCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandName == TEXT("create_example"))
    {
        return HandleCreateExample(Params);
    }
    else if (CommandName == TEXT("modify_example"))
    {
        return HandleModifyExample(Params);
    }
    else if (CommandName == TEXT("delete_example"))
    {
        return HandleDeleteExample(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown command: %s"), *CommandName));
}

// Implementar cada Handle... seguindo o padrão obrigatório
```

### **3. Python Template (example_tools.py):**
```python
"""
Example Tools for Unreal MCP.

This module provides tools for creating and manipulating example assets in Unreal Engine.
"""

import logging
from typing import Dict, List, Any
from mcp.server.fastmcp import FastMCP, Context

logger = logging.getLogger("UnrealMCP")

def register_example_tools(mcp: FastMCP):
    """Register Example tools with the MCP server."""

    @mcp.tool()
    def create_example(
        ctx: Context,
        name: str,
        example_type: str = "default"
    ) -> Dict[str, Any]:
        """Create a new example asset."""
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("create_example", {
                "name": name,
                "example_type": example_type
            })

            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}

            logger.info(f"Example creation response: {response}")
            return response or {}

        except Exception as e:
            error_msg = f"Error creating example: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
```

---

## 🏆 **CERTIFICAÇÃO DE QUALIDADE**

### **Para considerar uma implementação PRODUCTION READY, ela deve:**

✅ **Funcionalidade Real:** Criar arquivos físicos verificáveis no disco
✅ **Thread Safety:** Executar todas as operações UE no Game Thread
✅ **APIs Modernas:** Usar exclusivamente APIs UE 5.6 não-depreciadas
✅ **Salvamento Garantido:** Salvar todos os assets criados/modificados
✅ **Validação Robusta:** Validar todos os parâmetros de entrada
✅ **Tratamento de Erros:** Capturar e tratar todas as exceções possíveis
✅ **Logs Detalhados:** Logar todas as operações para debugging
✅ **Compilação Limpa:** Compilar sem erros ou warnings
✅ **Documentação Completa:** Código bem documentado e comentado
✅ **Testes Funcionais:** Testado em cenários reais de uso

---

**🎯 CONCLUSÃO: Este manual garante implementações PRODUCTION READY, robustas e funcionais. Seguindo todos os padrões obrigatórios, você criará ferramentas MCP que realmente funcionam e criam arquivos reais no Unreal Engine 5.6!**

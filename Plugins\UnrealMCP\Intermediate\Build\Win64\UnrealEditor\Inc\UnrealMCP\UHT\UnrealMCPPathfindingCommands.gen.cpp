// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPPathfindingCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPPathfindingCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FMultilayerPath();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FMultilayerPath ***************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FMultilayerPath;
class UScriptStruct* FMultilayerPath::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FMultilayerPath.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FMultilayerPath.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FMultilayerPath, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("MultilayerPath"));
	}
	return Z_Registration_Info_UScriptStruct_FMultilayerPath.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FMultilayerPath_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Multilayer path structure for complex pathfinding\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Multilayer path structure for complex pathfinding" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathPoints_MetaData[] = {
		{ "Category", "MultilayerPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Path points across multiple layers */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Path points across multiple layers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndices_MetaData[] = {
		{ "Category", "MultilayerPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Layer indices for each path point */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Layer indices for each path point" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionTypes_MetaData[] = {
		{ "Category", "MultilayerPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition types between points */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition types between points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalCost_MetaData[] = {
		{ "Category", "MultilayerPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total path cost */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total path cost" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "MultilayerPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether path is valid */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPPathfindingCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether path is valid" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PathPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndices_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerIndices;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TransitionTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TransitionTypes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalCost;
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FMultilayerPath>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_PathPoints_Inner = { "PathPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_PathPoints = { "PathPoints", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMultilayerPath, PathPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathPoints_MetaData), NewProp_PathPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_LayerIndices_Inner = { "LayerIndices", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_LayerIndices = { "LayerIndices", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMultilayerPath, LayerIndices), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndices_MetaData), NewProp_LayerIndices_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TransitionTypes_Inner = { "TransitionTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TransitionTypes = { "TransitionTypes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMultilayerPath, TransitionTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionTypes_MetaData), NewProp_TransitionTypes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TotalCost = { "TotalCost", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FMultilayerPath, TotalCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalCost_MetaData), NewProp_TotalCost_MetaData) };
void Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FMultilayerPath*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FMultilayerPath), &Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FMultilayerPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_PathPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_PathPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_LayerIndices_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_LayerIndices,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TransitionTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TransitionTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_TotalCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewProp_bIsValid,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMultilayerPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FMultilayerPath_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"MultilayerPath",
	Z_Construct_UScriptStruct_FMultilayerPath_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMultilayerPath_Statics::PropPointers),
	sizeof(FMultilayerPath),
	alignof(FMultilayerPath),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FMultilayerPath_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FMultilayerPath_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FMultilayerPath()
{
	if (!Z_Registration_Info_UScriptStruct_FMultilayerPath.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FMultilayerPath.InnerSingleton, Z_Construct_UScriptStruct_FMultilayerPath_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FMultilayerPath.InnerSingleton;
}
// ********** End ScriptStruct FMultilayerPath *****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FMultilayerPath::StaticStruct, Z_Construct_UScriptStruct_FMultilayerPath_Statics::NewStructOps, TEXT("MultilayerPath"), &Z_Registration_Info_UScriptStruct_FMultilayerPath, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FMultilayerPath), 3890410405U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h__Script_UnrealMCP_655743433(TEXT("/Script/UnrealMCP"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPPathfindingCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

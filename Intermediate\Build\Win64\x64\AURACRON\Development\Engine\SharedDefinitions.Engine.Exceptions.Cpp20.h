// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared Definitions for Engine.Exceptions.Cpp20
#pragma once
#define IS_PROGRAM 0
#define UE_GAME 1
#define USE_SHADER_COMPILER_WORKER_TRACE 0
#define UE_REFERENCE_COLLECTOR_REQUIRE_OBJECTPTR 1
#define WITH_VERSE_VM 0
#define ENABLE_PGO_PROFILE 0
#define USE_VORBIS_FOR_STREAMING 1
#define USE_XMA2_FOR_STREAMING 1
#define WITH_DEV_AUTOMATION_TESTS 1
#define WITH_PERF_AUTOMATION_TESTS 1
#define WITH_LOW_LEVEL_TESTS 0
#define EXPLICIT_TESTS_TARGET 0
#define WITH_TESTS 1
#define UNICODE 1
#define _UNICODE 1
#define __UNREAL__ 1
#define IS_MONOLITHIC 1
#define IS_MERGEDMODULES 0
#define WITH_ENGINE 1
#define WITH_UNREAL_DEVELOPER_TOOLS 1
#define WITH_UNREAL_TARGET_DEVELOPER_TOOLS 1
#define WITH_APPLICATION_CORE 1
#define WITH_COREUOBJECT 1
#define UE_TRACE_ENABLED 1
#define UE_TRACE_FORCE_ENABLED 0
#define WITH_VERSE 1
#define UE_USE_VERSE_PATHS 1
#define WITH_VERSE_BPVM 1
#define USE_STATS_WITHOUT_ENGINE 0
#define WITH_PLUGIN_SUPPORT 0
#define WITH_ACCESSIBILITY 1
#define WITH_PERFCOUNTERS 0
#define WITH_FIXED_TIME_STEP_SUPPORT 1
#define USE_LOGGING_IN_SHIPPING 0
#define ALLOW_CONSOLE_IN_SHIPPING 0
#define ALLOW_PROFILEGPU_IN_TEST 0
#define ALLOW_PROFILEGPU_IN_SHIPPING 0
#define WITH_LOGGING_TO_MEMORY 0
#define USE_CACHE_FREED_OS_ALLOCS 1
#define USE_CHECKS_IN_SHIPPING 0
#define USE_UTF8_TCHARS 0
#define USE_ESTIMATED_UTCNOW 0
#define UE_ALLOW_EXEC_COMMANDS_IN_SHIPPING 1
#define WITH_EDITOR 0
#define WITH_EDITORONLY_DATA 0
#define WITH_CLIENT_CODE 1
#define WITH_SERVER_CODE 1
#define UE_FNAME_OUTLINE_NUMBER 0
#define WITH_PUSH_MODEL 0
#define WITH_CEF3 1
#define WITH_LIVE_CODING 1
#define WITH_CPP_MODULES 0
#define WITH_CPP_COROUTINES 0
#define WITH_PROCESS_PRIORITY_CONTROL 0
#define UBT_MODULE_MANIFEST "UnrealGame.modules"
#define UBT_MODULE_MANIFEST_DEBUGGAME "UnrealGame-Win64-DebugGame.modules"
#define UBT_COMPILED_PLATFORM Win64
#define UBT_COMPILED_TARGET Game
#define UE_APP_NAME "UnrealGame"
#define UE_WARNINGS_AS_ERRORS 0
#define UE_ENGINE_DIRECTORY "../../../../Program Files/Epic Games/UE_5.6/Engine/"
#define FORCE_ANSI_ALLOCATOR 0
#define USE_MALLOC_BINNED2 1
#define USE_MALLOC_BINNED3 0
#define NDIS_MINIPORT_MAJOR_VERSION 0
#define WIN32 1
#define _WIN32_WINNT 0x0601
#define WINVER 0x0601
#define PLATFORM_WINDOWS 1
#define PLATFORM_MICROSOFT 1
#define OVERRIDE_PLATFORM_HEADER_NAME Windows
#define RHI_RAYTRACING 1
#define WINDOWS_MAX_NUM_TLS_SLOTS 2048
#define WINDOWS_MAX_NUM_THREADS_WITH_TLS_SLOTS 512
#define NDEBUG 1
#define UE_BUILD_DEVELOPMENT 1
#define UE_IS_ENGINE_MODULE 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 0
#define UE_VALIDATE_EXPERIMENTAL_API 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_CHAOS_VISUAL_DEBUGGER 1
#define GPUPARTICLE_LOCAL_VF_ONLY 0
#define WITH_ODSC 1
#define UE_WITH_IRIS 1
#define ENGINE_API 
#define UE_MEMORY_TAGS_TRACE_ENABLED 1
#define UE_ENABLE_ICU 1
#define WITH_ADDITIONAL_CRASH_CONTEXTS 1
#define PLATFORM_SUPPORTS_PLATFORM_EVENTS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_VIRTUAL_MEMORY_HOOKS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_MODULE_DIAGNOSTICS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_CALLSTACK 1
#define UE_MEMORY_TRACE_AVAILABLE 1
#define WITH_MALLOC_STOMP 1
#define UE_MERGED_MODULES 0
#define CORE_API 
#define GSL_NO_IOSTREAMS 1
#define TRACELOG_API 
#define AUTORTFM_API 
#define IMAGECORE_API 
#define PLATFORM_MAX_LOCAL_PLAYERS 0
#define COREONLINE_API 
#define WITH_VERSE_COMPILER 0
#define COREUOBJECT_API 
#define COREPRECISEFP_API 
#define FIELDNOTIFICATION_API 
#define NETCORE_API 
#define NETCOMMON_API 
#define JSON_API 
#define JSONUTILITIES_API 
#define WITH_FREETYPE 1
#define SLATECORE_API 
#define DEVELOPERSETTINGS_API 
#define INPUTCORE_API 
#define UE_WINDOWS_USING_UIA 1
#define APPLICATIONCORE_API 
#define WITH_MGPU 1
#define RHI_WANT_RESOURCE_INFO 1
#define RHI_API 
#define SLATE_API 
#define WITH_UNREALPNG 1
#define WITH_UNREALJPEG 1
#define WITH_LIBJPEGTURBO 1
#define WITH_UNREALEXR 1
#define WITH_UNREALEXR_MINIMAL 0
#define IMAGEWRAPPER_API 
#define WITH_LIBTIFF 1
#define MESSAGING_API 
#define MESSAGINGCOMMON_API 
#define RENDERCORE_API 
#define OPENGLDRV_API 
#define ANALYTICSET_API 
#define ANALYTICS_API 
#define SOCKETS_PACKAGE 1
#define SOCKETS_API 
#define ASSETREGISTRY_API 
#define ENGINEMESSAGES_API 
#define ENGINESETTINGS_API 
#define SYNTHBENCHMARK_API 
#define GAMEPLAYTAGS_API 
#define PACKETHANDLER_API 
#define RELIABILITYHANDLERCOMPONENT_API 
#define AUDIOPLATFORMCONFIGURATION_API 
#define MESHDESCRIPTION_API 
#define STATICMESHDESCRIPTION_API 
#define SKELETALMESHDESCRIPTION_API 
#define ANIMATIONCORE_API 
#define PAKFILE_API 
#define RSA_API 
#define NETWORKREPLAYSTREAMING_API 
#define PHYSICSCORE_API 
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define CHAOS_CHECKED 0
#define CHAOS_DEBUG_NAME 1
#define CHAOSCORE_API 
#define INTEL_ISPC 1
#define CHAOS_MEMORY_TRACKING 0
#define USE_ISPC_OPTIMIZED_VECTORGATHER 0
#define CHAOS_API 
#define VORONOI_API 
#define GEOMETRYCORE_API 
#define CHAOSVDRUNTIME_API 
#define NNE_API 
#define SIGNALPROCESSING_API 
#define STATESTREAM_API 
#define AUDIOEXTENSIONS_API 
#define AUDIOMIXERCORE_API 
#define AUDIOMIXER_API 
#define TARGETPLATFORM_API 
#define TEXTUREFORMAT_API 
#define DESKTOPPLATFORM_API 
#define AUDIOLINKENGINE_API 
#define AUDIOLINKCORE_API 
#define COOKONTHEFLY_API 
#define NETWORKING_API 
#define IOSTOREONDEMANDCORE_API 
#define CLOTHINGSYSTEMRUNTIMEINTERFACE_API 
#define IRISCORE_API 
#define IOSTOREONDEMAND_API 
#define MOVIESCENECAPTURE_API 
#define RENDERER_API 
#define TYPEDELEMENTFRAMEWORK_API 
#define TYPEDELEMENTRUNTIME_API 

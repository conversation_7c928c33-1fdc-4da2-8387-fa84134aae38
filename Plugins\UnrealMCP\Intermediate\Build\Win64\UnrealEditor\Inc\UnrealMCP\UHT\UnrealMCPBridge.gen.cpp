// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "UnrealMCPBridge.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPBridge() {}

// ********** Begin Cross Module References ********************************************************
EDITORSUBSYSTEM_API UClass* Z_Construct_UClass_UEditorSubsystem();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBridge();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBridge_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UUnrealMCPBridge *********************************************************
void UUnrealMCPBridge::StaticRegisterNativesUUnrealMCPBridge()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPBridge;
UClass* UUnrealMCPBridge::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPBridge;
	if (!Z_Registration_Info_UClass_UUnrealMCPBridge.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPBridge"),
			Z_Registration_Info_UClass_UUnrealMCPBridge.InnerSingleton,
			StaticRegisterNativesUUnrealMCPBridge,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPBridge.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPBridge_NoRegister()
{
	return UUnrealMCPBridge::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPBridge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Editor subsystem for MCP Bridge\n * Handles communication between external tools and the Unreal Editor\n * through a TCP socket connection. Commands are received as JSON and\n * routed to appropriate command handlers.\n */" },
#endif
		{ "IncludePath", "UnrealMCPBridge.h" },
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Editor subsystem for MCP Bridge\nHandles communication between external tools and the Unreal Editor\nthrough a TCP socket connection. Commands are received as JSON and\nrouted to appropriate command handlers." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralMeshCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ArchitectureCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollisionAdvancedCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualEffectsCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeCommands_MetaData[] = {
		{ "ModuleRelativePath", "Public/UnrealMCPBridge.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ProceduralMeshCommands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MaterialCommands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ArchitectureCommands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CollisionAdvancedCommands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VisualEffectsCommands;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LandscapeCommands;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPBridge>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_ProceduralMeshCommands = { "ProceduralMeshCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, ProceduralMeshCommands), Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralMeshCommands_MetaData), NewProp_ProceduralMeshCommands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_MaterialCommands = { "MaterialCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, MaterialCommands), Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialCommands_MetaData), NewProp_MaterialCommands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_ArchitectureCommands = { "ArchitectureCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, ArchitectureCommands), Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ArchitectureCommands_MetaData), NewProp_ArchitectureCommands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_CollisionAdvancedCommands = { "CollisionAdvancedCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, CollisionAdvancedCommands), Z_Construct_UClass_UUnrealMCPCollisionAdvancedCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollisionAdvancedCommands_MetaData), NewProp_CollisionAdvancedCommands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_VisualEffectsCommands = { "VisualEffectsCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, VisualEffectsCommands), Z_Construct_UClass_UUnrealMCPVisualEffectsCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualEffectsCommands_MetaData), NewProp_VisualEffectsCommands_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_LandscapeCommands = { "LandscapeCommands", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPBridge, LandscapeCommands), Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeCommands_MetaData), NewProp_LandscapeCommands_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPBridge_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_ProceduralMeshCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_MaterialCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_ArchitectureCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_CollisionAdvancedCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_VisualEffectsCommands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPBridge_Statics::NewProp_LandscapeCommands,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBridge_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPBridge_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UEditorSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBridge_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPBridge_Statics::ClassParams = {
	&UUnrealMCPBridge::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPBridge_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBridge_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBridge_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPBridge_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPBridge()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPBridge.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPBridge.OuterSingleton, Z_Construct_UClass_UUnrealMCPBridge_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPBridge.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPBridge);
// ********** End Class UUnrealMCPBridge ***********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h__Script_UnrealMCP_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPBridge, UUnrealMCPBridge::StaticClass, TEXT("UUnrealMCPBridge"), &Z_Registration_Info_UClass_UUnrealMCPBridge, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPBridge), 22545586U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h__Script_UnrealMCP_1978929874(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h__Script_UnrealMCP_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

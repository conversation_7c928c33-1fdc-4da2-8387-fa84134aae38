# AURACRON - Documento Técnico de Design do Mapa

## 1. Visão Geral do Mapa

### 1.1 Conceito Central
O mapa do AURACRON é uma inovação revolucionária no gênero MOBA, apresentando **três camadas verticais interconectadas** que representam diferentes reinos dimensionais:

- **Planície Radiante** (Camada Inferior)
- **Firmamento Zephyr** (Camada Média)
- **Abismo Umbral** (Camada Superior)

### 1.2 Filosofia de Design
Baseado nas melhores práticas de design MOBA identificadas em Dota 2 e League of Legends, o mapa do AURACRON incorpora:

- **Simetria Estratégica**: Garantindo equilíbrio competitivo entre as equipes
- **Assimetria Tática**: Criando variações sutis que favorecem diferentes estratégias
- **Profundidade Vertical**: Adicionando uma dimensão completamente nova ao gameplay MOBA
- **Conectividade Dinâmica**: Permitindo transições fluidas entre camadas

## 2. Especificações Técnicas Gerais

### 2.1 Dimensões do Mapa
**Baseado nas referências de League of Legends (16000x16000 unidades) e Dota 2 (17664x16643 unidades)**

- **Dimensões por Camada**: 18000 x 18000 unidades
- **Altura Total**: 6000 unidades (2000 por camada)
- **Unidade de Medida**: 1 unidade = 1 centímetro (padrão LoL)
- **Tamanho Real**: 180m x 180m por camada

### 2.2 Sistema de Coordenadas
```
Camada Superior (Abismo Umbral): Z = 4000-6000
Camada Média (Firmamento Zephyr): Z = 2000-4000  
Camada Inferior (Planície Radiante): Z = 0-2000
```

### 2.3 Collision System
**Baseado no sistema de Dota 2 com melhorias**

- **Collision Size Padrão**: 24 unidades (heróis)
- **Bound Radius**: 32 unidades (heróis)
- **Torres**: Collision Size 144, Bound Radius 200
- **Estruturas Principais**: Collision Size 288, Bound Radius 400
- **Creeps**: Collision Size 16, Bound Radius 24

## 3. Estrutura das Camadas

### 3.1 Planície Radiante (Camada Inferior)
**Tema**: Reino da Luz e Crescimento

#### Layout das Lanes
- **3 Lanes Principais**: Top, Mid, Bot
- **Comprimento das Lanes**:
  - Top Lane: 4200 unidades
  - Mid Lane: 3800 unidades (mais curta para rotações rápidas)
  - Bot Lane: 4000 unidades

#### Jungle Design
- **4 Quadrantes de Jungle**
- **Camps Neutros**: 12 camps por lado (24 total)
- **Buffs Principais**: 
  - Buff da Luz (equivalente ao Blue Buff)
  - Buff do Crescimento (equivalente ao Red Buff)

#### Objetivos Únicos
- **Guardião da Aurora**: Boss central (equivalente ao Baron/Roshan)
- **Cristais de Luz**: Objetivos menores (equivalente aos Dragões)
- **Torres de Cristal**: 11 torres por equipe

### 3.2 Firmamento Zephyr (Camada Média)
**Tema**: Reino do Vento e Movimento

#### Layout Modificado
- **2 Lanes Principais**: Correntes de Vento Norte e Sul
- **Zona Central**: Grande área aberta para combates em equipe
- **Plataformas Flutuantes**: Áreas elevadas conectadas por pontes de vento

#### Mecânicas Únicas
- **Correntes de Vento**: Aceleram movimento em direções específicas
- **Plataformas Móveis**: Mudam de posição periodicamente
- **Vórtices**: Portais para outras camadas

#### Objetivos Únicos
- **Senhor dos Ventos**: Boss que concede mobilidade aumentada
- **Esferas de Tempestade**: Objetivos que concedem controle de área

### 3.3 Abismo Umbral (Camada Superior)
**Tema**: Reino das Sombras e Mistério

#### Layout Labiríntico
- **Múltiplos Caminhos**: Rede complexa de corredores sombrios
- **Zonas de Sombra**: Áreas com visibilidade reduzida
- **Câmaras Secretas**: Salas ocultas com recompensas especiais

#### Mecânicas Únicas
- **Névoa Sombria**: Reduz alcance de visão
- **Portais Sombrios**: Teletransporte instantâneo
- **Armadilhas Umbrais**: Perigos ambientais

#### Objetivos Únicos
- **Arqui-Sombra**: Boss final que pode influenciar todas as camadas
- **Fragmentos Umbrais**: Recursos raros para itens poderosos

## 4. Sistema de Conectividade Vertical

### 4.1 Portais Primários
**Localizações Fixas**
- **4 Portais por Camada**: Nos cantos e centro do mapa
- **Tempo de Ativação**: 3 segundos
- **Cooldown**: 30 segundos por jogador
- **Capacidade**: 1 jogador por vez

### 4.2 Elevadores Místicos
**Transporte de Equipe**
- **2 Elevadores por Lado**: Próximos às bases
- **Capacidade**: Equipe completa (5 jogadores)
- **Tempo de Viagem**: 5 segundos
- **Vulnerabilidade**: Podem ser atacados durante o transporte

### 4.3 Pontes Dimensionais
**Conexões Temporárias**
- **Ativadas por Objetivos**: Aparecem após conquistar certos objetivos
- **Duração**: 2 minutos
- **Localização**: Varia baseada no objetivo conquistado

## 5. Pathfinding e Navegação

### 5.1 Algoritmo A* Multicamada
**Implementação Técnica**
```
class MultilayerPathfinding {
    calculatePath(start, end, allowedLayers) {
        // Considera custos de movimento vertical
        // Prioriza caminhos dentro da mesma camada
        // Calcula rotas de escape entre camadas
    }
}
```

### 5.2 Custos de Movimento
- **Movimento Horizontal**: 1 unidade de custo
- **Transição Vertical (Portal)**: 5 unidades de custo
- **Transição Vertical (Elevador)**: 3 unidades de custo
- **Áreas Especiais**: Custos variáveis baseados no terreno

### 5.3 Navegação Assistida
**Para Dispositivos Mobile**
- **Auto-pathfinding**: Calcula automaticamente rotas entre camadas
- **Indicadores Visuais**: Setas e trilhas luminosas
- **Sugestões de Rota**: IA sugere caminhos otimizados

## 6. Sistema de Visão Multicamada

### 6.1 Alcance de Visão
**Baseado nas referências de LoL e Dota 2**
- **Visão Padrão**: 1200 unidades na mesma camada
- **Visão Vertical**: 400 unidades para camadas adjacentes
- **Wards**: 1600 unidades na mesma camada, 600 verticalmente

### 6.2 Fog of War Tridimensional
- **Fog por Camada**: Cada camada mantém seu próprio fog of war
- **Visão Compartilhada**: Informações limitadas entre camadas
- **Revelação Temporal**: Alguns objetivos revelam múltiplas camadas

### 6.3 Mecânicas de Stealth
- **Invisibilidade Planar**: Funciona apenas na camada atual
- **Camuflagem Dimensional**: Permite movimento entre camadas sem detecção
- **Detecção Verdadeira**: Revela inimigos em todas as camadas próximas

## 7. Balanceamento e Simetria

### 7.1 Simetria Estratégica
**Garantindo Fairness Competitivo**
- **Recursos Espelhados**: Cada equipe tem acesso aos mesmos tipos de recursos
- **Distâncias Equivalentes**: Tempos de rotação similares entre objetivos
- **Oportunidades Iguais**: Acesso balanceado a todas as camadas

### 7.2 Assimetria Tática
**Criando Diversidade Estratégica**
- **Layouts Únicos**: Cada camada tem características distintas
- **Objetivos Especializados**: Diferentes tipos de vantagens por camada
- **Rotas Alternativas**: Múltiplos caminhos para o mesmo destino

### 7.3 Balanceamento Dinâmico
- **Ajustes Temporais**: Objetivos mudam de poder ao longo da partida
- **Compensação Automática**: Sistema detecta desequilíbrios e ajusta
- **Feedback de Dados**: Telemetria contínua para refinamentos

## 8. Objetivos e Estruturas

### 8.1 Estruturas Defensivas
**Por Camada e Por Equipe**

#### Torres
- **Planície Radiante**: 11 torres (3-1-3-1-3 por lane + Nexus)
- **Firmamento Zephyr**: 8 torres (4-4 por corrente + estruturas centrais)
- **Abismo Umbral**: 6 torres (distribuídas estrategicamente)

#### Inibidores/Barracks
- **3 por Camada**: Um para cada lane principal
- **Efeito Cascata**: Destruição afeta outras camadas
- **Super Minions**: Versões especializadas por camada

### 8.2 Objetivos Neutros

#### Objetivos Primários (Game-Changing)
- **Guardião da Aurora** (Planície): +20% dano, regeneração de mana
- **Senhor dos Ventos** (Firmamento): +30% velocidade de movimento, atravessar terreno
- **Arqui-Sombra** (Abismo): Invisibilidade de equipe, dano verdadeiro

#### Objetivos Secundários (Vantagens Táticas)
- **Cristais de Luz**: +10% dano mágico por 3 minutos
- **Esferas de Tempestade**: Controle de área por 2 minutos
- **Fragmentos Umbrais**: Recursos para itens únicos

### 8.3 Camps Neutros
**Distribuição por Camada**

#### Planície Radiante
- **12 Camps por Lado**: 4 pequenos, 6 médios, 2 grandes
- **Respawn**: 60-120 segundos baseado no tamanho
- **Buffs**: Luz (mana/CDR) e Crescimento (vida/dano)

#### Firmamento Zephyr
- **8 Camps por Lado**: Focados em mobilidade e utilidade
- **Respawn**: 90-150 segundos
- **Buffs**: Vento (velocidade) e Tempestade (penetração)

#### Abismo Umbral
- **6 Camps por Lado**: Mais poderosos e perigosos
- **Respawn**: 120-180 segundos
- **Buffs**: Sombra (stealth) e Umbral (dano verdadeiro)

## 9. Implementação Técnica

### 9.1 Engine Requirements
**Baseado em Unreal Engine 5**
- **World Composition**: Para gerenciar múltiplas camadas
- **Level Streaming**: Carregamento dinâmico de camadas
- **Nanite Virtualized Geometry**: Para detalhes visuais complexos
- **Lumen Global Illumination**: Iluminação dinâmica por camada

### 9.2 Performance Optimization
- **LOD System**: Diferentes níveis de detalhe por distância e camada
- **Occlusion Culling**: Não renderizar camadas não visíveis
- **Instanced Rendering**: Para objetos repetitivos (árvores, rochas)
- **Texture Streaming**: Carregamento sob demanda de texturas

### 9.3 Network Architecture
- **Layer-based Networking**: Sincronização otimizada por camada
- **Predictive Movement**: Compensação de latência para transições verticais
- **State Compression**: Redução de dados para múltiplas camadas
- **Priority System**: Priorização de updates baseada na relevância

## 10. Ferramentas de Desenvolvimento

### 10.1 Map Editor
**Ferramenta Personalizada**
- **3D Layer Editor**: Edição simultânea de múltiplas camadas
- **Connectivity Tools**: Ferramentas para criar/editar portais
- **Balance Analyzer**: Análise automática de simetria e balance
- **Pathfinding Debugger**: Visualização de rotas de navegação

### 10.2 Testing Tools
- **Bot Navigation**: Bots que testam todas as rotas possíveis
- **Balance Simulator**: Simulações de milhares de partidas
- **Performance Profiler**: Análise de performance por camada
- **Accessibility Checker**: Verificação de acessibilidade mobile

### 10.3 Analytics Integration
- **Heatmaps 3D**: Visualização de movimento em todas as camadas
- **Objective Control**: Análise de controle de objetivos por camada
- **Transition Patterns**: Padrões de movimento entre camadas
- **Balance Metrics**: Métricas de winrate por lado e camada

---

## **📐 GEOMETRIA DETALHADA DO MAPA**

### **🏗️ Especificações de Conectores Verticais**

#### **🌀 Portais Primários**

**Localização e Dimensões**:
- **Quantidade**: 6 portais por camada (18 total)
- **Posições**: Intersecções de lanes principais
- **Raio de Ativação**: 200 unidades
- **Área de Efeito**: 400 unidades de diâmetro
- **Altura**: 300 unidades

**Coordenadas Específicas**:
```
Portais Planície Radiante → Firmamento Zephyr:
- Portal_Top_River: (0, 6000, 1000)
- Portal_Mid_Center: (0, 0, 1000)
- Portal_Bot_River: (0, -6000, 1000)
- Portal_Jungle_NE: (4500, 4500, 1000)
- Portal_Jungle_NW: (-4500, 4500, 1000)
- Portal_Jungle_SE: (4500, -4500, 1000)

Portais Firmamento Zephyr → Abismo Umbral:
- Portal_Sky_Top: (0, 6000, 3000)
- Portal_Sky_Mid: (0, 0, 3000)
- Portal_Sky_Bot: (0, -6000, 3000)
- Portal_Tempest_NE: (4500, 4500, 3000)
- Portal_Tempest_NW: (-4500, 4500, 3000)
- Portal_Tempest_SE: (4500, -4500, 3000)
```

#### **⬆️ Elevadores Místicos**

**Especificações Técnicas**:
- **Quantidade**: 12 elevadores (4 por camada)
- **Velocidade**: 800 unidades/segundo
- **Tempo de Viagem**: 2.5 segundos entre camadas
- **Capacidade**: 5 unidades simultâneas
- **Cooldown**: 3 segundos após uso

**Posições dos Elevadores**:
```
Elevadores Principais:
- Elevador_Norte: (0, 7500, variável)
- Elevador_Sul: (0, -7500, variável)
- Elevador_Leste: (7500, 0, variável)
- Elevador_Oeste: (-7500, 0, variável)

Alturas por Camada:
- Base Radiante: Z = 0
- Plataforma Zephyr: Z = 2000
- Entrada Umbral: Z = 4000
```

#### **🌉 Pontes Dimensionais**

**Características Estruturais**:
- **Comprimento**: 1000 unidades
- **Largura**: 300 unidades
- **Altura de Clearance**: 200 unidades
- **Material**: Energia cristalizada semi-transparente
- **Estabilidade**: Requer objetivo controlado

**Localizações das Pontes**:
```
Pontes Ativadas por Objetivos:
- Ponte_Altar_Amanhecer: Liga Radiante(2000, 3000) → Zephyr(2000, 3000)
- Ponte_Templo_Ventos: Liga Zephyr(3000, 2000) → Umbral(3000, 2000)
- Ponte_Coração_Trevas: Liga Umbral(-2000, -3000) → Radiante(-2000, -3000)
- Ponte_Cristal_Prismal: Liga todas as camadas no centro (0, 0)
```

### **🎯 Sistema de Colisão Detalhado**

#### **📊 Hierarquia de Colisão**

**Collision Profiles Customizados**:
```cpp
// Perfis de Colisão por Camada
RadianteProfile:
- Collision Enabled: Collision Enabled
- Object Type: RadianteLayer
- Collision Responses:
  - RadianteLayer: Block
  - ZephyrLayer: Ignore
  - UmbralLayer: Ignore
  - VerticalConnector: Overlap

ZephyrProfile:
- Collision Enabled: Collision Enabled
- Object Type: ZephyrLayer
- Collision Responses:
  - RadianteLayer: Ignore
  - ZephyrLayer: Block
  - UmbralLayer: Ignore
  - VerticalConnector: Overlap

UmbralProfile:
- Collision Enabled: Collision Enabled
- Object Type: UmbralLayer
- Collision Responses:
  - RadianteLayer: Ignore
  - ZephyrLayer: Ignore
  - UmbralLayer: Block
  - VerticalConnector: Overlap
```

#### **🔍 Detecção de Colisão Multicamada**

**Algoritmo de Detecção**:
```cpp
class AMultiLayerCollision {
public:
    bool CheckCollision(AActor* ActorA, AActor* ActorB);
    bool IsInSameLayer(AActor* ActorA, AActor* ActorB);
    void UpdateActorLayer(AActor* Actor, int32 NewLayer);
    
private:
    struct FLayerCollisionData {
        int32 LayerIndex;
        TArray<AActor*> ActorsInLayer;
        FCollisionQueryParams QueryParams;
    };
    
    TMap<int32, FLayerCollisionData> LayerCollisionMap;
    bool ShouldCollide(int32 LayerA, int32 LayerB);
};
```

### **🧭 Pathfinding Avançado**

#### **📈 Algoritmo A* Multicamada Otimizado**

**Estrutura de Dados Avançada**:
```cpp
struct FAdvancedPathNode {
    FVector WorldPosition;
    int32 LayerIndex;
    float GCost;  // Custo do início até este nó
    float HCost;  // Heurística até o destino
    float FCost;  // GCost + HCost
    
    // Dados de conectividade
    TArray<FVerticalConnection> VerticalConnections;
    TArray<FAdvancedPathNode*> HorizontalNeighbors;
    
    // Metadados
    bool bIsVerticalConnector;
    bool bRequiresObjective;
    float MovementSpeedModifier;
    ETerrainType TerrainType;
};

enum class ETerrainType : uint8 {
    Normal,
    Difficult,  // Jungle, obstáculos
    Fast,       // Rios, caminhos especiais
    Blocked,    // Paredes, estruturas
    Connector   // Portais, elevadores, pontes
};
```

**Custos de Movimento Detalhados**:
```cpp
class APathfindingCostCalculator {
public:
    float CalculateMovementCost(FAdvancedPathNode* From, FAdvancedPathNode* To);
    
private:
    // Custos base por tipo de movimento
    float HorizontalCost = 1.0f;
    float DiagonalCost = 1.414f;
    
    // Modificadores por terreno
    TMap<ETerrainType, float> TerrainModifiers = {
        {ETerrainType::Normal, 1.0f},
        {ETerrainType::Difficult, 1.5f},
        {ETerrainType::Fast, 0.8f},
        {ETerrainType::Blocked, 999.0f},
        {ETerrainType::Connector, 2.0f}
    };
    
    // Custos de transição vertical
    TMap<FString, float> VerticalTransitionCosts = {
        {"Portal_Primary", 2.0f},
        {"Elevator_Mystic", 3.0f},
        {"Bridge_Dimensional", 1.5f},
        {"Emergency_Exit", 4.0f}
    };
};
```

## 11. Considerações de Acessibilidade

### 11.1 Navegação Simplificada
- **Modo Assistido**: IA ajuda na navegação entre camadas
- **Indicadores Visuais**: Setas e marcadores claros
- **Audio Cues**: Sinais sonoros para transições
- **Controles Simplificados**: Botões dedicados para mudança de camada

### 11.2 Adaptações Mobile
- **Interface Adaptativa**: UI que se ajusta à camada atual
- **Gestos Intuitivos**: Swipe para mudança de camada
- **Auto-Camera**: Câmera que segue automaticamente a ação
- **Simplified View**: Modo de visão simplificada para dispositivos menos potentes

### 11.3 Opções de Customização
- **Densidade Visual**: Ajuste de detalhes visuais por camada
- **Filtros de Cor**: Para jogadores com daltonismo
- **Tamanho de UI**: Escalabilidade para diferentes tamanhos de tela
- **Controles Personalizáveis**: Mapeamento livre de controles

## 12. Cronograma de Implementação

### 12.1 Fase 1: Protótipo (Meses 1-3)
- Implementação básica de uma camada
- Sistema de portais simples
- Pathfinding básico
- Testes de conceito

### 12.2 Fase 2: Core Systems (Meses 4-8)
- Implementação das três camadas
- Sistema completo de conectividade
- Pathfinding multicamada
- Objetivos básicos

### 12.3 Fase 3: Polish & Balance (Meses 9-12)
- Balanceamento extensivo
- Otimizações de performance
- Arte final e efeitos visuais
- Testes de stress e QA

### 12.4 Fase 4: Launch Preparation (Meses 13-15)
- Testes beta fechado
- Ajustes baseados em feedback
- Otimizações finais
- Preparação para lançamento

## 13. Métricas de Sucesso

### 13.1 Métricas de Gameplay
- **Utilização de Camadas**: % de tempo gasto em cada camada
- **Transições por Partida**: Número médio de mudanças de camada
- **Controle de Objetivos**: Distribuição de controle entre camadas
- **Duração de Partidas**: Tempo médio de jogo

### 13.2 Métricas de Performance
- **FPS Médio**: Por camada e transições
- **Tempo de Carregamento**: Para mudanças de camada
- **Uso de Memória**: Por camada ativa
- **Latência de Rede**: Para sincronização multicamada

### 13.3 Métricas de Engajamento
- **Retenção de Jogadores**: Taxa de retorno
- **Satisfação**: Pesquisas de feedback
- **Diversidade Estratégica**: Variedade de estratégias utilizadas
- **Acessibilidade**: Adoção em dispositivos mobile

---

**Este documento serve como base técnica para a implementação do mapa revolucionário do AURACRON, combinando as melhores práticas do gênero MOBA com inovações únicas que estabelecerão novos padrões para jogos competitivos multiplayer.**
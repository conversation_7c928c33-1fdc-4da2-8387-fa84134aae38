// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPLandscapeCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPLandscapeCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FIntPoint();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
LANDSCAPE_API UClass* Z_Construct_UClass_ALandscape_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FProceduralLandscapeParams();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronLayerLandscapeConfig *************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig;
class UScriptStruct* FAuracronLayerLandscapeConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronLayerLandscapeConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron layer landscape configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron layer landscape configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerName_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerOffset_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeSize_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightScale_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ThemeColor_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeightmapData_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableNanite_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableWorldPartition_MetaData[] = {
		{ "Category", "AuracronLayerLandscapeConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayerName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerOffset;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LandscapeSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HeightScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ThemeColor;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HeightmapData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HeightmapData;
	static void NewProp_bEnableNanite_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableNanite;
	static void NewProp_bEnableWorldPartition_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableWorldPartition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerLandscapeConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerName = { "LayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, LayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerName_MetaData), NewProp_LayerName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerOffset = { "LayerOffset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, LayerOffset), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerOffset_MetaData), NewProp_LayerOffset_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LandscapeSize = { "LandscapeSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, LandscapeSize), Z_Construct_UScriptStruct_FIntPoint, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeSize_MetaData), NewProp_LandscapeSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightScale = { "HeightScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, HeightScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightScale_MetaData), NewProp_HeightScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_ThemeColor = { "ThemeColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, ThemeColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ThemeColor_MetaData), NewProp_ThemeColor_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightmapData_Inner = { "HeightmapData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightmapData = { "HeightmapData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerLandscapeConfig, HeightmapData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeightmapData_MetaData), NewProp_HeightmapData_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableNanite_SetBit(void* Obj)
{
	((FAuracronLayerLandscapeConfig*)Obj)->bEnableNanite = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableNanite = { "bEnableNanite", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerLandscapeConfig), &Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableNanite_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableNanite_MetaData), NewProp_bEnableNanite_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableWorldPartition_SetBit(void* Obj)
{
	((FAuracronLayerLandscapeConfig*)Obj)->bEnableWorldPartition = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableWorldPartition = { "bEnableWorldPartition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerLandscapeConfig), &Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableWorldPartition_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableWorldPartition_MetaData), NewProp_bEnableWorldPartition_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LayerOffset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_LandscapeSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_ThemeColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightmapData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_HeightmapData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableNanite,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewProp_bEnableWorldPartition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronLayerLandscapeConfig",
	Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::PropPointers),
	sizeof(FAuracronLayerLandscapeConfig),
	alignof(FAuracronLayerLandscapeConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerLandscapeConfig ***************************************

// ********** Begin ScriptStruct FProceduralLandscapeParams ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams;
class UScriptStruct* FProceduralLandscapeParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FProceduralLandscapeParams, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("ProceduralLandscapeParams"));
	}
	return Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural landscape generation parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural landscape generation parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandscapeName_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerConfigs_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePCGGeneration_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAsyncGeneration_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseScale_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoiseOctaves_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoisePersistence_MetaData[] = {
		{ "Category", "ProceduralLandscapeParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LandscapeName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LayerConfigs_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LayerConfigs;
	static void NewProp_bUsePCGGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePCGGeneration;
	static void NewProp_bEnableAsyncGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAsyncGeneration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoiseScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NoiseOctaves;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NoisePersistence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FProceduralLandscapeParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LandscapeName = { "LandscapeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProceduralLandscapeParams, LandscapeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandscapeName_MetaData), NewProp_LandscapeName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LayerConfigs_Inner = { "LayerConfigs", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig, METADATA_PARAMS(0, nullptr) }; // 3008245999
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LayerConfigs = { "LayerConfigs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProceduralLandscapeParams, LayerConfigs), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerConfigs_MetaData), NewProp_LayerConfigs_MetaData) }; // 3008245999
void Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bUsePCGGeneration_SetBit(void* Obj)
{
	((FProceduralLandscapeParams*)Obj)->bUsePCGGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bUsePCGGeneration = { "bUsePCGGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FProceduralLandscapeParams), &Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bUsePCGGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePCGGeneration_MetaData), NewProp_bUsePCGGeneration_MetaData) };
void Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bEnableAsyncGeneration_SetBit(void* Obj)
{
	((FProceduralLandscapeParams*)Obj)->bEnableAsyncGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bEnableAsyncGeneration = { "bEnableAsyncGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FProceduralLandscapeParams), &Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bEnableAsyncGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAsyncGeneration_MetaData), NewProp_bEnableAsyncGeneration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoiseScale = { "NoiseScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProceduralLandscapeParams, NoiseScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseScale_MetaData), NewProp_NoiseScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoiseOctaves = { "NoiseOctaves", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProceduralLandscapeParams, NoiseOctaves), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoiseOctaves_MetaData), NewProp_NoiseOctaves_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoisePersistence = { "NoisePersistence", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProceduralLandscapeParams, NoisePersistence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoisePersistence_MetaData), NewProp_NoisePersistence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LandscapeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LayerConfigs_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_LayerConfigs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bUsePCGGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_bEnableAsyncGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoiseScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoiseOctaves,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewProp_NoisePersistence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"ProceduralLandscapeParams",
	Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::PropPointers),
	sizeof(FProceduralLandscapeParams),
	alignof(FProceduralLandscapeParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FProceduralLandscapeParams()
{
	if (!Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.InnerSingleton, Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams.InnerSingleton;
}
// ********** End ScriptStruct FProceduralLandscapeParams ******************************************

// ********** Begin Class UUnrealMCPLandscapeCommands **********************************************
void UUnrealMCPLandscapeCommands::StaticRegisterNativesUUnrealMCPLandscapeCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands;
UClass* UUnrealMCPLandscapeCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPLandscapeCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPLandscapeCommands"),
			Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPLandscapeCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands_NoRegister()
{
	return UUnrealMCPLandscapeCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Landscape Commands - Modern UE 5.6.1 Implementation\n * Handles procedural landscape creation with Auracron-specific features\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPLandscapeCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Landscape Commands - Modern UE 5.6.1 Implementation\nHandles procedural landscape creation with Auracron-specific features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedLandscapes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created landscapes\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created landscapes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// PCG components for procedural generation\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "PCG components for procedural generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material instances cache\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPLandscapeCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material instances cache" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedLandscapes_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedLandscapes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedLandscapes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponents_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PCGComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PCGComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LayerMaterials_ValueProp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LayerMaterials;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPLandscapeCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes_ValueProp = { "CreatedLandscapes", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_ALandscape_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes_Key_KeyProp = { "CreatedLandscapes_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes = { "CreatedLandscapes", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPLandscapeCommands, CreatedLandscapes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedLandscapes_MetaData), NewProp_CreatedLandscapes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents_ValueProp = { "PCGComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents_Key_KeyProp = { "PCGComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents = { "PCGComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPLandscapeCommands, PCGComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponents_MetaData), NewProp_PCGComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials_ValueProp = { "LayerMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials_Key_KeyProp = { "LayerMaterials_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials = { "LayerMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPLandscapeCommands, LayerMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerMaterials_MetaData), NewProp_LayerMaterials_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_CreatedLandscapes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_PCGComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::NewProp_LayerMaterials,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::ClassParams = {
	&UUnrealMCPLandscapeCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPLandscapeCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPLandscapeCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands.OuterSingleton;
}
UUnrealMCPLandscapeCommands::UUnrealMCPLandscapeCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPLandscapeCommands);
UUnrealMCPLandscapeCommands::~UUnrealMCPLandscapeCommands() {}
// ********** End Class UUnrealMCPLandscapeCommands ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLayerLandscapeConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerLandscapeConfig_Statics::NewStructOps, TEXT("AuracronLayerLandscapeConfig"), &Z_Registration_Info_UScriptStruct_FAuracronLayerLandscapeConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerLandscapeConfig), 3008245999U) },
		{ FProceduralLandscapeParams::StaticStruct, Z_Construct_UScriptStruct_FProceduralLandscapeParams_Statics::NewStructOps, TEXT("ProceduralLandscapeParams"), &Z_Registration_Info_UScriptStruct_FProceduralLandscapeParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FProceduralLandscapeParams), 3876724120U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPLandscapeCommands, UUnrealMCPLandscapeCommands::StaticClass, TEXT("UUnrealMCPLandscapeCommands"), &Z_Registration_Info_UClass_UUnrealMCPLandscapeCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPLandscapeCommands), 2479741918U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_438540138(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPLandscapeCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS

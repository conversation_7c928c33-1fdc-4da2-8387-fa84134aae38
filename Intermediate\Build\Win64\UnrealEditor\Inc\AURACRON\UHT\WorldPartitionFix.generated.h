// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "WorldPartitionFix.h"

#ifdef AURACRON_WorldPartitionFix_generated_h
#error "WorldPartitionFix.generated.h already included, missing '#pragma once' in WorldPartitionFix.h"
#endif
#define AURACRON_WorldPartitionFix_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UWorld;
class UWorldPartition;

// ********** Begin Class UWorldPartitionFix *******************************************************
#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execVerifyAndFixWorldPartitionState); \
	DECLARE_FUNCTION(execSafeUninitializeWorldPartition); \
	DECLARE_FUNCTION(execValidateWorldPartitionInitialization); \
	DECLARE_FUNCTION(execForceResetWorldPartitionState); \
	DECLARE_FUNCTION(execIsWorldPartitionInValidState); \
	DECLARE_FUNCTION(execOnWorldCleanup);


AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFix_NoRegister();

#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUWorldPartitionFix(); \
	friend struct Z_Construct_UClass_UWorldPartitionFix_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UWorldPartitionFix_NoRegister(); \
public: \
	DECLARE_CLASS2(UWorldPartitionFix, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UWorldPartitionFix_NoRegister) \
	DECLARE_SERIALIZER(UWorldPartitionFix)


#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UWorldPartitionFix(UWorldPartitionFix&&) = delete; \
	UWorldPartitionFix(const UWorldPartitionFix&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UWorldPartitionFix); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UWorldPartitionFix); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UWorldPartitionFix) \
	NO_API virtual ~UWorldPartitionFix();


#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_23_PROLOG
#define FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h_26_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UWorldPartitionFix;

// ********** End Class UWorldPartitionFix *********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Source_AURACRON_Public_WorldPartitionFix_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

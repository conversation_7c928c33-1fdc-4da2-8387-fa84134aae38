#include "UnrealMCPBridge.h"
#include "MCPServerRunnable.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "HAL/RunnableThread.h"
#include "Interfaces/IPv4/IPv4Address.h"
#include "Interfaces/IPv4/IPv4Endpoint.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonWriter.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Camera/CameraActor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "JsonObjectConverter.h"
#include "GameFramework/Actor.h"
#include "Engine/Selection.h"
#include "Kismet/GameplayStatics.h"
#include "Async/Async.h"
// Add Blueprint related includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
// UE5.5 correct includes
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"
#include "UObject/Field.h"
#include "UObject/FieldPath.h"
// Blueprint Graph specific includes
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "K2Node_CallFunction.h"
#include "K2Node_InputAction.h"
#include "K2Node_Self.h"
#include "GameFramework/InputSettings.h"
#include "EditorSubsystem.h"
#include "Subsystems/EditorActorSubsystem.h"
// Include our new command handler classes
#include "Commands/UnrealMCPEditorCommands.h"
#include "Commands/UnrealMCPBlueprintCommands.h"
#include "Commands/UnrealMCPBlueprintNodeCommands.h"
#include "Commands/UnrealMCPProjectCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Commands/UnrealMCPUMGCommands.h"

// Default settings
#define MCP_SERVER_HOST "127.0.0.1"
#define MCP_SERVER_PORT 55557

UUnrealMCPBridge::UUnrealMCPBridge()
{
    EditorCommands = MakeShared<FUnrealMCPEditorCommands>();
    BlueprintCommands = MakeShared<FUnrealMCPBlueprintCommands>();
    BlueprintNodeCommands = MakeShared<FUnrealMCPBlueprintNodeCommands>();
    ProjectCommands = MakeShared<FUnrealMCPProjectCommands>();
    UMGCommands = MakeShared<FUnrealMCPUMGCommands>();
    MapCommands = MakeShared<FUnrealMCPMapCommands>();
    PathfindingCommands = MakeShared<FUnrealMCPPathfindingCommands>();
    VisionCommands = MakeShared<FUnrealMCPVisionCommands>();
    MOBACommands = MakeShared<FUnrealMCPMOBACommands>();
    CollisionCommands = MakeShared<FUnrealMCPCollisionCommands>();
    AnalyticsCommands = MakeShared<FUnrealMCPAnalyticsCommands>();
    BalanceCommands = MakeShared<FUnrealMCPBalanceCommands>();
    ProceduralMeshCommands = CreateDefaultSubobject<UUnrealMCPProceduralMeshCommands>(TEXT("ProceduralMeshCommands"));
    MaterialCommands = CreateDefaultSubobject<UUnrealMCPMaterialCommands>(TEXT("MaterialCommands"));
    ArchitectureCommands = CreateDefaultSubobject<UUnrealMCPArchitectureCommands>(TEXT("ArchitectureCommands"));
    CollisionAdvancedCommands = CreateDefaultSubobject<UUnrealMCPCollisionAdvancedCommands>(TEXT("CollisionAdvancedCommands"));
    VisualEffectsCommands = CreateDefaultSubobject<UUnrealMCPVisualEffectsCommands>(TEXT("VisualEffectsCommands"));
    LandscapeCommands = CreateDefaultSubobject<UUnrealMCPLandscapeCommands>(TEXT("LandscapeCommands"));
}

UUnrealMCPBridge::~UUnrealMCPBridge()
{
    EditorCommands.Reset();
    BlueprintCommands.Reset();
    BlueprintNodeCommands.Reset();
    ProjectCommands.Reset();
    UMGCommands.Reset();
    MapCommands.Reset();
    PathfindingCommands.Reset();
    VisionCommands.Reset();
    MOBACommands.Reset();
    CollisionCommands.Reset();
    AnalyticsCommands.Reset();
    BalanceCommands.Reset();
    ProceduralMeshCommands = nullptr;
    MaterialCommands = nullptr;
    ArchitectureCommands = nullptr;
    CollisionAdvancedCommands = nullptr;
    VisualEffectsCommands = nullptr;
    LandscapeCommands = nullptr;
}

// Initialize subsystem
void UUnrealMCPBridge::Initialize(FSubsystemCollectionBase& Collection)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Initializing"));
    
    bIsRunning = false;
    ListenerSocket = nullptr;
    ConnectionSocket = nullptr;
    ServerThread = nullptr;
    Port = MCP_SERVER_PORT;
    FIPv4Address::Parse(MCP_SERVER_HOST, ServerAddress);

    // Start the server automatically
    StartServer();
}

// Clean up resources when subsystem is destroyed
void UUnrealMCPBridge::Deinitialize()
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Shutting down"));
    StopServer();
}

// Start the MCP server
void UUnrealMCPBridge::StartServer()
{
    if (bIsRunning)
    {
        UE_LOG(LogTemp, Warning, TEXT("UnrealMCPBridge: Server is already running"));
        return;
    }

    // Create socket subsystem
    ISocketSubsystem* SocketSubsystem = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM);
    if (!SocketSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to get socket subsystem"));
        return;
    }

    // Create listener socket
    TSharedPtr<FSocket> NewListenerSocket = MakeShareable(SocketSubsystem->CreateSocket(NAME_Stream, TEXT("UnrealMCPListener"), false));
    if (!NewListenerSocket.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create listener socket"));
        return;
    }

    // Allow address reuse for quick restarts
    NewListenerSocket->SetReuseAddr(true);
    NewListenerSocket->SetNonBlocking(true);

    // Bind to address
    FIPv4Endpoint Endpoint(ServerAddress, Port);
    if (!NewListenerSocket->Bind(*Endpoint.ToInternetAddr()))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to bind listener socket to %s:%d"), *ServerAddress.ToString(), Port);
        return;
    }

    // Start listening
    if (!NewListenerSocket->Listen(5))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to start listening"));
        return;
    }

    ListenerSocket = NewListenerSocket;
    bIsRunning = true;
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server started on %s:%d"), *ServerAddress.ToString(), Port);

    // Start server thread
    ServerThread = FRunnableThread::Create(
        new FMCPServerRunnable(this, ListenerSocket),
        TEXT("UnrealMCPServerThread"),
        0, TPri_Normal
    );

    if (!ServerThread)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create server thread"));
        StopServer();
        return;
    }
}

// Stop the MCP server
void UUnrealMCPBridge::StopServer()
{
    if (!bIsRunning)
    {
        return;
    }

    bIsRunning = false;

    // Clean up thread
    if (ServerThread)
    {
        ServerThread->Kill(true);
        delete ServerThread;
        ServerThread = nullptr;
    }

    // Close sockets
    if (ConnectionSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ConnectionSocket.Get());
        ConnectionSocket.Reset();
    }

    if (ListenerSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ListenerSocket.Get());
        ListenerSocket.Reset();
    }

    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server stopped"));
}

// Execute a command received from a client
FString UUnrealMCPBridge::ExecuteCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Executing command: %s"), *CommandType);
    
    // Create a promise to wait for the result
    TPromise<FString> Promise;
    TFuture<FString> Future = Promise.GetFuture();
    
    // Queue execution on Game Thread
    AsyncTask(ENamedThreads::GameThread, [this, CommandType, Params, Promise = MoveTemp(Promise)]() mutable
    {
        TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
        
        try
        {
            TSharedPtr<FJsonObject> ResultJson;
            
            if (CommandType == TEXT("ping"))
            {
                ResultJson = MakeShareable(new FJsonObject);
                ResultJson->SetStringField(TEXT("message"), TEXT("pong"));
            }
            // Editor Commands (including actor manipulation)
            else if (CommandType == TEXT("get_actors_in_level") || 
                     CommandType == TEXT("find_actors_by_name") ||
                     CommandType == TEXT("spawn_actor") ||
                     CommandType == TEXT("create_actor") ||
                     CommandType == TEXT("delete_actor") || 
                     CommandType == TEXT("set_actor_transform") ||
                     CommandType == TEXT("get_actor_properties") ||
                     CommandType == TEXT("set_actor_property") ||
                     CommandType == TEXT("spawn_blueprint_actor") ||
                     CommandType == TEXT("focus_viewport") || 
                     CommandType == TEXT("take_screenshot"))
            {
                ResultJson = EditorCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Commands
            else if (CommandType == TEXT("create_blueprint") || 
                     CommandType == TEXT("add_component_to_blueprint") || 
                     CommandType == TEXT("set_component_property") || 
                     CommandType == TEXT("set_physics_properties") || 
                     CommandType == TEXT("compile_blueprint") || 
                     CommandType == TEXT("set_blueprint_property") || 
                     CommandType == TEXT("set_static_mesh_properties") ||
                     CommandType == TEXT("set_pawn_properties"))
            {
                ResultJson = BlueprintCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Node Commands
            else if (CommandType == TEXT("connect_blueprint_nodes") || 
                     CommandType == TEXT("add_blueprint_get_self_component_reference") ||
                     CommandType == TEXT("add_blueprint_self_reference") ||
                     CommandType == TEXT("find_blueprint_nodes") ||
                     CommandType == TEXT("add_blueprint_event_node") ||
                     CommandType == TEXT("add_blueprint_input_action_node") ||
                     CommandType == TEXT("add_blueprint_function_node") ||
                     CommandType == TEXT("add_blueprint_get_component_node") ||
                     CommandType == TEXT("add_blueprint_variable"))
            {
                ResultJson = BlueprintNodeCommands->HandleCommand(CommandType, Params);
            }
            // Project Commands
            else if (CommandType == TEXT("create_input_mapping"))
            {
                ResultJson = ProjectCommands->HandleCommand(CommandType, Params);
            }
            // UMG Commands
            else if (CommandType == TEXT("create_umg_widget_blueprint") ||
                     CommandType == TEXT("add_text_block_to_widget") ||
                     CommandType == TEXT("add_button_to_widget") ||
                     CommandType == TEXT("bind_widget_event") ||
                     CommandType == TEXT("set_text_block_binding") ||
                     CommandType == TEXT("add_widget_to_viewport") ||
                     CommandType == TEXT("create_auracron_multilayer_interface") ||
                     CommandType == TEXT("create_layer_indicator_widget") ||
                     CommandType == TEXT("create_vertical_transition_controls"))
            {
                ResultJson = UMGCommands->HandleCommand(CommandType, Params);
            }
            // Map Commands
            else if (CommandType == TEXT("create_multilayer_map") ||
                     CommandType == TEXT("configure_layer_properties") ||
                     CommandType == TEXT("create_portal_system") ||
                     CommandType == TEXT("create_elevator_system") ||
                     CommandType == TEXT("create_dimensional_bridge") ||
                     CommandType == TEXT("setup_layer_lighting") ||
                     CommandType == TEXT("create_layer_boundaries"))
            {
                ResultJson = MapCommands->HandleCommand(CommandType, Params);
            }
            // Procedural Mesh Commands
            else if (CommandType == TEXT("create_lane_geometry") ||
                     CommandType == TEXT("create_jungle_structures") ||
                     CommandType == TEXT("create_tower_meshes") ||
                     CommandType == TEXT("create_base_architecture") ||
                     CommandType == TEXT("create_portal_geometry") ||
                     CommandType == TEXT("create_bridge_meshes"))
            {
                ResultJson = ProceduralMeshCommands->HandleCommand(CommandType, Params);
            }
            // Material Commands
            else if (CommandType == TEXT("create_layer_materials") ||
                     CommandType == TEXT("create_themed_textures") ||
                     CommandType == TEXT("create_dynamic_materials") ||
                     CommandType == TEXT("create_material_functions") ||
                     CommandType == TEXT("setup_material_parameters"))
            {
                ResultJson = MaterialCommands->HandleCommand(CommandType, Params);
            }
            // Architecture Commands
            else if (CommandType == TEXT("create_tower_structures") ||
                     CommandType == TEXT("create_base_buildings") ||
                     CommandType == TEXT("create_inhibitor_monuments") ||
                     CommandType == TEXT("create_nexus_architecture") ||
                     CommandType == TEXT("create_defensive_walls") ||
                     CommandType == TEXT("create_jungle_camps"))
            {
                ResultJson = ArchitectureCommands->HandleCommand(CommandType, Params);
            }
            // Collision Advanced Commands
            else if (CommandType == TEXT("create_precise_collision") ||
                     CommandType == TEXT("setup_physics_materials") ||
                     CommandType == TEXT("create_collision_profiles") ||
                     CommandType == TEXT("setup_chaos_physics") ||
                     CommandType == TEXT("create_trigger_volumes"))
            {
                ResultJson = CollisionAdvancedCommands->HandleCommand(CommandType, Params);
            }
            // Visual Effects Commands
            else if (CommandType == TEXT("create_dynamic_lighting") ||
                     CommandType == TEXT("setup_sky_atmosphere") ||
                     CommandType == TEXT("create_volumetric_effects") ||
                     CommandType == TEXT("setup_niagara_effects") ||
                     CommandType == TEXT("configure_post_processing"))
            {
                ResultJson = VisualEffectsCommands->HandleCommand(CommandType, Params);
            }
            // Landscape Commands
            else if (CommandType == TEXT("create_procedural_landscape") ||
                     CommandType == TEXT("create_layer_heightmaps") ||
                     CommandType == TEXT("apply_landscape_materials") ||
                     CommandType == TEXT("create_landscape_splines") ||
                     CommandType == TEXT("setup_landscape_collision"))
            {
                ResultJson = LandscapeCommands->HandleCommand(CommandType, Params);
            }
            // Pathfinding Commands
            else if (CommandType == TEXT("create_multilayer_pathfinding") ||
                     CommandType == TEXT("create_custom_query_filter") ||
                     CommandType == TEXT("setup_vertical_transition_costs") ||
                     CommandType == TEXT("create_mobile_navigation_assistance") ||
                     CommandType == TEXT("optimize_pathfinding_performance") ||
                     CommandType == TEXT("create_custom_astar_algorithm") ||
                     CommandType == TEXT("setup_pathfinding_debug_tools"))
            {
                ResultJson = PathfindingCommands->HandleCommand(CommandType, Params);
            }
            // Vision Commands
            else if (CommandType == TEXT("create_multilayer_fog_of_war") ||
                     CommandType == TEXT("configure_vertical_sight_ranges") ||
                     CommandType == TEXT("create_tridimensional_ward_system") ||
                     CommandType == TEXT("create_multilayer_stealth_system") ||
                     CommandType == TEXT("setup_truesight_mechanics") ||
                     CommandType == TEXT("optimize_vision_performance"))
            {
                ResultJson = VisionCommands->HandleCommand(CommandType, Params);
            }
            // MOBA Commands
            else if (CommandType == TEXT("create_multilayer_tower_system") ||
                     CommandType == TEXT("create_cascading_inhibitor_system") ||
                     CommandType == TEXT("create_specialized_neutral_camps") ||
                     CommandType == TEXT("create_epic_objectives") ||
                     CommandType == TEXT("create_minion_spawning_system") ||
                     CommandType == TEXT("create_dynamic_buff_system") ||
                     CommandType == TEXT("create_team_objective_control"))
            {
                ResultJson = MOBACommands->HandleCommand(CommandType, Params);
            }
            // Collision Commands
            else if (CommandType == TEXT("create_layer_collision_profiles") ||
                     CommandType == TEXT("create_intelligent_collision_detection") ||
                     CommandType == TEXT("configure_chaos_physics_per_layer") ||
                     CommandType == TEXT("create_custom_collision_handlers") ||
                     CommandType == TEXT("create_advanced_shape_collision") ||
                     CommandType == TEXT("optimize_collision_performance"))
            {
                ResultJson = CollisionCommands->HandleCommand(CommandType, Params);
            }
            // Analytics Commands
            else if (CommandType == TEXT("create_3d_heatmap_system") ||
                     CommandType == TEXT("create_objective_control_analysis") ||
                     CommandType == TEXT("create_transition_pattern_analysis") ||
                     CommandType == TEXT("create_advanced_balance_metrics") ||
                     CommandType == TEXT("create_automated_feedback_system") ||
                     CommandType == TEXT("create_performance_analytics"))
            {
                ResultJson = AnalyticsCommands->HandleCommand(CommandType, Params);
            }
            // Balance Commands
            else if (CommandType == TEXT("create_automated_symmetry_analysis") ||
                     CommandType == TEXT("create_advanced_imbalance_detection") ||
                     CommandType == TEXT("create_dynamic_compensation_system") ||
                     CommandType == TEXT("create_continuous_telemetry_system") ||
                     CommandType == TEXT("create_automated_refinement_suggestions"))
            {
                ResultJson = BalanceCommands->HandleCommand(CommandType, Params);
            }
            else
            {
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), FString::Printf(TEXT("Unknown command: %s"), *CommandType));
                
                FString ResultString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
                FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
                Promise.SetValue(ResultString);
                return;
            }
            
            // Check if the result contains an error
            bool bSuccess = true;
            FString ErrorMessage;
            
            if (ResultJson->HasField(TEXT("success")))
            {
                bSuccess = ResultJson->GetBoolField(TEXT("success"));
                if (!bSuccess && ResultJson->HasField(TEXT("error")))
                {
                    ErrorMessage = ResultJson->GetStringField(TEXT("error"));
                }
            }
            
            if (bSuccess)
            {
                // Set success status and include the result
                ResponseJson->SetStringField(TEXT("status"), TEXT("success"));
                ResponseJson->SetObjectField(TEXT("result"), ResultJson);
            }
            else
            {
                // Set error status and include the error message
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), ErrorMessage);
            }
        }
        catch (const std::exception& e)
        {
            ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
            ResponseJson->SetStringField(TEXT("error"), UTF8_TO_TCHAR(e.what()));
        }
        
        FString ResultString;
        TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
        FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
        Promise.SetValue(ResultString);
    });
    
    return Future.Get();
}
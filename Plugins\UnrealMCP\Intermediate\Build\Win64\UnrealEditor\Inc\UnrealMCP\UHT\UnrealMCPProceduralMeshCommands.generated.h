// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPProceduralMeshCommands.h"

#ifdef UNREALMCP_UnrealMCPProceduralMeshCommands_generated_h
#error "UnrealMCPProceduralMeshCommands.generated.h already included, missing '#pragma once' in UnrealMCPProceduralMeshCommands.h"
#endif
#define UNREALMCP_UnrealMCPProceduralMeshCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronProceduralMeshConfig *************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_44_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronProceduralMeshConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronProceduralMeshConfig;
// ********** End ScriptStruct FAuracronProceduralMeshConfig ***************************************

// ********** Begin ScriptStruct FLaneGeometryParams ***********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_97_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLaneGeometryParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FLaneGeometryParams;
// ********** End ScriptStruct FLaneGeometryParams *************************************************

// ********** Begin Class UUnrealMCPProceduralMeshCommands *****************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_134_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPProceduralMeshCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPProceduralMeshCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPProceduralMeshCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPProceduralMeshCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_134_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPProceduralMeshCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPProceduralMeshCommands(UUnrealMCPProceduralMeshCommands&&) = delete; \
	UUnrealMCPProceduralMeshCommands(const UUnrealMCPProceduralMeshCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPProceduralMeshCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPProceduralMeshCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPProceduralMeshCommands) \
	NO_API virtual ~UUnrealMCPProceduralMeshCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_131_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_134_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_134_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h_134_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPProceduralMeshCommands;

// ********** End Class UUnrealMCPProceduralMeshCommands *******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralMeshCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS

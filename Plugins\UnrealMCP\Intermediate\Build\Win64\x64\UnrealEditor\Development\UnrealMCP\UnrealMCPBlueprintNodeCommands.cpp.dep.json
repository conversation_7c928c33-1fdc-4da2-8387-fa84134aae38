{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpblueprintnodecommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintnodecommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_event.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_eventnodeinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_eventnodeinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompilermisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\bpterminal.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\blueprintcompiledstatement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_event.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_callfunction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_callfunction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variable.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_inputaction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_inputaction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_self.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_self.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\blueprinteditorutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\inputsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\keystate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\genericplatform\\inputdevicemappingpolicy.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\developersettings\\public\\engine\\platformsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\developersettings\\public\\engine\\platformsettingsmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\developersettings\\uht\\platformsettingsmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\developersettings\\uht\\platformsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\inputsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\camera\\cameraactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameraactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_addcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_addcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_addcomponentbyclass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_constructobjectfromclass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_constructobjectfromclass.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_addcomponentbyclass.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_asyncaction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_baseasynctask.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_baseasynctask.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_asyncaction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_breakstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_structmemberget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_structoperation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_structoperation.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_structmemberget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_breakstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_callarrayfunction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_callarrayfunction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_calldatatablefunction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_calldatatablefunction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_actorboundevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_actorboundevent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_assigndelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_adddelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_basemcdelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_basemcdelegate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_adddelegate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_assigndelegate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_bitmaskliteral.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\nodedependingonenuminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\nodedependingonenuminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_bitmaskliteral.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\public\\blueprintactiondatabase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\public\\blueprintactiondatabaseregistrar.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\public\\blueprintnodespawner.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\blueprintnodespawner.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\hal\\platformapplicationmisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\windows\\windowsplatformapplicationmisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\genericplatform\\genericplatformapplicationmisc.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}
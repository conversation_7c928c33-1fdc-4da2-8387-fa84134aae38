﻿Log file open, 08/29/25 08:45:01
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=33604)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 37223
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:/Game/AURACRON/AURACRON.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=244cc56c65ca4082a4aa8d2575394e90 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.341508
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-B8DF59EE4F369E7C17FA7BB37F734AF3
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AuracronEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AuracronEditor.target
LogConfig: Display: Loading VulkanPC ini files took 0.08 seconds
LogConfig: Display: Loading Android ini files took 0.08 seconds
LogConfig: Display: Loading IOS ini files took 0.08 seconds
LogConfig: Display: Loading Mac ini files took 0.08 seconds
LogAssetRegistry: Display: Asset registry cache read as 74.0 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin EnhancedInput
LogConfig: Display: Loading Windows ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogConfig: Display: Loading TVOS ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogConfig: Display: Loading Unix ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogConfig: Display: Loading Linux ini files took 0.10 seconds
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LandscapePatch
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PCGBiomeCore
LogPluginManager: Mounting Engine plugin PCGBiomeSample
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin MLAdapter
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=244cc56c65ca4082a4aa8d2575394e90 -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.setres:1280x720]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.29-11.45.01:822][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.29-11.45.01:822][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.29-11.45.01:824][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.29-11.45.01:824][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.29-11.45.01:824][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.29-11.45.01:824][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.29-11.45.01:825][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.29-11.45.01:825][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.29-11.45.01:827][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-11.45.01:827][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-11.45.01:827][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-11.45.01:829][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.29-11.45.01:829][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-11.45.01:910][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.29-11.45.01:934][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.29-11.45.01:934][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.29-11.45.01:934][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.29-11.45.01:934][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-11.45.01:934][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.29-11.45.01:939][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.29-11.45.01:939][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.29-11.45.01:939][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.29-11.45.01:939][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.29-11.45.01:939][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.29-11.45.01:939][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-11.45.01:939][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-11.45.01:939][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-11.45.01:940][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-11.45.01:940][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.29-11.45.01:940][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.29-11.45.01:940][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.29-11.45.01:940][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.45.01:940][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.29-11.45.02:146][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.45.02:146][  0]LogD3D11RHI:     AdapterLuid : 0 86560
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.29-11.45.02:150][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.45.02:150][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.45.02:150][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.29-11.45.02:150][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-11.45.02:150][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.29-11.45.02:150][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.29-11.45.02:151][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.29-11.45.02:151][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.45.02:151][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.29-11.45.02:151][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.29-11.45.02:151][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-11.45.02:151][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.29-11.45.02:151][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.29-11.45.02:151][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.29-11.45.02:151][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.29-11.45.02:151][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.29-11.45.02:151][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-11.45.02:151][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.29-11.45.02:151][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.29-11.45.02:151][  0]LogInit: Computer: TKT
[2025.08.29-11.45.02:151][  0]LogInit: User: tktca
[2025.08.29-11.45.02:151][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.29-11.45.02:151][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.29-11.45.02:151][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.29-11.45.02:151][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=37.6GB
[2025.08.29-11.45.02:151][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.29-11.45.02:151][  0]LogMemory: Process Physical Memory: 645.77 MB used, 708.44 MB peak
[2025.08.29-11.45.02:151][  0]LogMemory: Process Virtual Memory: 650.16 MB used, 693.62 MB peak
[2025.08.29-11.45.02:151][  0]LogMemory: Physical Memory: 20814.68 MB used,  11638.02 MB free, 32452.70 MB total
[2025.08.29-11.45.02:151][  0]LogMemory: Virtual Memory: 28382.32 MB used,  10108.42 MB free, 38490.75 MB total
[2025.08.29-11.45.02:151][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.29-11.45.02:154][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.29-11.45.02:156][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.29-11.45.02:156][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.29-11.45.02:157][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.29-11.45.02:157][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.29-11.45.02:164][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.29-11.45.02:521][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.29-11.45.02:611][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.29-11.45.02:612][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.29-11.45.02:612][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.29-11.45.02:612][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.29-11.45.02:621][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.29-11.45.02:624][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.29-11.45.02:628][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.29-11.45.02:628][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.29-11.45.02:740][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-11.45.02:740][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-11.45.02:740][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-11.45.02:740][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-11.45.02:740][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-11.45.02:740][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-11.45.02:740][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-11.45.02:740][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-11.45.02:740][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-11.45.02:741][  0]LogWindows: Attached monitors:
[2025.08.29-11.45.02:741][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY1' [PRIMARY]
[2025.08.29-11.45.02:741][  0]LogWindows: Found 1 attached monitors.
[2025.08.29-11.45.02:741][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.29-11.45.02:741][  0]LogRHI: RHI Adapter Info:
[2025.08.29-11.45.02:741][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.02:741][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-11.45.02:741][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.29-11.45.02:741][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.29-11.45.02:741][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.29-11.45.02:741][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.29-11.45.02:741][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-11.45.02:741][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-11.45.02:911][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.29-11.45.02:938][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.29-11.45.02:938][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.29-11.45.02:939][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.29-11.45.02:939][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.29-11.45.02:939][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.29-11.45.02:939][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.29-11.45.02:939][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.29-11.45.02:953][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.29-11.45.02:956][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.29-11.45.02:956][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.29-11.45.02:956][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.29-11.45.02:956][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.29-11.45.02:956][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.29-11.45.02:959][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all'
[2025.08.29-11.45.02:959][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_0.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_0.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -platform=all" ]
[2025.08.29-11.45.02:978][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.29-11.45.02:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.29-11.45.02:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.29-11.45.02:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.29-11.45.02:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.29-11.45.02:978][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.29-11.45.02:978][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.29-11.45.02:978][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.29-11.45.02:979][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.29-11.45.02:979][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.29-11.45.03:019][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.29-11.45.03:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.29-11.45.03:036][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.29-11.45.03:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.29-11.45.03:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.29-11.45.03:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.29-11.45.03:061][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.29-11.45.03:082][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.29-11.45.03:082][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.29-11.45.03:082][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.29-11.45.03:082][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.29-11.45.03:099][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.29-11.45.03:099][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.29-11.45.03:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.29-11.45.03:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.29-11.45.03:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.29-11.45.03:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.29-11.45.03:116][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.29-11.45.03:147][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.29-11.45.03:154][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.29-11.45.03:154][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.29-11.45.03:158][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.29-11.45.03:158][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.29-11.45.03:158][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.29-11.45.03:158][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-11.45.03:158][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.29-11.45.03:370][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.29-11.45.03:371][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-11.45.03:371][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.29-11.45.03:371][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.29-11.45.03:372][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-11.45.03:372][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-11.45.03:373][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.29-11.45.03:781][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.29-11.45.03:781][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.410 seconds
[2025.08.29-11.45.03:783][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.29-11.45.03:804][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.02 seconds.
[2025.08.29-11.45.03:804][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=1404.36MBs, RandomWriteSpeed=45.06MBs. Assigned SpeedClass 'Local'
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.29-11.45.03:808][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.29-11.45.03:809][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/28336/).
[2025.08.29-11.45.03:809][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/F7C95DC144761A1D40720FAF1651A125/'.
[2025.08.29-11.45.03:810][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.29-11.45.03:810][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.29-11.45.03:810][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.29-11.45.03:812][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.29-11.45.03:812][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.29-11.45.04:255][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.29-11.45.05:455][  0]LogSlate: Using FreeType 2.10.0
[2025.08.29-11.45.05:455][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.29-11.45.05:459][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.29-11.45.05:459][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.29-11.45.05:459][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.29-11.45.05:459][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.29-11.45.05:474][  0]LogAssetRegistry: FAssetRegistry took 0.0031 seconds to start up
[2025.08.29-11.45.05:476][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.29-11.45.05:571][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.29-11.45.05:793][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.29-11.45.05:793][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.29-11.45.05:831][  0]LogDeviceProfileManager: Active device profile: [00000252CEA00500][00000252C7EA0000 66] WindowsEditor
[2025.08.29-11.45.05:831][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.29-11.45.05:835][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.29-11.45.05:837][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.29-11.45.05:837][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.29-11.45.05:837][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.29-11.45.05:848][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.29-11.45.05:848][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.29-11.45.05:848][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.29-11.45.05:849][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT'
[2025.08.29-11.45.05:849][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/AURACRON/AURACRON.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/AURACRON/Intermediate/TurnkeyReport_1.log" -log="C:/Game/AURACRON/Intermediate/TurnkeyLog_1.log" -project="C:/Game/AURACRON/AURACRON.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.29-11.45.05:886][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:887][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.29-11.45.05:887][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:887][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:888][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.29-11.45.05:888][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:888][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:891][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.29-11.45.05:891][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.29-11.45.05:894][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.29-11.45.05:895][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:896][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:896][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:897][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.29-11.45.05:898][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.29-11.45.05:899][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.29-11.45.05:900][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:947][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.29-11.45.05:947][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:969][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.29-11.45.05:969][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.05:985][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.29-11.45.05:985][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.45.06:134][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.29-11.45.06:134][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.29-11.45.06:134][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.29-11.45.06:134][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.29-11.45.06:134][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.29-11.45.06:887][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.29-11.45.06:938][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.29-11.45.06:938][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.29-11.45.06:938][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.29-11.45.06:938][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.29-11.45.06:944][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.29-11.45.06:944][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.29-11.45.06:945][  0]LogLiveCoding: Display: First instance in process group "UE_AURACRON_0xa5ca6502", spawning console
[2025.08.29-11.45.06:952][  0]LogLiveCoding: Display: Waiting for server
[2025.08.29-11.45.06:974][  0]LogSlate: Border
[2025.08.29-11.45.06:974][  0]LogSlate: BreadcrumbButton
[2025.08.29-11.45.06:974][  0]LogSlate: Brushes.Title
[2025.08.29-11.45.06:974][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.29-11.45.06:974][  0]LogSlate: Default
[2025.08.29-11.45.06:974][  0]LogSlate: Icons.Save
[2025.08.29-11.45.06:974][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.29-11.45.06:974][  0]LogSlate: ListView
[2025.08.29-11.45.06:974][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.29-11.45.06:974][  0]LogSlate: SoftwareCursor_Grab
[2025.08.29-11.45.06:974][  0]LogSlate: TableView.DarkRow
[2025.08.29-11.45.06:974][  0]LogSlate: TableView.Row
[2025.08.29-11.45.06:974][  0]LogSlate: TreeView
[2025.08.29-11.45.07:087][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.29-11.45.07:093][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 5.604 ms
[2025.08.29-11.45.07:131][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.29-11.45.07:131][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.29-11.45.07:131][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.29-11.45.07:178][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.29-11.45.07:548][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.29-11.45.07:582][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.29-11.45.07:583][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.29-11.45.07:583][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55670'.
[2025.08.29-11.45.07:590][  0]LogUdpMessaging: Display: Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
[2025.08.29-11.45.07:590][  0]LogUdpMessaging: Display: Added local interface '172.17.96.1' to multicast group '230.0.0.1:6666'
[2025.08.29-11.45.07:594][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.29-11.45.07:857][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-11.45.07:857][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.07:957][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT: Interface availability:
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   RDG: no
[2025.08.29-11.45.07:969][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.29-11.45.08:074][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-11.45.08:074][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-11.45.08:342][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.29-11.45.08:342][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.29-11.45.08:360][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.29-11.45.08:399][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.29-11.45.08:420][  0]LogMLAdapter: Warning: Neural network asset data not set
[2025.08.29-11.45.08:424][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 8D02C3086CE146548000000000004600 | Instance: 9275508040C07B99384099A492C0D74E (TKT-28336).
[2025.08.29-11.45.08:481][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.29-11.45.08:481][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.29-11.45.08:487][  0]LogTimingProfiler: Initialize
[2025.08.29-11.45.08:487][  0]LogTimingProfiler: OnSessionChanged
[2025.08.29-11.45.08:487][  0]LoadingProfiler: Initialize
[2025.08.29-11.45.08:487][  0]LoadingProfiler: OnSessionChanged
[2025.08.29-11.45.08:487][  0]LogNetworkingProfiler: Initialize
[2025.08.29-11.45.08:487][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.29-11.45.08:487][  0]LogMemoryProfiler: Initialize
[2025.08.29-11.45.08:487][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.29-11.45.08:522][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.29-11.45.08:817][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.29-11.45.08:817][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.29-11.45.08:817][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.29-11.45.08:817][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.29-11.45.08:822][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.45.08:823][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.45.08:833][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.29-11.45.08:881][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.29-11.45.08:883][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-11.45.08:883][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-11.45.08:942][  0]LogCollectionManager: Loaded 0 collections in 0.001435 seconds
[2025.08.29-11.45.08:945][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.00s
[2025.08.29-11.45.08:948][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.29-11.45.08:951][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.00s
[2025.08.29-11.45.08:993][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.29-11.45.09:006][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.29-11.45.09:014][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.29-11.45.09:014][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.29-11.45.09:014][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.29-11.45.09:014][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.29-11.45.09:039][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.29-11.45.09:039][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.29-11.45.09:039][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.29-11.45.09:039][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.29-11.45.09:056][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-08-29T11:45:09.056Z using C
[2025.08.29-11.45.09:057][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.26100.4768.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=AURACRON, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.08.29-11.45.09:057][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.29-11.45.09:057][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.08.29-11.45.09:064][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.08.29-11.45.09:064][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.08.29-11.45.09:064][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.08.29-11.45.09:064][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000108
[2025.08.29-11.45.09:142][  0]LogUObjectArray: 45950 objects as part of root set at end of initial load.
[2025.08.29-11.45.09:142][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.29-11.45.09:300][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.45.09:300][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.45.09:300][  0]LogAutomationTest: Error: Condition failed
[2025.08.29-11.45.09:300][  0]LogEngine: Initializing Engine...
[2025.08.29-11.45.09:436][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.29-11.45.09:436][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.29-11.45.09:436][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.29-11.45.09:437][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.29-11.45.09:437][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.29-11.45.09:442][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.29-11.45.09:756][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.29-11.45.09:782][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.29-11.45.09:805][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.29-11.45.09:826][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.08.29-11.45.09:826][  0]LogInit: Texture streaming: Enabled
[2025.08.29-11.45.09:846][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.29-11.45.09:853][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.29-11.45.09:867][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.29-11.45.09:868][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.29-11.45.09:870][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.29-11.45.09:870][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.29-11.45.09:870][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.29-11.45.09:870][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.29-11.45.09:870][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.29-11.45.09:870][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.29-11.45.09:870][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.29-11.45.09:883][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.29-11.45.10:580][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.29-11.45.10:581][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.29-11.45.10:585][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.29-11.45.10:585][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.29-11.45.10:587][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.29-11.45.10:587][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.29-11.45.10:592][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.29-11.45.10:593][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.29-11.45.10:593][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.29-11.45.10:594][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.29-11.45.10:594][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.29-11.45.10:608][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.29-11.45.10:616][  0]LogInit: Undo buffer set to 256 MB
[2025.08.29-11.45.10:616][  0]LogInit: Transaction tracking system initialized
[2025.08.29-11.45.10:632][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/AURACRON/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.29-11.45.10:693][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.29-11.45.10:973][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/' took 0.05s
[2025.08.29-11.45.11:032][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.29-11.45.11:059][  0]LogMLAdapter: Creating MLAdapter manager of class MLAdapterManager
[2025.08.29-11.45.11:059][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.29-11.45.11:060][  0]LogPython: Using Python 3.11.8
[2025.08.29-11.45.11:118][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.29-11.45.12:060][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.29-11.45.12:129][  0]LogEditorDataStorage: Initializing
[2025.08.29-11.45.12:134][  0]LogEditorDataStorage: Initialized
[2025.08.29-11.45.12:140][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.29-11.45.12:144][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.29-11.45.12:144][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.29-11.45.12:282][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.29-11.45.12:286][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-11.45.12:286][  0]LogUnrealEdMisc: Loading editor; pre map load, took 11.263
[2025.08.29-11.45.12:288][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.29-11.45.12:290][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.29-11.45.12:292][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.29-11.45.12:322][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.29-11.45.12:324][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.08.29-11.45.12:340][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.29-11.45.12:340][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.29-11.45.12:343][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.29-11.45.12:344][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.29-11.45.12:344][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.29-11.45.12:546][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.45.12:558][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.45.12:565][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.45.12:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.29-11.45.12:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.29-11.45.12:572][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-11.45.12:581][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.29-11.45.12:773][  0]LogWorldPartition: Display: WorldPartition initialize took 429.956 ms
[2025.08.29-11.45.13:013][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.29-11.45.13:043][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.08.29-11.45.13:043][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.29-11.45.13:045][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 1,94ms para ser concluída.
[2025.08.29-11.45.13:060][  0]LogUnrealEdMisc: Total Editor Startup Time, took 12.036
[2025.08.29-11.45.13:192][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.29-11.45.13:252][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.29-11.45.13:584][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.45.13:826][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.45.13:890][  0]LogAssetRegistry: Display: Asset registry cache written as 74.0 MiB to ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin
[2025.08.29-11.45.14:091][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.45.14:310][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.29-11.45.14:613][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:614][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.29-11.45.14:616][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:617][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.29-11.45.14:618][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:619][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.29-11.45.14:619][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:620][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.29-11.45.14:621][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:622][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.29-11.45.14:623][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:624][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.29-11.45.14:624][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:625][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.29-11.45.14:626][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:629][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.29-11.45.14:629][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:629][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.29-11.45.14:629][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.29-11.45.14:631][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.29-11.45.15:553][  0]LogSlate: Took 0.000463 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.29-11.45.15:558][  0]LogSlate: Took 0.000207 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.29-11.45.15:614][  0]LogSlate: Took 0.000350 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.29-11.45.15:750][  0]LogStall: Startup...
[2025.08.29-11.45.15:754][  0]LogStall: Startup complete.
[2025.08.29-11.45.15:759][  0]LogLoad: (Engine Initialization) Total time: 14.74 seconds
[2025.08.29-11.45.16:784][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.29-11.45.16:784][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.29-11.45.16:846][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.29-11.45.16:848][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.29-11.45.16:903][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.29-11.45.16:916][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 67.959 ms
[2025.08.29-11.45.17:238][  1]LogAssetRegistry: AssetRegistryGather time 0.1924s: AssetDataDiscovery 0.0168s, AssetDataGather 0.0618s, StoreResults 0.1138s. Wall time 11.7660s.
	NumCachedDirectories 1647. NumUncachedDirectories 23. NumCachedFiles 7994. NumUncachedFiles 4.
	BackgroundTickInterruptions 2.
[2025.08.29-11.45.17:267][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.29-11.45.17:276][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.29-11.45.17:276][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.29-11.45.17:521][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 8.441351
[2025.08.29-11.45.17:522][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.08.29-11.45.17:524][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8.456198
[2025.08.29-11.45.17:539][  4]LogSourceControl: Uncontrolled asset discovery finished in 0.262157 seconds (Found 7970 uncontrolled assets)
[2025.08.29-11.45.18:847][  7]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.29-11.45.19:513][  9]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 10.115839
[2025.08.29-11.45.19:515][  9]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 1782979643
[2025.08.29-11.45.19:515][  9]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10.115839, Update Interval: 354.559753
[2025.08.29-11.46.07:711][154]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.29-11.46.07:711][154]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.29-11.46.07:711][154]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.29-11.46.07:812][154]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.29-11.46.07:812][154]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.29-11.46.07:812][154]LogTemp: Display: MCPServerRunnable: Received: {"type": "create_multilayer_map", "params": {"map_name": "AURACRON_Revolutionary_Map", "layers": [{"name": "Planicie_Radiante", "height_offset": 1000, "layer_type": "ground", "streaming_distance": 5000, "description": "Camada principal inspirada no Summoner's Rift com tr\u00eas lanes, jungle e rio central"}, {"name": "Firmamento_Zephyr", "height_offset": 3000, "layer_type": "aerial", "streaming_distance": 4000, "description": "Camada a\u00e9rea com plataformas flutuantes e estruturas celestiais"}, {"name": "Abismo_Umbral", "height_offset": -1000, "layer_type": "underground", "streaming_distance": 3000, "description": "Camada subterr\u00e2nea com cavernas e t\u00faneis sombrios"}], "world_partition_settings": {"enable_streaming": true, "cell_size": 12800, "loading_range": 25600, "enable_hlod": true}}}
[2025.08.29-11.46.07:812][154]LogTemp: Display: UnrealMCPBridge: Executing command: create_multilayer_map
[2025.08.29-11.46.07:870][154]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/Maps/AURACRON_Revolutionary_Map'
[2025.08.29-11.46.07:870][154]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/Maps/AURACRON_Revolutionary_Map', Physical: '../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.uasset'
[2025.08.29-11.46.07:870][154]LogChaosDD: Creating Chaos Debug Draw Scene for world AURACRON_Revolutionary_Map
[2025.08.29-11.46.07:872][154]LogTemp: AURACRON MEMORY FIX: World configured WITHOUT RF_Standalone to allow garbage collection
[2025.08.29-11.46.07:874][154]LogStreaming: Display: FlushAsyncLoading(477): 1 QueuedPackages, 0 AsyncPackages
[2025.08.29-11.46.07:875][154]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: WaitingForIo) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.46.07:875][154]LogStreaming: Display: Package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced is adding a dynamic import to package /Engine/Maps/Templates/HLODs/HLODLayer_Merged because of a recursive sync load
[2025.08.29-11.46.07:875][154]LogStreaming: Display: Flushing package /Engine/Maps/Templates/HLODs/HLODLayer_Merged (state: DeferredPostLoad) recursively from another package /Engine/Maps/Templates/HLODs/HLODLayer_Instanced (state: PreloadLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-11.46.07:876][154]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.29-11.46.07:876][154]LogWorldPartition: UWorldPartition::Initialize : World = /Game/Maps/AURACRON_Revolutionary_Map.AURACRON_Revolutionary_Map, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.29-11.46.07:876][154]LogWorldPartition: Display: WorldPartition initialize took 313 us (total: 430.269 ms)
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] WorldPartition initialized successfully
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] WorldPartition streaming enabled
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] Configuring level for Data Layers support
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] WorldPartition found - External Objects should be automatically enabled
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] External Objects configuration attempted
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] External Objects support verified through WorldPartition
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] External Objects check via PersistentLevel: Not Supported
[2025.08.29-11.46.07:876][154]LogTemp: Warning: [MapSystem] Attempting to force enable External Actors...
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] External Objects confirmed - proceeding with Data Layer creation
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] Creating Data Layer: Planicie_Radiante (Index: 0)
[2025.08.29-11.46.07:876][154]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante
[2025.08.29-11.46.07:876][154]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante'
[2025.08.29-11.46.07:876][154]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.46.07:877][154]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.46.07:885][154]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante
[2025.08.29-11.46.07:885][154]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_PlaniC65282DA496F58AFBAFA1C925D6A7C0C.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Planicie_Radiante.uasset'
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Planicie_Radiante
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Planicie_Radiante
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Planicie_Radiante
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Planicie_Radiante (Height: 1000.0, Type: ground, State: Activated)
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] Creating Data Layer: Firmamento_Zephyr (Index: 1)
[2025.08.29-11.46.07:887][154]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr
[2025.08.29-11.46.07:887][154]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr'
[2025.08.29-11.46.07:887][154]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr.uasset'
[2025.08.29-11.46.07:887][154]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr.uasset'
[2025.08.29-11.46.07:896][154]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr
[2025.08.29-11.46.07:897][154]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_FirmaC04495F04FCE9197B7CDF2AA5322BBCD.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Firmamento_Zephyr.uasset'
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Firmamento_Zephyr
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Firmamento_Zephyr
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Firmamento_Zephyr
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Firmamento_Zephyr (Height: 3000.0, Type: aerial, State: Loaded)
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] Creating Data Layer: Abismo_Umbral (Index: 2)
[2025.08.29-11.46.07:898][154]LogTemp: [MapSystem] Creating DataLayer asset at virtual path: /Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral
[2025.08.29-11.46.07:898][154]LogTemp: AURACRON PACKAGE FIX: Creating package with name: '/Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral'
[2025.08.29-11.46.07:898][154]LogTemp: AURACRON PACKAGE FIX: Package created - Virtual: '/Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral', Physical: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.46.07:898][154]LogTemp: AURACRON FILENAME FIX: Standard filename: '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.46.07:917][154]LogSavePackage: Moving output files for package: /Game/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral
[2025.08.29-11.46.07:917][154]LogSavePackage: Moving '../../../../../../Game/AURACRON/Saved/AURACRON_Revolutionary_Map_Abism858C70D843DB9367B550E6A2E2377C90.tmp' to '../../../../../../Game/AURACRON/Content/DataLayers/AURACRON_Revolutionary_Map_Abismo_Umbral.uasset'
[2025.08.29-11.46.07:918][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerAsset saved successfully for: Abismo_Umbral
[2025.08.29-11.46.07:919][154]LogTemp: [MapSystem] CRITICAL FIX: DataLayerInstance properly registered and found in WorldDataLayers for: Abismo_Umbral
[2025.08.29-11.46.07:919][154]LogTemp: [MapSystem] DataLayerInstance created and validated successfully for: Abismo_Umbral
[2025.08.29-11.46.07:919][154]LogTemp: [MapSystem] MODERN DataLayer created SAFELY: Abismo_Umbral (Height: -1000.0, Type: underground, State: Loaded)
[2025.08.29-11.46.07:919][154]LogTemp: [MapSystem] WorldPartition streaming configured via subsystem
[2025.08.29-11.46.07:919][154]LogTemp: [MapSystem] World Partition configured SAFELY with 3 Data Layers (Created: 3, Valid Instances: 3)
[2025.08.29-11.46.07:919][154]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Planicie_Radiante - SKIPPING to prevent assertion failure
[2025.08.29-11.46.07:919][154]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Firmamento_Zephyr - SKIPPING to prevent assertion failure
[2025.08.29-11.46.07:919][154]LogTemp: Error: SpawnInitialLayerActors: DataLayerInstance not registered in DataLayerManager for layer: Abismo_Umbral - SKIPPING to prevent assertion failure
[2025.08.29-11.46.07:919][154]LogTemp: SpawnInitialLayerActors: Total actors spawned: 0
[2025.08.29-11.46.07:919][154]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.08.29-11.46.07:986][154]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/AURACRON_Revolutionary_Map" FILE="../../../../../../Game/AURACRON/Content/Maps/AURACRON_Revolutionary_Map.umap" SILENT=true AUTOSAVING=false KEEPDIRTY=false
[2025.08.29-11.46.08:008][154]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.08.29-11.46.08:167][154]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.158 s
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: === Handled ensure: ===
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: 
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: Ensure condition failed: !bInitialized  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\Engine\Private\Subsystems\WorldSubsystem.cpp] [Line: 115] 
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: Tickable subsystem WorldPartitionSubsystem None.None:None was destroyed while still initialized! Check for missing Super call
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: Stack: 
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4b5f370e UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4aa63144 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff56083aa2 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff55fa821c UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff55e97e5d UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff55f324e8 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff55ed55c9 UnrealEditor-CoreUObject.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff467598fc UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46768ecf UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46769d16 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff467bc8ef UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff467bb97d UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e55c876 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4b161574 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff473e8152 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c4f585 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c4b918 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c2ccdc UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c2b197 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c2a37e UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46c4c384 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff472b45ff UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ffedb9e032c UnrealEditor-UnrealMCP.dll!FUnrealMCPMapCommands::HandleCreateMultilayerMap() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMapCommands.cpp:812]
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ffedb9cf7db UnrealEditor-UnrealMCP.dll!FUnrealMCPMapCommands::HandleCommand() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMapCommands.cpp:72]
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ffedb9136ec UnrealEditor-UnrealMCP.dll!`UUnrealMCPBridge::ExecuteCommand'::`2'::<lambda_1>::operator()() [C:\Game\AURACRON\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp:319]
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e14cd83 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e16fb72 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e16284f UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e162ece UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e751aa4 UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4e7535bf UnrealEditor-Core.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ffeee580b86 UnrealEditor-MassEntityEditor.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff461c37cb UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff4abf2c25 UnrealEditor-Engine.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff46771ff7 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fff47445956 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff623929ce4 UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff62394e5ac UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff62394e6ba UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff62395209e UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff623964e44 UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007ff6239680fa UnrealEditor.exe!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fffec69e8d7 KERNEL32.DLL!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: [Callstack] 0x00007fffee5bc34c ntdll.dll!UnknownFunction []
[2025.08.29-11.46.08:167][154]LogOutputDevice: Error: 

#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Core Engine APIs - UE 5.6
#include "Engine/World.h"
#include "Engine/Level.h"
#include "Engine/LevelStreaming.h"
#include "Engine/LevelStreamingDynamic.h"
#include "GameFramework/WorldSettings.h"

// Editor APIs - UE 5.6 Modern
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Components - UE 5.6 Enhanced
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"

// Lighting - UE 5.6 Lumen Integration
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"

// World Partition and Data Layer APIs - UE 5.6.1 Modern
#include "UObject/WeakObjectPtr.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerInstanceWithAsset.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"

// Thread safety macros are now imported from UnrealMCPCommonUtils.h

#define AURACRON_VALIDATE_OBJECT_SAFE(Object, ObjectName) \
    if (!Object || !IsValid(Object) || Object->IsUnreachable()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: %s is null, invalid, or unreachable in %s"), TEXT(#ObjectName), ANSI_TO_TCHAR(__FUNCTION__)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("%s is null, invalid, or unreachable"), TEXT(#ObjectName))); \
    }

#define AURACRON_VALIDATE_WEAK_PTR(WeakPtr, ObjectName) \
    if (!WeakPtr.IsValid() || WeakPtr.IsStale()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: %s WeakPtr is invalid or stale in %s"), TEXT(#ObjectName), ANSI_TO_TCHAR(__FUNCTION__)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("%s WeakPtr is invalid or stale"), TEXT(#ObjectName))); \
    }

#define AURACRON_SAFE_EXECUTE(Expression, ErrorMessage) \
    try \
    { \
        Expression; \
    } \
    catch (...) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception in %s: %s"), ANSI_TO_TCHAR(__FUNCTION__), TEXT(ErrorMessage)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Exception: %s"), TEXT(ErrorMessage))); \
    }

#define AURACRON_VALIDATE_DATA_LAYER_INSTANCE(DataLayerInstance, LayerName) \
    if (!DataLayerInstance || !IsValid(DataLayerInstance) || DataLayerInstance->IsUnreachable()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerInstance for %s is null, invalid, or unreachable in %s"), *LayerName, ANSI_TO_TCHAR(__FUNCTION__)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("DataLayerInstance for %s is null, invalid, or unreachable"), *LayerName)); \
    }

#define AURACRON_VALIDATE_EXTERNAL_OBJECTS_SUPPORT(Level) \
    if (!Level || !IsValid(Level) || !Level->IsUsingExternalObjects()) \
    { \
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Level does not support External Objects (required for Data Layers) in %s"), ANSI_TO_TCHAR(__FUNCTION__)); \
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Level does not support External Objects - Data Layers cannot be created")); \
    }

/**
 * Handler class for Map System-related MCP commands
 * 
 * Implements PRODUCTION READY tools for creating multilayer MOBA maps
 * with advanced World Partition, Level Streaming, and Landscape integration.
 * 
 * Features:
 * - 3-layer vertical map system (Planície Radiante, Firmamento Zephyr, Abismo Umbral)
 * - Portal system for inter-layer transitions
 * - Elevator system for team movement
 * - Dimensional bridges for temporary connections
 * - Advanced lighting per layer
 * - Collision boundaries between layers
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6 APIs only
 */
class UNREALMCP_API FUnrealMCPMapCommands
{
public:
    FUnrealMCPMapCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // CORE MAP CREATION COMMANDS
    // ========================================

    /**
     * Creates a multilayer map with 3 vertical layers for MOBA gameplay
     * 
     * Parameters:
     * - map_name (string): Name of the map to create
     * - size_x (int): Map width in Unreal units
     * - size_y (int): Map depth in Unreal units
     * - layer_heights (array): Heights for each layer [0-2000, 2000-4000, 4000-6000]
     * 
     * Implementation:
     * - Uses World Partition for large world management
     * - Creates separate streaming levels for each layer
     * - Sets up proper Z-coordinate boundaries
     * - Configures runtime spatial hash for optimal streaming
     * 
     * @param Params JSON parameters
     * @return JSON response with created map details
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerMap(const TSharedPtr<FJsonObject>& Params);

    /**
     * Configures layer-specific properties (lighting, physics, materials)
     * 
     * Parameters:
     * - layer_name (string): Target layer name
     * - lighting_settings (object): Lighting configuration
     * - physics_settings (object): Physics properties
     * - material_overrides (object): Material settings
     * 
     * Implementation:
     * - Modifies WorldSettings for each layer
     * - Configures Post Process Volumes
     * - Sets up layer-specific collision profiles
     * - Applies Lumen lighting settings
     * 
     * @param Params JSON parameters
     * @return JSON response with configuration results
     */
    TSharedPtr<FJsonObject> HandleConfigureLayerProperties(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // NAVIGATION SYSTEM COMMANDS
    // ========================================

    /**
     * Creates portal system for transitions between layers
     * 
     * Parameters:
     * - portal_locations (array): Array of portal positions
     * - channel_time (float): Time to activate portal (default: 3.0s)
     * - cooldown_time (float): Personal cooldown (default: 30.0s)
     * - interruption_enabled (bool): Can be interrupted by damage
     * 
     * Implementation:
     * - Creates custom Portal Actor with teleportation logic
     * - Implements channel system with progress bar
     * - Sets up damage interruption mechanics
     * - Configures visual effects and audio cues
     * 
     * @param Params JSON parameters
     * @return JSON response with portal creation results
     */
    TSharedPtr<FJsonObject> HandleCreatePortalSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Creates elevator system for team movement between layers
     * 
     * Parameters:
     * - elevator_locations (array): Elevator positions
     * - capacity (int): Maximum players (default: 5)
     * - travel_time (float): Time between layers (default: 5.0s)
     * - vulnerability_enabled (bool): Can be attacked during transport
     * 
     * Implementation:
     * - Creates Elevator Actor with Movement Component
     * - Implements team capacity management
     * - Sets up vulnerability system during transport
     * - Configures smooth movement between layers
     * 
     * @param Params JSON parameters
     * @return JSON response with elevator creation results
     */
    TSharedPtr<FJsonObject> HandleCreateElevatorSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * Creates dimensional bridges for temporary layer connections
     * 
     * Parameters:
     * - bridge_points (array): Start and end points for bridges
     * - duration (float): Bridge lifetime (90-120 seconds)
     * - activation_trigger (string): What activates the bridge
     * - capacity (string): "unlimited" or specific number
     * 
     * Implementation:
     * - Uses Spline Component for bridge geometry
     * - Implements timer system for temporary existence
     * - Creates trigger system for activation
     * - Sets up visual effects for bridge appearance/disappearance
     * 
     * @param Params JSON parameters
     * @return JSON response with bridge creation results
     */
    TSharedPtr<FJsonObject> HandleCreateDimensionalBridge(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // ENVIRONMENT SETUP COMMANDS
    // ========================================

    /**
     * Sets up layer-specific lighting systems
     * 
     * Parameters:
     * - layer_name (string): Target layer
     * - light_type (string): "directional", "sky", "point", "spot"
     * - intensity (float): Light intensity
     * - color (array): RGB color values [0-1]
     * - lumen_settings (object): Lumen-specific configuration
     * 
     * Implementation:
     * - Creates appropriate Light Components
     * - Configures Lumen Global Illumination
     * - Sets up Post Process Volumes per layer
     * - Implements dynamic lighting for different layer themes
     * 
     * @param Params JSON parameters
     * @return JSON response with lighting setup results
     */
    TSharedPtr<FJsonObject> HandleSetupLayerLighting(const TSharedPtr<FJsonObject>& Params);

    /**
     * Creates collision boundaries and limits between layers
     * 
     * Parameters:
     * - layer_name (string): Target layer
     * - boundary_type (string): "hard", "soft", "invisible"
     * - collision_settings (object): Collision configuration
     * - visual_indicators (bool): Show boundary visually
     * 
     * Implementation:
     * - Creates collision volumes using Chaos Physics
     * - Sets up proper collision profiles
     * - Implements boundary enforcement logic
     * - Configures visual feedback for boundaries
     * 
     * @param Params JSON parameters
     * @return JSON response with boundary creation results
     */
    TSharedPtr<FJsonObject> HandleCreateLayerBoundaries(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName,
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // ACTOR SPAWNING FUNCTIONS (PRODUCTION READY)
    // ========================================

    /**
     * Spawns initial actors for each layer after map creation
     * @param World Target world
     * @param LayerNames Array of layer names
     * @param LayerHeights Array of layer heights
     * @param LayerTypes Array of layer types
     * @param DataLayerInstances Array of created data layer instances
     * @return Number of actors spawned
     */
    int32 SpawnInitialLayerActors(UWorld* World, const TArray<FString>& LayerNames,
                                 const TArray<float>& LayerHeights, const TArray<FString>& LayerTypes,
                                 const TArray<TWeakObjectPtr<UDataLayerInstance>>& DataLayerInstances);

    /**
     * Spawns actors for specific layer type
     * @param World Target world
     * @param LayerName Layer name
     * @param LayerHeight Layer height
     * @param LayerType Layer type (ground, aerial, underground)
     * @param DataLayer Data layer instance
     * @return Number of actors spawned
     */
    int32 SpawnActorsForLayerType(UWorld* World, const FString& LayerName,
                                 float LayerHeight, const FString& LayerType, UDataLayerInstance* DataLayer);

    /**
     * Spawns actors for Planície Radiante (Ground Layer)
     * @param World Target world
     * @param BaseLocation Base spawn location
     * @param DataLayer Data layer instance
     * @return Number of actors spawned
     */
    int32 SpawnGroundLayerActors(UWorld* World, const FVector& BaseLocation, UDataLayerInstance* DataLayer);

    /**
     * Spawns actors for Firmamento Zephyr (Aerial Layer)
     * @param World Target world
     * @param BaseLocation Base spawn location
     * @param DataLayer Data layer instance
     * @return Number of actors spawned
     */
    int32 SpawnAerialLayerActors(UWorld* World, const FVector& BaseLocation, UDataLayerInstance* DataLayer);

    /**
     * Spawns actors for Abismo Umbral (Underground Layer)
     * @param World Target world
     * @param BaseLocation Base spawn location
     * @param DataLayer Data layer instance
     * @return Number of actors spawned
     */
    int32 SpawnUndergroundLayerActors(UWorld* World, const FVector& BaseLocation, UDataLayerInstance* DataLayer);
};
